<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:35 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>常量字段值</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u5E38\u91CF\u5B57\u6BB5\u503C";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="overview-tree.html">树</a></li>
<li><a href="deprecated-list.html">已过时</a></li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="index.html?constant-values.html" target="_top">框架</a></li>
<li><a href="constant-values.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="常量字段值" class="title">常量字段值</h1>
<h2 title="目录">目录</h2>
<ul>
<li><a href="#com.huazheng">com.huazheng.*</a></li>
</ul>
</div>
<div class="constantValuesContainer"><a name="com.huazheng">
<!--   -->
</a>
<h2 title="com.huazheng">com.huazheng.*</h2>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.huazheng.tunny.tools.base.<a href="com/huazheng/tunny/tools/base/Platforms.html" title="com.huazheng.tunny.tools.base中的类">Platforms</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.huazheng.tunny.tools.base.Platforms.LINUX_FILE_PATH_SEPARATOR_CHAR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;char</code></td>
<td><code><a href="com/huazheng/tunny/tools/base/Platforms.html#LINUX_FILE_PATH_SEPARATOR_CHAR">LINUX_FILE_PATH_SEPARATOR_CHAR</a></code></td>
<td class="colLast"><code>47</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.huazheng.tunny.tools.base.Platforms.WINDOWS_FILE_PATH_SEPARATOR_CHAR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;char</code></td>
<td><code><a href="com/huazheng/tunny/tools/base/Platforms.html#WINDOWS_FILE_PATH_SEPARATOR_CHAR">WINDOWS_FILE_PATH_SEPARATOR_CHAR</a></code></td>
<td class="colLast"><code>92</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.huazheng.tunny.tools.collection.<a href="com/huazheng/tunny/tools/collection/MapUtil.html" title="com.huazheng.tunny.tools.collection中的类">MapUtil</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.huazheng.tunny.tools.collection.MapUtil.DEFAULT_LOAD_FACTOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;float</code></td>
<td><code><a href="com/huazheng/tunny/tools/collection/MapUtil.html#DEFAULT_LOAD_FACTOR">DEFAULT_LOAD_FACTOR</a></code></td>
<td class="colLast"><code>0.75f</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.huazheng.tunny.tools.collection.type.primitive.<a href="com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">IntObjectHashMap</a>&lt;<a href="com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html" title="IntObjectHashMap中的类型参数">V</a>&gt;</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.huazheng.tunny.tools.collection.type.primitive.IntObjectHashMap.DEFAULT_CAPACITY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html#DEFAULT_CAPACITY">DEFAULT_CAPACITY</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.huazheng.tunny.tools.collection.type.primitive.IntObjectHashMap.DEFAULT_LOAD_FACTOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;float</code></td>
<td><code><a href="com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html#DEFAULT_LOAD_FACTOR">DEFAULT_LOAD_FACTOR</a></code></td>
<td class="colLast"><code>0.5f</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.huazheng.tunny.tools.collection.type.primitive.<a href="com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">LongObjectHashMap</a>&lt;<a href="com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&gt;</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.huazheng.tunny.tools.collection.type.primitive.LongObjectHashMap.DEFAULT_CAPACITY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#DEFAULT_CAPACITY">DEFAULT_CAPACITY</a></code></td>
<td class="colLast"><code>8</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.huazheng.tunny.tools.collection.type.primitive.LongObjectHashMap.DEFAULT_LOAD_FACTOR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;float</code></td>
<td><code><a href="com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#DEFAULT_LOAD_FACTOR">DEFAULT_LOAD_FACTOR</a></code></td>
<td class="colLast"><code>0.5f</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.huazheng.tunny.tools.net.<a href="com/huazheng/tunny/tools/net/NetUtil.html" title="com.huazheng.tunny.tools.net中的类">NetUtil</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.huazheng.tunny.tools.net.NetUtil.PORT_RANGE_MAX">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/huazheng/tunny/tools/net/NetUtil.html#PORT_RANGE_MAX">PORT_RANGE_MAX</a></code></td>
<td class="colLast"><code>65535</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.huazheng.tunny.tools.net.NetUtil.PORT_RANGE_MIN">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/huazheng/tunny/tools/net/NetUtil.html#PORT_RANGE_MIN">PORT_RANGE_MIN</a></code></td>
<td class="colLast"><code>1024</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.huazheng.tunny.tools.text.<a href="com/huazheng/tunny/tools/text/CsvUtil.html" title="com.huazheng.tunny.tools.text中的类">CsvUtil</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.huazheng.tunny.tools.text.CsvUtil.DOUBLE_QUOTE">
<!--   -->
</a><code>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/huazheng/tunny/tools/text/CsvUtil.html#DOUBLE_QUOTE">DOUBLE_QUOTE</a></code></td>
<td class="colLast"><code>"\"\""</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.huazheng.tunny.tools.text.CsvUtil.FIELD_QUOTE">
<!--   -->
</a><code>protected&nbsp;static&nbsp;final&nbsp;char</code></td>
<td><code><a href="com/huazheng/tunny/tools/text/CsvUtil.html#FIELD_QUOTE">FIELD_QUOTE</a></code></td>
<td class="colLast"><code>34</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.huazheng.tunny.tools.text.CsvUtil.FIELD_SEPARATOR">
<!--   -->
</a><code>protected&nbsp;static&nbsp;final&nbsp;char</code></td>
<td><code><a href="com/huazheng/tunny/tools/text/CsvUtil.html#FIELD_SEPARATOR">FIELD_SEPARATOR</a></code></td>
<td class="colLast"><code>44</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.huazheng.tunny.tools.text.CsvUtil.QUOTE">
<!--   -->
</a><code>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/huazheng/tunny/tools/text/CsvUtil.html#QUOTE">QUOTE</a></code></td>
<td class="colLast"><code>"\""</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.huazheng.tunny.tools.text.CsvUtil.SPACE">
<!--   -->
</a><code>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/huazheng/tunny/tools/text/CsvUtil.html#SPACE">SPACE</a></code></td>
<td class="colLast"><code>" "</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.huazheng.tunny.tools.text.CsvUtil.SPECIAL_CHARS">
<!--   -->
</a><code>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/huazheng/tunny/tools/text/CsvUtil.html#SPECIAL_CHARS">SPECIAL_CHARS</a></code></td>
<td class="colLast"><code>"\r\n"</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.huazheng.tunny.tools.text.<a href="com/huazheng/tunny/tools/text/HashUtil.html" title="com.huazheng.tunny.tools.text中的类">HashUtil</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.huazheng.tunny.tools.text.HashUtil.MURMUR_SEED">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;int</code></td>
<td><code><a href="com/huazheng/tunny/tools/text/HashUtil.html#MURMUR_SEED">MURMUR_SEED</a></code></td>
<td class="colLast"><code>1318007700</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.huazheng.tunny.tools.text.<a href="com/huazheng/tunny/tools/text/TextValidator.html" title="com.huazheng.tunny.tools.text中的类">TextValidator</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.huazheng.tunny.tools.text.TextValidator.REGEX_MOBILE_EXACT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/huazheng/tunny/tools/text/TextValidator.html#REGEX_MOBILE_EXACT">REGEX_MOBILE_EXACT</a></code></td>
<td class="colLast"><code>"^((13[0-9])|(14[5,7])|(15[0-3,5-9])|(16[6])|(17[0,1,3,5-8])|(18[0-9])|(19[8,9]))\\d{8}$"</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.huazheng.tunny.tools.text.<a href="com/huazheng/tunny/tools/text/WildcardMatcher.html" title="com.huazheng.tunny.tools.text中的类">WildcardMatcher</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.huazheng.tunny.tools.text.WildcardMatcher.PATH_MATCH">
<!--   -->
</a><code>protected&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/huazheng/tunny/tools/text/WildcardMatcher.html#PATH_MATCH">PATH_MATCH</a></code></td>
<td class="colLast"><code>"**"</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
<ul class="blockList">
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.huazheng.tunny.tools.time.<a href="com/huazheng/tunny/tools/time/DateFormatUtil.html" title="com.huazheng.tunny.tools.time中的类">DateFormatUtil</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.huazheng.tunny.tools.time.DateFormatUtil.PATTERN_DEFAULT">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/huazheng/tunny/tools/time/DateFormatUtil.html#PATTERN_DEFAULT">PATTERN_DEFAULT</a></code></td>
<td class="colLast"><code>"yyyy-MM-dd HH:mm:ss.SSS"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.huazheng.tunny.tools.time.DateFormatUtil.PATTERN_DEFAULT_ON_SECOND">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/huazheng/tunny/tools/time/DateFormatUtil.html#PATTERN_DEFAULT_ON_SECOND">PATTERN_DEFAULT_ON_SECOND</a></code></td>
<td class="colLast"><code>"yyyy-MM-dd HH:mm:ss"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.huazheng.tunny.tools.time.DateFormatUtil.PATTERN_ISO">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/huazheng/tunny/tools/time/DateFormatUtil.html#PATTERN_ISO">PATTERN_ISO</a></code></td>
<td class="colLast"><code>"yyyy-MM-dd\'T\'HH:mm:ss.SSSZZ"</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.huazheng.tunny.tools.time.DateFormatUtil.PATTERN_ISO_ON_DATE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/huazheng/tunny/tools/time/DateFormatUtil.html#PATTERN_ISO_ON_DATE">PATTERN_ISO_ON_DATE</a></code></td>
<td class="colLast"><code>"yyyy-MM-dd"</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.huazheng.tunny.tools.time.DateFormatUtil.PATTERN_ISO_ON_SECOND">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;java.lang.String</code></td>
<td><code><a href="com/huazheng/tunny/tools/time/DateFormatUtil.html#PATTERN_ISO_ON_SECOND">PATTERN_ISO_ON_SECOND</a></code></td>
<td class="colLast"><code>"yyyy-MM-dd\'T\'HH:mm:ssZZ"</code></td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="constantsSummary" border="0" cellpadding="3" cellspacing="0" summary="常量字段值表, 列表常量字段和值">
<caption><span>com.huazheng.tunny.tools.time.<a href="com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th scope="col">常量字段</th>
<th class="colLast" scope="col">值</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a name="com.huazheng.tunny.tools.time.DateUtil.MILLIS_PER_DAY">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;long</code></td>
<td><code><a href="com/huazheng/tunny/tools/time/DateUtil.html#MILLIS_PER_DAY">MILLIS_PER_DAY</a></code></td>
<td class="colLast"><code>86400000L</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.huazheng.tunny.tools.time.DateUtil.MILLIS_PER_HOUR">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;long</code></td>
<td><code><a href="com/huazheng/tunny/tools/time/DateUtil.html#MILLIS_PER_HOUR">MILLIS_PER_HOUR</a></code></td>
<td class="colLast"><code>3600000L</code></td>
</tr>
<tr class="altColor">
<td class="colFirst"><a name="com.huazheng.tunny.tools.time.DateUtil.MILLIS_PER_MINUTE">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;long</code></td>
<td><code><a href="com/huazheng/tunny/tools/time/DateUtil.html#MILLIS_PER_MINUTE">MILLIS_PER_MINUTE</a></code></td>
<td class="colLast"><code>60000L</code></td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a name="com.huazheng.tunny.tools.time.DateUtil.MILLIS_PER_SECOND">
<!--   -->
</a><code>public&nbsp;static&nbsp;final&nbsp;long</code></td>
<td><code><a href="com/huazheng/tunny/tools/time/DateUtil.html#MILLIS_PER_SECOND">MILLIS_PER_SECOND</a></code></td>
<td class="colLast"><code>1000L</code></td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="overview-tree.html">树</a></li>
<li><a href="deprecated-list.html">已过时</a></li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="index.html?constant-values.html" target="_top">框架</a></li>
<li><a href="constant-values.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
