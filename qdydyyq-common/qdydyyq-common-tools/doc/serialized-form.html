<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:35 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>序列化表格</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u5E8F\u5217\u5316\u8868\u683C";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="overview-tree.html">树</a></li>
<li><a href="deprecated-list.html">已过时</a></li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="index.html?serialized-form.html" target="_top">框架</a></li>
<li><a href="serialized-form.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="序列化表格" class="title">序列化表格</h1>
</div>
<div class="serializedFormContainer">
<ul class="blockList">
<li class="blockList">
<h2 title="程序包">程序包&nbsp;com.huazheng.tunny.tools.base</h2>
<ul class="blockList">
<li class="blockList"><a name="com.huazheng.tunny.tools.base.SystemPropertiesUtil.ListenableProperties">
<!--   -->
</a>
<h3>类<a href="com/huazheng/tunny/tools/base/SystemPropertiesUtil.ListenableProperties.html" title="com.huazheng.tunny.tools.base中的类">com.huazheng.tunny.tools.base.SystemPropertiesUtil.ListenableProperties</a>扩展java.util.Properties实现可序列化</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>-8282465702074684324L</dd>
</dl>
</li>
</ul>
</li>
<li class="blockList">
<h2 title="程序包">程序包&nbsp;com.huazheng.tunny.tools.base.type</h2>
<ul class="blockList">
<li class="blockList"><a name="com.huazheng.tunny.tools.base.type.CloneableException">
<!--   -->
</a>
<h3>类<a href="com/huazheng/tunny/tools/base/type/CloneableException.html" title="com.huazheng.tunny.tools.base.type中的类">com.huazheng.tunny.tools.base.type.CloneableException</a>扩展java.lang.Exception实现可序列化</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>-6270471689928560417L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>序列化字段</h3>
<ul class="blockList">
<li class="blockListLast">
<h4>message</h4>
<pre>java.lang.String message</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="com.huazheng.tunny.tools.base.type.CloneableRuntimeException">
<!--   -->
</a>
<h3>类<a href="com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html" title="com.huazheng.tunny.tools.base.type中的类">com.huazheng.tunny.tools.base.type.CloneableRuntimeException</a>扩展java.lang.RuntimeException实现可序列化</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>3984796576627959400L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>序列化字段</h3>
<ul class="blockList">
<li class="blockListLast">
<h4>message</h4>
<pre>java.lang.String message</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="com.huazheng.tunny.tools.base.type.UncheckedException">
<!--   -->
</a>
<h3>类<a href="com/huazheng/tunny/tools/base/type/UncheckedException.html" title="com.huazheng.tunny.tools.base.type中的类">com.huazheng.tunny.tools.base.type.UncheckedException</a>扩展java.lang.RuntimeException实现可序列化</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>4140223302171577501L</dd>
</dl>
</li>
</ul>
</li>
<li class="blockList">
<h2 title="程序包">程序包&nbsp;com.huazheng.tunny.tools.collection.type</h2>
<ul class="blockList">
<li class="blockList"><a name="com.huazheng.tunny.tools.collection.type.ConcurrentHashSet">
<!--   -->
</a>
<h3>类<a href="com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="com.huazheng.tunny.tools.collection.type中的类">com.huazheng.tunny.tools.collection.type.ConcurrentHashSet</a>扩展java.util.AbstractSet&lt;<a href="com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;实现可序列化</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>-8672117787651310382L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>序列化字段</h3>
<ul class="blockList">
<li class="blockListLast">
<h4>m</h4>
<pre>java.util.Map&lt;K,V&gt; m</pre>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="com.huazheng.tunny.tools.collection.type.SortedArrayList">
<!--   -->
</a>
<h3>类<a href="com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="com.huazheng.tunny.tools.collection.type中的类">com.huazheng.tunny.tools.collection.type.SortedArrayList</a>扩展java.util.ArrayList&lt;<a href="com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&gt;实现可序列化</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>-8301136559614447593L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>序列化字段</h3>
<ul class="blockList">
<li class="blockListLast">
<h4>comparator</h4>
<pre>java.util.Comparator&lt;T&gt; comparator</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList">
<h2 title="程序包">程序包&nbsp;com.huazheng.tunny.tools.concurrent.jsr166e</h2>
<ul class="blockList">
<li class="blockList"><a name="com.huazheng.tunny.tools.concurrent.jsr166e.LongAdder">
<!--   -->
</a>
<h3>类<a href="com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html" title="com.huazheng.tunny.tools.concurrent.jsr166e中的类">com.huazheng.tunny.tools.concurrent.jsr166e.LongAdder</a>扩展<a href="com/huazheng/tunny/tools/concurrent/jsr166e/Striped64.html" title="com.huazheng.tunny.tools.concurrent.jsr166e中的类">Striped64</a>实现可序列化</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>7249069246863182397L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>序列化方法</h3>
<ul class="blockList">
<li class="blockList">
<h4>readObject</h4>
<pre>private&nbsp;void&nbsp;readObject(java.io.ObjectInputStream&nbsp;s)
                 throws java.io.IOException,
                        java.lang.ClassNotFoundException</pre>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
<dd><code>java.lang.ClassNotFoundException</code></dd>
</dl>
</li>
<li class="blockListLast">
<h4>writeObject</h4>
<pre>private&nbsp;void&nbsp;writeObject(java.io.ObjectOutputStream&nbsp;s)
                  throws java.io.IOException</pre>
<dl>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
<li class="blockList"><a name="com.huazheng.tunny.tools.concurrent.jsr166e.Striped64">
<!--   -->
</a>
<h3>类<a href="com/huazheng/tunny/tools/concurrent/jsr166e/Striped64.html" title="com.huazheng.tunny.tools.concurrent.jsr166e中的类">com.huazheng.tunny.tools.concurrent.jsr166e.Striped64</a>扩展java.lang.Number实现可序列化</h3>
</li>
</ul>
</li>
<li class="blockList">
<h2 title="程序包">程序包&nbsp;com.huazheng.tunny.tools.concurrent.threadpool</h2>
<ul class="blockList">
<li class="blockList"><a name="com.huazheng.tunny.tools.concurrent.threadpool.QueuableCachedThreadPool.ControllableQueue">
<!--   -->
</a>
<h3>类<a href="com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.ControllableQueue.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">com.huazheng.tunny.tools.concurrent.threadpool.QueuableCachedThreadPool.ControllableQueue</a>扩展java.util.concurrent.LinkedBlockingQueue&lt;java.lang.Runnable&gt;实现可序列化</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>5044057462066661171L</dd>
</dl>
</li>
</ul>
</li>
<li class="blockList">
<h2 title="程序包">程序包&nbsp;com.huazheng.tunny.tools.io.type</h2>
<ul class="blockList">
<li class="blockList"><a name="com.huazheng.tunny.tools.io.type.StringBuilderWriter">
<!--   -->
</a>
<h3>类<a href="com/huazheng/tunny/tools/io/type/StringBuilderWriter.html" title="com.huazheng.tunny.tools.io.type中的类">com.huazheng.tunny.tools.io.type.StringBuilderWriter</a>扩展java.io.Writer实现可序列化</h3>
<dl class="nameValue">
<dt>serialVersionUID:</dt>
<dd>-146927496096066153L</dd>
</dl>
<ul class="blockList">
<li class="blockList">
<h3>序列化字段</h3>
<ul class="blockList">
<li class="blockListLast">
<h4>builder</h4>
<pre>java.lang.StringBuilder builder</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="overview-tree.html">树</a></li>
<li><a href="deprecated-list.html">已过时</a></li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="index.html?serialized-form.html" target="_top">框架</a></li>
<li><a href="serialized-form.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
