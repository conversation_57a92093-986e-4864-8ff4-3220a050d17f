<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:36 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>W - 索引</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="W - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-22.html">上一个字母</a></li>
<li><a href="index-24.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-23.html" target="_top">框架</a></li>
<li><a href="index-23.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a name="I:W">
<!--   -->
</a>
<h2 class="title">W</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtilTest.html#weakMap--">weakMap()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtilTest.html" title="com.huazheng.tunny.tools.collection中的类">MapUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/text/WildcardMatcher.html" title="com.huazheng.tunny.tools.text中的类"><span class="typeNameLink">WildcardMatcher</span></a> - <a href="../com/huazheng/tunny/tools/text/package-summary.html">com.huazheng.tunny.tools.text</a>中的类</dt>
<dd>
<div class="block">从Jodd移植，匹配以通配符比较字符串（比正则表达式简单），以及Ant Path风格如比较目录Path
 
 https://github.com/oblac/jodd/blob/master/jodd-core/src/main/java/jodd/util/Wildcard.java
 
 Checks whether a string or path matches a given wildcard pattern.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/WildcardMatcher.html#WildcardMatcher--">WildcardMatcher()</a></span> - 类 的构造器com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/WildcardMatcher.html" title="com.huazheng.tunny.tools.text中的类">WildcardMatcher</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/text/WildcardMatcherTest.html" title="com.huazheng.tunny.tools.text中的类"><span class="typeNameLink">WildcardMatcherTest</span></a> - <a href="../com/huazheng/tunny/tools/text/package-summary.html">com.huazheng.tunny.tools.text</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/WildcardMatcherTest.html#WildcardMatcherTest--">WildcardMatcherTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/WildcardMatcherTest.html" title="com.huazheng.tunny.tools.text中的类">WildcardMatcherTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/Platforms.html#WINDOWS_FILE_PATH_SEPARATOR_CHAR">WINDOWS_FILE_PATH_SEPARATOR_CHAR</a></span> - 类 中的静态变量com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/Platforms.html" title="com.huazheng.tunny.tools.base中的类">Platforms</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/Platforms.html#WORKING_DIR">WORKING_DIR</a></span> - 类 中的静态变量com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/Platforms.html" title="com.huazheng.tunny.tools.base中的类">Platforms</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtilTest.html#wrapException--">wrapException()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtilTest.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#write-java.lang.CharSequence-java.io.File-">write(CharSequence, File)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">简单写入String到File.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/IOUtil.html#write-java.lang.String-java.io.OutputStream-">write(String, OutputStream)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/IOUtil.html" title="com.huazheng.tunny.tools.io中的类">IOUtil</a></dt>
<dd>
<div class="block">简单写入String到OutputStream.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/IOUtil.html#write-java.lang.String-java.io.Writer-">write(String, Writer)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/IOUtil.html" title="com.huazheng.tunny.tools.io中的类">IOUtil</a></dt>
<dd>
<div class="block">简单写入String到Writer.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/IOUtilTest.html#write--">write()</a></span> - 类 中的方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/IOUtilTest.html" title="com.huazheng.tunny.tools.io中的类">IOUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html#write-java.lang.String-">write(String)</a></span> - 类 中的方法com.huazheng.tunny.tools.io.type.<a href="../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html" title="com.huazheng.tunny.tools.io.type中的类">StringBuilderWriter</a></dt>
<dd>
<div class="block">Writes a String to the <code>StringBuilder</code>.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html#write-char:A-int-int-">write(char[], int, int)</a></span> - 类 中的方法com.huazheng.tunny.tools.io.type.<a href="../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html" title="com.huazheng.tunny.tools.io.type中的类">StringBuilderWriter</a></dt>
<dd>
<div class="block">Writes a portion of a character array to the <code>StringBuilder</code>.</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-22.html">上一个字母</a></li>
<li><a href="index-24.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-23.html" target="_top">框架</a></li>
<li><a href="index-23.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
