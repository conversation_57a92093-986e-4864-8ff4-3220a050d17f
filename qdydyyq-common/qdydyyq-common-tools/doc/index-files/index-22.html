<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:36 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>V - 索引</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="V - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-21.html">上一个字母</a></li>
<li><a href="index-23.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-22.html" target="_top">框架</a></li>
<li><a href="index-22.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a name="I:V">
<!--   -->
</a>
<h2 class="title">V</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/ValueValidator.Validator.html#validate-T-">validate(T)</a></span> - 接口 中的方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/ValueValidator.Validator.html" title="com.huazheng.tunny.tools.base中的接口">ValueValidator.Validator</a></dt>
<dd>
<div class="block">校验值是否匹配</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectMap.PrimitiveEntry.html#value--">value()</a></span> - 接口 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectMap.PrimitiveEntry.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口">IntObjectMap.PrimitiveEntry</a></dt>
<dd>
<div class="block">Gets the value for this entry.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.PrimitiveEntry.html#value--">value()</a></span> - 接口 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.PrimitiveEntry.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口">LongObjectMap.PrimitiveEntry</a></dt>
<dd>
<div class="block">Gets the value for this entry.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/EnumUtilTest.Options.html#valueOf-java.lang.String-">valueOf(String)</a></span> - 枚举 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/EnumUtilTest.Options.html" title="com.huazheng.tunny.tools.base中的枚举">EnumUtilTest.Options</a></dt>
<dd>
<div class="block">返回带有指定名称的该类型的枚举常量。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtilTest.EnumA.html#valueOf-java.lang.String-">valueOf(String)</a></span> - 枚举 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtilTest.EnumA.html" title="com.huazheng.tunny.tools.collection中的枚举">MapUtilTest.EnumA</a></dt>
<dd>
<div class="block">返回带有指定名称的该类型的枚举常量。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/SizeUnit.html#valueOf-java.lang.String-">valueOf(String)</a></span> - 枚举 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/SizeUnit.html" title="com.huazheng.tunny.tools.number中的枚举">SizeUnit</a></dt>
<dd>
<div class="block">返回带有指定名称的该类型的枚举常量。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/EnumUtilTest.Options.html#values--">values()</a></span> - 枚举 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/EnumUtilTest.Options.html" title="com.huazheng.tunny.tools.base中的枚举">EnumUtilTest.Options</a></dt>
<dd>
<div class="block">按照声明该枚举类型的常量的顺序, 返回
包含这些常量的数组。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtilTest.EnumA.html#values--">values()</a></span> - 枚举 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtilTest.EnumA.html" title="com.huazheng.tunny.tools.collection中的枚举">MapUtilTest.EnumA</a></dt>
<dd>
<div class="block">按照声明该枚举类型的常量的顺序, 返回
包含这些常量的数组。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html#values--">values()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">IntObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#values--">values()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">LongObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/SizeUnit.html#values--">values()</a></span> - 枚举 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/SizeUnit.html" title="com.huazheng.tunny.tools.number中的枚举">SizeUnit</a></dt>
<dd>
<div class="block">按照声明该枚举类型的常量的顺序, 返回
包含这些常量的数组。</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/base/ValueValidator.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">ValueValidator</span></a> - <a href="../com/huazheng/tunny/tools/base/package-summary.html">com.huazheng.tunny.tools.base</a>中的类</dt>
<dd>
<div class="block">配值较验器 
 
 提供对值进行较验的api，并根据较验结果取值且返回</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/ValueValidator.html#ValueValidator--">ValueValidator()</a></span> - 类 的构造器com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/ValueValidator.html" title="com.huazheng.tunny.tools.base中的类">ValueValidator</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/base/ValueValidator.Validator.html" title="com.huazheng.tunny.tools.base中的接口"><span class="typeNameLink">ValueValidator.Validator</span></a>&lt;<a href="../com/huazheng/tunny/tools/base/ValueValidator.Validator.html" title="ValueValidator.Validator中的类型参数">T</a>&gt; - <a href="../com/huazheng/tunny/tools/base/package-summary.html">com.huazheng.tunny.tools.base</a>中的接口</dt>
<dd>
<div class="block">对Properties值进行规则匹配的验证器</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/base/ValueValidatorTest.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">ValueValidatorTest</span></a> - <a href="../com/huazheng/tunny/tools/base/package-summary.html">com.huazheng.tunny.tools.base</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/ValueValidatorTest.html#ValueValidatorTest--">ValueValidatorTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/ValueValidatorTest.html" title="com.huazheng.tunny.tools.base中的类">ValueValidatorTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.AClass.html#vfield">vfield</a></span> - 类 中的变量com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.AClass.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtilTest.AClass</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/base/annotation/VisibleForTesting.html" title="com.huazheng.tunny.tools.base.annotation中的注释"><span class="typeNameLink">VisibleForTesting</span></a> - <a href="../com/huazheng/tunny/tools/base/annotation/package-summary.html">com.huazheng.tunny.tools.base.annotation</a>中的注释类型</dt>
<dd>
<div class="block">标注因为方便UT，将方法／属性的可访问范围扩大了，参考Guava</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-21.html">上一个字母</a></li>
<li><a href="index-23.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-22.html" target="_top">框架</a></li>
<li><a href="index-22.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
