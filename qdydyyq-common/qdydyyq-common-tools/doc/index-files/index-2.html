<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:35 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>B - 索引</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="B - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-1.html">上一个字母</a></li>
<li><a href="index-3.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-2.html" target="_top">框架</a></li>
<li><a href="index-2.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a name="I:B">
<!--   -->
</a>
<h2 class="title">B</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/EncodeUtilTest.html#base64Encode--">base64Encode()</a></span> - 类 中的方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/EncodeUtilTest.html" title="com.huazheng.tunny.tools.text中的类">EncodeUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/EncodeUtilTest.html#base64UrlSafeEncode--">base64UrlSafeEncode()</a></span> - 类 中的方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/EncodeUtilTest.html" title="com.huazheng.tunny.tools.text中的类">EncodeUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html" title="com.huazheng.tunny.tools.concurrent.type中的类"><span class="typeNameLink">BasicFuture</span></a>&lt;<a href="../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html" title="BasicFuture中的类型参数">T</a>&gt; - <a href="../com/huazheng/tunny/tools/concurrent/type/package-summary.html">com.huazheng.tunny.tools.concurrent.type</a>中的类</dt>
<dd>
<div class="block">从Apache HttpClient 移植(2017.4)，一个Future实现类的基本框架.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html#BasicFuture--">BasicFuture()</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.type.<a href="../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html" title="com.huazheng.tunny.tools.concurrent.type中的类">BasicFuture</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/BasicFutureTest.html" title="com.huazheng.tunny.tools.concurrent中的类"><span class="typeNameLink">BasicFutureTest</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/package-summary.html">com.huazheng.tunny.tools.concurrent</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/BasicFutureTest.html#BasicFutureTest--">BasicFutureTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/BasicFutureTest.html" title="com.huazheng.tunny.tools.concurrent中的类">BasicFutureTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/BasicFutureTest.MyFuture.html" title="com.huazheng.tunny.tools.concurrent中的类"><span class="typeNameLink">BasicFutureTest.MyFuture</span></a>&lt;<a href="../com/huazheng/tunny/tools/concurrent/BasicFutureTest.MyFuture.html" title="BasicFutureTest.MyFuture中的类型参数">T</a>&gt; - <a href="../com/huazheng/tunny/tools/concurrent/package-summary.html">com.huazheng.tunny.tools.concurrent</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.BClass.html#BClass--">BClass()</a></span> - 类 的构造器com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.BClass.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtilTest.BClass</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/mapper/BeanMapper.html" title="com.huazheng.tunny.tools.mapper中的类"><span class="typeNameLink">BeanMapper</span></a> - <a href="../com/huazheng/tunny/tools/mapper/package-summary.html">com.huazheng.tunny.tools.mapper</a>中的类</dt>
<dd>
<div class="block">实现深度的BeanOfClasssA<->BeanOfClassB复制
 
 不要使用Apache Common BeanUtils进行类复制，每次就行反射查询对象的属性列表, 非常缓慢.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapper.html#BeanMapper--">BeanMapper()</a></span> - 类 的构造器com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapper.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapper</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.html" title="com.huazheng.tunny.tools.mapper中的类"><span class="typeNameLink">BeanMapperTest</span></a> - <a href="../com/huazheng/tunny/tools/mapper/package-summary.html">com.huazheng.tunny.tools.mapper</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.html#BeanMapperTest--">BeanMapperTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Student.html" title="com.huazheng.tunny.tools.mapper中的类"><span class="typeNameLink">BeanMapperTest.Student</span></a> - <a href="../com/huazheng/tunny/tools/mapper/package-summary.html">com.huazheng.tunny.tools.mapper</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.StudentVO.html" title="com.huazheng.tunny.tools.mapper中的类"><span class="typeNameLink">BeanMapperTest.StudentVO</span></a> - <a href="../com/huazheng/tunny/tools/mapper/package-summary.html">com.huazheng.tunny.tools.mapper</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Teacher.html" title="com.huazheng.tunny.tools.mapper中的类"><span class="typeNameLink">BeanMapperTest.Teacher</span></a> - <a href="../com/huazheng/tunny/tools/mapper/package-summary.html">com.huazheng.tunny.tools.mapper</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.TeacherVO.html" title="com.huazheng.tunny.tools.mapper中的类"><span class="typeNameLink">BeanMapperTest.TeacherVO</span></a> - <a href="../com/huazheng/tunny/tools/mapper/package-summary.html">com.huazheng.tunny.tools.mapper</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/base/BeanUtilTest.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">BeanUtilTest</span></a> - <a href="../com/huazheng/tunny/tools/base/package-summary.html">com.huazheng.tunny.tools.base</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/BeanUtilTest.html#BeanUtilTest--">BeanUtilTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/BeanUtilTest.html" title="com.huazheng.tunny.tools.base中的类">BeanUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#beginOfDate-java.util.Date-">beginOfDate(Date)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">2016-11-10 07:33:23, 则返回2016-11-10 00:00:00</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#beginOfHour-java.util.Date-">beginOfHour(Date)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">2016-12-10 07:33:23, 则返回2016-12-10 07:00:00</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#beginOfMinute-java.util.Date-">beginOfMinute(Date)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">2016-12-10 07:33:23, 则返回2016-12-10 07:33:00</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#beginOfMonth-java.util.Date-">beginOfMonth(Date)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">2016-11-10 07:33:23, 则返回2016-11-1 00:00:00</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#beginOfWeek-java.util.Date-">beginOfWeek(Date)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">2017-1-20 07:33:23, 则返回2017-1-16 00:00:00</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#beginOfYear-java.util.Date-">beginOfYear(Date)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">2016-11-10 07:33:23, 则返回2016-1-1 00:00:00</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtil.html#binarySearch-java.util.List-T-">binarySearch(List&lt;? extends Comparable&lt;? super T&gt;&gt;, T)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtil.html" title="com.huazheng.tunny.tools.collection中的类">ListUtil</a></dt>
<dd>
<div class="block">二分法快速查找对象, 使用Comparable对象自身的比较.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtil.html#binarySearch-java.util.List-T-java.util.Comparator-">binarySearch(List&lt;? extends T&gt;, T, Comparator&lt;? super T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtil.html" title="com.huazheng.tunny.tools.collection中的类">ListUtil</a></dt>
<dd>
<div class="block">二分法快速查找对象，使用Comparator.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtilTest.html#booleanSystemProperty--">booleanSystemProperty()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtilTest.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/base/BooleanUtil.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">BooleanUtil</span></a> - <a href="../com/huazheng/tunny/tools/base/package-summary.html">com.huazheng.tunny.tools.base</a>中的类</dt>
<dd>
<div class="block">1.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/BooleanUtil.html#BooleanUtil--">BooleanUtil()</a></span> - 类 的构造器com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/BooleanUtil.html" title="com.huazheng.tunny.tools.base中的类">BooleanUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/CollectionUtil.html#bottomN-java.lang.Iterable-int-">bottomN(Iterable&lt;T&gt;, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/CollectionUtil.html" title="com.huazheng.tunny.tools.collection中的类">CollectionUtil</a></dt>
<dd>
<div class="block">返回Iterable中最小的N个对象, back by guava.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/CollectionUtil.html#bottomN-java.lang.Iterable-int-java.util.Comparator-">bottomN(Iterable&lt;T&gt;, int, Comparator&lt;? super T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/CollectionUtil.html" title="com.huazheng.tunny.tools.collection中的类">CollectionUtil</a></dt>
<dd>
<div class="block">返回Iterable中最小的N个对象, back by guava.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html#build--">build()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.CachedThreadPoolBuilder</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.FixedThreadPoolBuilder.html#build--">build()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.FixedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.FixedThreadPoolBuilder</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.QueuableCachedThreadPoolBuilder.html#build--">build()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.QueuableCachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.QueuableCachedThreadPoolBuilder</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.ScheduledThreadPoolBuilder.html#build--">build()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.ScheduledThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.ScheduledThreadPoolBuilder</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/JsonMapper.html#buildCollectionType-java.lang.Class-java.lang.Class-">buildCollectionType(Class&lt;? extends Collection&gt;, Class&lt;?&gt;)</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/JsonMapper.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapper</a></dt>
<dd>
<div class="block">构造Collection类型.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtilTest.html#buildMap--">buildMap()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtilTest.html" title="com.huazheng.tunny.tools.collection中的类">MapUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/JsonMapper.html#buildMapType-java.lang.Class-java.lang.Class-java.lang.Class-">buildMapType(Class&lt;? extends Map&gt;, Class&lt;?&gt;, Class&lt;?&gt;)</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/JsonMapper.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapper</a></dt>
<dd>
<div class="block">构造Map类型.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/ExceptionUtilTest.html#buildMessage--">buildMessage()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/ExceptionUtilTest.html" title="com.huazheng.tunny.tools.base中的类">ExceptionUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtil.html#buildThreadFactory-java.lang.String-">buildThreadFactory(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtil.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolUtil</a></dt>
<dd>
<div class="block">创建ThreadFactory，使得创建的线程有自己的名字而不是默认的"pool-x-thread-y"
 
 使用了Guava的工具类</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtil.html#buildThreadFactory-java.lang.String-boolean-">buildThreadFactory(String, boolean)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtil.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolUtil</a></dt>
<dd>
<div class="block">可设定是否daemon, daemon线程在主线程已执行完毕时, 不会阻塞应用不退出, 而非daemon线程则会阻塞.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtilTest.html#buildThreadFactory--">buildThreadFactory()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtilTest.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolUtilTest</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-1.html">上一个字母</a></li>
<li><a href="index-3.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-2.html" target="_top">框架</a></li>
<li><a href="index-2.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
