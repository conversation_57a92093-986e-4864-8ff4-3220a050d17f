<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:36 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>C - 索引</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="C - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-2.html">上一个字母</a></li>
<li><a href="index-4.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-3.html" target="_top">框架</a></li>
<li><a href="index-3.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a name="I:C">
<!--   -->
</a>
<h2 class="title">C</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.html#cachedPool--">cachedPool()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilderTest.html#cachedPool--">cachedPool()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilderTest.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilderTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html#CachedThreadPoolBuilder--">CachedThreadPoolBuilder()</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.CachedThreadPoolBuilder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/time/CachingDateFormatter.html" title="com.huazheng.tunny.tools.time中的类"><span class="typeNameLink">CachingDateFormatter</span></a> - <a href="../com/huazheng/tunny/tools/time/package-summary.html">com.huazheng.tunny.tools.time</a>中的类</dt>
<dd>
<div class="block">DateFormat.format()消耗较大，如果时间戳是递增的，而且同一单位内有多次format()，使用用本类减少重复调用.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/CachingDateFormatter.html#CachingDateFormatter-java.lang.String-">CachingDateFormatter(String)</a></span> - 类 的构造器com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/CachingDateFormatter.html" title="com.huazheng.tunny.tools.time中的类">CachingDateFormatter</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/CachingDateFormatter.html#CachingDateFormatter-org.apache.commons.lang3.time.FastDateFormat-">CachingDateFormatter(FastDateFormat)</a></span> - 类 的构造器com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/CachingDateFormatter.html" title="com.huazheng.tunny.tools.time中的类">CachingDateFormatter</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/time/CachingDatFormatterTest.html" title="com.huazheng.tunny.tools.time中的类"><span class="typeNameLink">CachingDatFormatterTest</span></a> - <a href="../com/huazheng/tunny/tools/time/package-summary.html">com.huazheng.tunny.tools.time</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/CachingDatFormatterTest.html#CachingDatFormatterTest--">CachingDatFormatterTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/CachingDatFormatterTest.html" title="com.huazheng.tunny.tools.time中的类">CachingDatFormatterTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/MathUtilTest.html#caculate--">caculate()</a></span> - 类 中的方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/MathUtilTest.html" title="com.huazheng.tunny.tools.number中的类">MathUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html#cancel-boolean-">cancel(boolean)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.type.<a href="../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html" title="com.huazheng.tunny.tools.concurrent.type中的类">BasicFuture</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/ExceptionUtilTest.html#cause--">cause()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/ExceptionUtilTest.html" title="com.huazheng.tunny.tools.base中的类">ExceptionUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtilTest.html#changeDay--">changeDay()</a></span> - 类 中的方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtilTest.html" title="com.huazheng.tunny.tools.time中的类">DateUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/MoreStringUtilTest.html#charMatch--">charMatch()</a></span> - 类 中的方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/MoreStringUtilTest.html" title="com.huazheng.tunny.tools.text中的类">MoreStringUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/text/Charsets.html" title="com.huazheng.tunny.tools.text中的类"><span class="typeNameLink">Charsets</span></a> - <a href="../com/huazheng/tunny/tools/text/package-summary.html">com.huazheng.tunny.tools.text</a>中的类</dt>
<dd>
<div class="block">尽量使用Charsets.UTF8而不是"UTF-8"，减少JDK里的Charset查找消耗.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/Charsets.html#Charsets--">Charsets()</a></span> - 类 的构造器com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/Charsets.html" title="com.huazheng.tunny.tools.text中的类">Charsets</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/MoreStringUtil.html#charsSplitter-java.lang.String-">charsSplitter(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/MoreStringUtil.html" title="com.huazheng.tunny.tools.text中的类">MoreStringUtil</a></dt>
<dd>
<div class="block">使用多个可选的char作为分割符, 还可以设置omitEmptyStrings,trimResults等配置
 
 设置后的Splitter进行重用，不要每次创建</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/ValueValidator.html#checkAndGet-T-T-com.huazheng.tunny.tools.base.ValueValidator.Validator-">checkAndGet(T, T, ValueValidator.Validator&lt;T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/ValueValidator.html" title="com.huazheng.tunny.tools.base中的类">ValueValidator</a></dt>
<dd>
<div class="block">对目标值进行校验，并根据校验结果取值
 
 使用示例(校验目标值是否大于0, 如果小于 0 则取值为 1)
 
 ValueValidator.checkAndGet(-1, 1, Validator.INTEGER_GT_ZERO_VALIDATOR)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/Platforms.html#CLASS_PATH_SEPARATOR">CLASS_PATH_SEPARATOR</a></span> - 类 中的静态变量com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/Platforms.html" title="com.huazheng.tunny.tools.base中的类">Platforms</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/Platforms.html#CLASS_PATH_SEPARATOR_CHAR">CLASS_PATH_SEPARATOR_CHAR</a></span> - 类 中的静态变量com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/Platforms.html" title="com.huazheng.tunny.tools.base中的类">Platforms</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/reflect/ClassLoaderUtil.html" title="com.huazheng.tunny.tools.reflect中的类"><span class="typeNameLink">ClassLoaderUtil</span></a> - <a href="../com/huazheng/tunny/tools/reflect/package-summary.html">com.huazheng.tunny.tools.reflect</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassLoaderUtil.html#ClassLoaderUtil--">ClassLoaderUtil()</a></span> - 类 的构造器com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassLoaderUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ClassLoaderUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/reflect/ClassloaderUtilTest.html" title="com.huazheng.tunny.tools.reflect中的类"><span class="typeNameLink">ClassloaderUtilTest</span></a> - <a href="../com/huazheng/tunny/tools/reflect/package-summary.html">com.huazheng.tunny.tools.reflect</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassloaderUtilTest.html#ClassloaderUtilTest--">ClassloaderUtilTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassloaderUtilTest.html" title="com.huazheng.tunny.tools.reflect中的类">ClassloaderUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.html#classPresent--">classPresent()</a></span> - 类 中的方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/reflect/ClassUtil.html" title="com.huazheng.tunny.tools.reflect中的类"><span class="typeNameLink">ClassUtil</span></a> - <a href="../com/huazheng/tunny/tools/reflect/package-summary.html">com.huazheng.tunny.tools.reflect</a>中的类</dt>
<dd>
<div class="block">获取Class信息的工具类
 
 1.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassUtil.html#ClassUtil--">ClassUtil()</a></span> - 类 的构造器com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.html" title="com.huazheng.tunny.tools.reflect中的类"><span class="typeNameLink">ClassUtilTest</span></a> - <a href="../com/huazheng/tunny/tools/reflect/package-summary.html">com.huazheng.tunny.tools.reflect</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.html#ClassUtilTest--">ClassUtilTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.AAnnotation.html" title="com.huazheng.tunny.tools.reflect中的注释"><span class="typeNameLink">ClassUtilTest.AAnnotation</span></a> - <a href="../com/huazheng/tunny/tools/reflect/package-summary.html">com.huazheng.tunny.tools.reflect</a>中的注释类型</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.AClass.html" title="com.huazheng.tunny.tools.reflect中的类"><span class="typeNameLink">ClassUtilTest.AClass</span></a> - <a href="../com/huazheng/tunny/tools/reflect/package-summary.html">com.huazheng.tunny.tools.reflect</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.AInterface.html" title="com.huazheng.tunny.tools.reflect中的接口"><span class="typeNameLink">ClassUtilTest.AInterface</span></a> - <a href="../com/huazheng/tunny/tools/reflect/package-summary.html">com.huazheng.tunny.tools.reflect</a>中的接口</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.BAnnotation.html" title="com.huazheng.tunny.tools.reflect中的注释"><span class="typeNameLink">ClassUtilTest.BAnnotation</span></a> - <a href="../com/huazheng/tunny/tools/reflect/package-summary.html">com.huazheng.tunny.tools.reflect</a>中的注释类型</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.BClass.html" title="com.huazheng.tunny.tools.reflect中的类"><span class="typeNameLink">ClassUtilTest.BClass</span></a> - <a href="../com/huazheng/tunny/tools/reflect/package-summary.html">com.huazheng.tunny.tools.reflect</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.BInterface.html" title="com.huazheng.tunny.tools.reflect中的接口"><span class="typeNameLink">ClassUtilTest.BInterface</span></a> - <a href="../com/huazheng/tunny/tools/reflect/package-summary.html">com.huazheng.tunny.tools.reflect</a>中的接口</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.CAnnotation.html" title="com.huazheng.tunny.tools.reflect中的注释"><span class="typeNameLink">ClassUtilTest.CAnnotation</span></a> - <a href="../com/huazheng/tunny/tools/reflect/package-summary.html">com.huazheng.tunny.tools.reflect</a>中的注释类型</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.CInterface.html" title="com.huazheng.tunny.tools.reflect中的接口"><span class="typeNameLink">ClassUtilTest.CInterface</span></a> - <a href="../com/huazheng/tunny/tools/reflect/package-summary.html">com.huazheng.tunny.tools.reflect</a>中的接口</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.DAnnotation.html" title="com.huazheng.tunny.tools.reflect中的注释"><span class="typeNameLink">ClassUtilTest.DAnnotation</span></a> - <a href="../com/huazheng/tunny/tools/reflect/package-summary.html">com.huazheng.tunny.tools.reflect</a>中的注释类型</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.DInterface.html" title="com.huazheng.tunny.tools.reflect中的接口"><span class="typeNameLink">ClassUtilTest.DInterface</span></a> - <a href="../com/huazheng/tunny/tools/reflect/package-summary.html">com.huazheng.tunny.tools.reflect</a>中的接口</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.EAnnotation.html" title="com.huazheng.tunny.tools.reflect中的注释"><span class="typeNameLink">ClassUtilTest.EAnnotation</span></a> - <a href="../com/huazheng/tunny/tools/reflect/package-summary.html">com.huazheng.tunny.tools.reflect</a>中的注释类型</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.FAnnotation.html" title="com.huazheng.tunny.tools.reflect中的注释"><span class="typeNameLink">ClassUtilTest.FAnnotation</span></a> - <a href="../com/huazheng/tunny/tools/reflect/package-summary.html">com.huazheng.tunny.tools.reflect</a>中的注释类型</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.ParentBean.html" title="com.huazheng.tunny.tools.reflect中的类"><span class="typeNameLink">ClassUtilTest.ParentBean</span></a>&lt;<a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.ParentBean.html" title="ClassUtilTest.ParentBean中的类型参数">T</a>,<a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.ParentBean.html" title="ClassUtilTest.ParentBean中的类型参数">ID</a>&gt; - <a href="../com/huazheng/tunny/tools/reflect/package-summary.html">com.huazheng.tunny.tools.reflect</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.TestBean.html" title="com.huazheng.tunny.tools.reflect中的类"><span class="typeNameLink">ClassUtilTest.TestBean</span></a> - <a href="../com/huazheng/tunny/tools/reflect/package-summary.html">com.huazheng.tunny.tools.reflect</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.TestBean2.html" title="com.huazheng.tunny.tools.reflect中的类"><span class="typeNameLink">ClassUtilTest.TestBean2</span></a> - <a href="../com/huazheng/tunny/tools/reflect/package-summary.html">com.huazheng.tunny.tools.reflect</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.TestBean3.html" title="com.huazheng.tunny.tools.reflect中的类"><span class="typeNameLink">ClassUtilTest.TestBean3</span></a> - <a href="../com/huazheng/tunny/tools/reflect/package-summary.html">com.huazheng.tunny.tools.reflect</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html#clear--">clear()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.<a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="com.huazheng.tunny.tools.collection.type中的类">ConcurrentHashSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html#clear--">clear()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">IntObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#clear--">clear()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">LongObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/vip/vjtools/test/log/LogbackListAppender.html#clearLogs--">clearLogs()</a></span> - 类 中的方法com.vip.vjtools.test.log.<a href="../com/vip/vjtools/test/log/LogbackListAppender.html" title="com.vip.vjtools.test.log中的类">LogbackListAppender</a></dt>
<dd>
<div class="block">清除之前append的所有log.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/ExceptionUtil.html#clearStackTrace-T-">clearStackTrace(T)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/ExceptionUtil.html" title="com.huazheng.tunny.tools.base中的类">ExceptionUtil</a></dt>
<dd>
<div class="block">清除StackTrace.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/ExceptionUtilTest.html#clearStackTrace--">clearStackTrace()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/ExceptionUtilTest.html" title="com.huazheng.tunny.tools.base中的类">ExceptionUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/time/ClockUtil.html" title="com.huazheng.tunny.tools.time中的类"><span class="typeNameLink">ClockUtil</span></a> - <a href="../com/huazheng/tunny/tools/time/package-summary.html">com.huazheng.tunny.tools.time</a>中的类</dt>
<dd>
<div class="block">日期提供者, 使用它而不是直接取得系统时间, 方便测试.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/ClockUtil.html#ClockUtil--">ClockUtil()</a></span> - 类 的构造器com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/ClockUtil.html" title="com.huazheng.tunny.tools.time中的类">ClockUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/time/ClockUtil.Clock.html" title="com.huazheng.tunny.tools.time中的接口"><span class="typeNameLink">ClockUtil.Clock</span></a> - <a href="../com/huazheng/tunny/tools/time/package-summary.html">com.huazheng.tunny.tools.time</a>中的接口</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/time/ClockUtil.DefaultClock.html" title="com.huazheng.tunny.tools.time中的类"><span class="typeNameLink">ClockUtil.DefaultClock</span></a> - <a href="../com/huazheng/tunny/tools/time/package-summary.html">com.huazheng.tunny.tools.time</a>中的类</dt>
<dd>
<div class="block">默认时间提供者，返回当前的时间，线程安全。</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/time/ClockUtil.DummyClock.html" title="com.huazheng.tunny.tools.time中的类"><span class="typeNameLink">ClockUtil.DummyClock</span></a> - <a href="../com/huazheng/tunny/tools/time/package-summary.html">com.huazheng.tunny.tools.time</a>中的类</dt>
<dd>
<div class="block">可配置的时间提供者，用于测试.</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/time/ClockUtilTest.html" title="com.huazheng.tunny.tools.time中的类"><span class="typeNameLink">ClockUtilTest</span></a> - <a href="../com/huazheng/tunny/tools/time/package-summary.html">com.huazheng.tunny.tools.time</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/ClockUtilTest.html#ClockUtilTest--">ClockUtilTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/ClockUtilTest.html" title="com.huazheng.tunny.tools.time中的类">ClockUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/type/CloneableException.html#clone--">clone()</a></span> - 异常错误 中的方法com.huazheng.tunny.tools.base.type.<a href="../com/huazheng/tunny/tools/base/type/CloneableException.html" title="com.huazheng.tunny.tools.base.type中的类">CloneableException</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/type/CloneableException.html#clone-java.lang.String-">clone(String)</a></span> - 异常错误 中的方法com.huazheng.tunny.tools.base.type.<a href="../com/huazheng/tunny/tools/base/type/CloneableException.html" title="com.huazheng.tunny.tools.base.type中的类">CloneableException</a></dt>
<dd>
<div class="block">简便函数, clone并重新设定Message</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html#clone--">clone()</a></span> - 异常错误 中的方法com.huazheng.tunny.tools.base.type.<a href="../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html" title="com.huazheng.tunny.tools.base.type中的类">CloneableRuntimeException</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html#clone-java.lang.String-">clone(String)</a></span> - 异常错误 中的方法com.huazheng.tunny.tools.base.type.<a href="../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html" title="com.huazheng.tunny.tools.base.type中的类">CloneableRuntimeException</a></dt>
<dd>
<div class="block">简便函数, clone并重新设定Message</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/base/type/CloneableException.html" title="com.huazheng.tunny.tools.base.type中的类"><span class="typeNameLink">CloneableException</span></a> - <a href="../com/huazheng/tunny/tools/base/type/package-summary.html">com.huazheng.tunny.tools.base.type</a>中的异常错误</dt>
<dd>
<div class="block">适用于异常信息需要变更的情况, 可通过clone()，不经过构造函数（也就避免了获得StackTrace）地从之前定义的静态异常中克隆，再设定新的异常信息
 
 private static CloneableException TIMEOUT_EXCEPTION = new CloneableException("Timeout") .setStackTrace(My.class,
 "hello"); ...</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/type/CloneableException.html#CloneableException--">CloneableException()</a></span> - 异常错误 的构造器com.huazheng.tunny.tools.base.type.<a href="../com/huazheng/tunny/tools/base/type/CloneableException.html" title="com.huazheng.tunny.tools.base.type中的类">CloneableException</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/type/CloneableException.html#CloneableException-java.lang.String-">CloneableException(String)</a></span> - 异常错误 的构造器com.huazheng.tunny.tools.base.type.<a href="../com/huazheng/tunny/tools/base/type/CloneableException.html" title="com.huazheng.tunny.tools.base.type中的类">CloneableException</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/type/CloneableException.html#CloneableException-java.lang.String-java.lang.Throwable-">CloneableException(String, Throwable)</a></span> - 异常错误 的构造器com.huazheng.tunny.tools.base.type.<a href="../com/huazheng/tunny/tools/base/type/CloneableException.html" title="com.huazheng.tunny.tools.base.type中的类">CloneableException</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html" title="com.huazheng.tunny.tools.base.type中的类"><span class="typeNameLink">CloneableRuntimeException</span></a> - <a href="../com/huazheng/tunny/tools/base/type/package-summary.html">com.huazheng.tunny.tools.base.type</a>中的异常错误</dt>
<dd>
<div class="block">适用于异常信息需要变更的情况, 可通过clone()，不经过构造函数（也就避免了获得StackTrace）地从之前定义的静态异常中克隆，再设定新的异常信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html#CloneableRuntimeException--">CloneableRuntimeException()</a></span> - 异常错误 的构造器com.huazheng.tunny.tools.base.type.<a href="../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html" title="com.huazheng.tunny.tools.base.type中的类">CloneableRuntimeException</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html#CloneableRuntimeException-java.lang.String-">CloneableRuntimeException(String)</a></span> - 异常错误 的构造器com.huazheng.tunny.tools.base.type.<a href="../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html" title="com.huazheng.tunny.tools.base.type中的类">CloneableRuntimeException</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html#CloneableRuntimeException-java.lang.String-java.lang.Throwable-">CloneableRuntimeException(String, Throwable)</a></span> - 异常错误 的构造器com.huazheng.tunny.tools.base.type.<a href="../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html" title="com.huazheng.tunny.tools.base.type中的类">CloneableRuntimeException</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html#close--">close()</a></span> - 类 中的方法com.huazheng.tunny.tools.io.type.<a href="../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html" title="com.huazheng.tunny.tools.io.type中的类">StringBuilderWriter</a></dt>
<dd>
<div class="block">Closing this writer has no effect.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/IOUtil.html#closeQuietly-java.io.Closeable-">closeQuietly(Closeable)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/IOUtil.html" title="com.huazheng.tunny.tools.io中的类">IOUtil</a></dt>
<dd>
<div class="block">在final中安静的关闭, 不再往外抛出异常避免影响原有异常，最常用函数.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/XmlMapper.CollectionWrapper.html#collection">collection</a></span> - 类 中的变量com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/XmlMapper.CollectionWrapper.html" title="com.huazheng.tunny.tools.mapper中的类">XmlMapper.CollectionWrapper</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/SetUtilTest.html#collectionCaculate--">collectionCaculate()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/SetUtilTest.html" title="com.huazheng.tunny.tools.collection中的类">SetUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtilTest.html#collectionCalc--">collectionCalc()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtilTest.html" title="com.huazheng.tunny.tools.collection中的类">ListUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/collection/CollectionUtil.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">CollectionUtil</span></a> - <a href="../com/huazheng/tunny/tools/collection/package-summary.html">com.huazheng.tunny.tools.collection</a>中的类</dt>
<dd>
<div class="block">通用Collection的工具集
 
 1.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/CollectionUtil.html#CollectionUtil--">CollectionUtil()</a></span> - 类 的构造器com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/CollectionUtil.html" title="com.huazheng.tunny.tools.collection中的类">CollectionUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/collection/CollectionUtilTest.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">CollectionUtilTest</span></a> - <a href="../com/huazheng/tunny/tools/collection/package-summary.html">com.huazheng.tunny.tools.collection</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/CollectionUtilTest.html#CollectionUtilTest--">CollectionUtilTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/CollectionUtilTest.html" title="com.huazheng.tunny.tools.collection中的类">CollectionUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/XmlMapper.CollectionWrapper.html#CollectionWrapper--">CollectionWrapper()</a></span> - 类 的构造器com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/XmlMapper.CollectionWrapper.html" title="com.huazheng.tunny.tools.mapper中的类">XmlMapper.CollectionWrapper</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/base/package-summary.html">com.huazheng.tunny.tools.base</a> - 程序包 com.huazheng.tunny.tools.base</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/base/annotation/package-summary.html">com.huazheng.tunny.tools.base.annotation</a> - 程序包 com.huazheng.tunny.tools.base.annotation</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/base/type/package-summary.html">com.huazheng.tunny.tools.base.type</a> - 程序包 com.huazheng.tunny.tools.base.type</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/collection/package-summary.html">com.huazheng.tunny.tools.collection</a> - 程序包 com.huazheng.tunny.tools.collection</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/collection/type/package-summary.html">com.huazheng.tunny.tools.collection.type</a> - 程序包 com.huazheng.tunny.tools.collection.type</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/collection/type/primitive/package-summary.html">com.huazheng.tunny.tools.collection.type.primitive</a> - 程序包 com.huazheng.tunny.tools.collection.type.primitive</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/package-summary.html">com.huazheng.tunny.tools.concurrent</a> - 程序包 com.huazheng.tunny.tools.concurrent</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/jsr166e/package-summary.html">com.huazheng.tunny.tools.concurrent.jsr166e</a> - 程序包 com.huazheng.tunny.tools.concurrent.jsr166e</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/threadpool/package-summary.html">com.huazheng.tunny.tools.concurrent.threadpool</a> - 程序包 com.huazheng.tunny.tools.concurrent.threadpool</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/type/package-summary.html">com.huazheng.tunny.tools.concurrent.type</a> - 程序包 com.huazheng.tunny.tools.concurrent.type</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/io/package-summary.html">com.huazheng.tunny.tools.io</a> - 程序包 com.huazheng.tunny.tools.io</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/io/type/package-summary.html">com.huazheng.tunny.tools.io.type</a> - 程序包 com.huazheng.tunny.tools.io.type</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/mapper/package-summary.html">com.huazheng.tunny.tools.mapper</a> - 程序包 com.huazheng.tunny.tools.mapper</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/net/package-summary.html">com.huazheng.tunny.tools.net</a> - 程序包 com.huazheng.tunny.tools.net</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/number/package-summary.html">com.huazheng.tunny.tools.number</a> - 程序包 com.huazheng.tunny.tools.number</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/reflect/package-summary.html">com.huazheng.tunny.tools.reflect</a> - 程序包 com.huazheng.tunny.tools.reflect</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/security/package-summary.html">com.huazheng.tunny.tools.security</a> - 程序包 com.huazheng.tunny.tools.security</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/text/package-summary.html">com.huazheng.tunny.tools.text</a> - 程序包 com.huazheng.tunny.tools.text</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/time/package-summary.html">com.huazheng.tunny.tools.time</a> - 程序包 com.huazheng.tunny.tools.time</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/vip/vjtools/test/data/package-summary.html">com.vip.vjtools.test.data</a> - 程序包 com.vip.vjtools.test.data</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/vip/vjtools/test/log/package-summary.html">com.vip.vjtools.test.log</a> - 程序包 com.vip.vjtools.test.log</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/vip/vjtools/test/rule/package-summary.html">com.vip.vjtools.test.rule</a> - 程序包 com.vip.vjtools.test.rule</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/SortedArrayList.html#comparator">comparator</a></span> - 类 中的变量com.huazheng.tunny.tools.collection.type.<a href="../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="com.huazheng.tunny.tools.collection.type中的类">SortedArrayList</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/SortedArrayList.html#compare-E-E-">compare(E, E)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.<a href="../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="com.huazheng.tunny.tools.collection.type中的类">SortedArrayList</a></dt>
<dd>
<div class="block">Compares two keys using the correct comparison method for this
 collection.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html#completed-T-">completed(T)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.type.<a href="../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html" title="com.huazheng.tunny.tools.concurrent.type中的类">BasicFuture</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ArrayUtil.html#concat-T-T:A-">concat(T, T[])</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ArrayUtil.html" title="com.huazheng.tunny.tools.collection中的类">ArrayUtil</a></dt>
<dd>
<div class="block">添加元素到数组头.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ArrayUtil.html#concat-T:A-T-">concat(T[], T)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ArrayUtil.html" title="com.huazheng.tunny.tools.collection中的类">ArrayUtil</a></dt>
<dd>
<div class="block">添加元素到数组末尾.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FilePathUtil.html#concat-java.lang.String-java.lang.String...-">concat(String, String...)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FilePathUtil.html" title="com.huazheng.tunny.tools.io中的类">FilePathUtil</a></dt>
<dd>
<div class="block">以拼接路径名</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="com.huazheng.tunny.tools.collection.type中的类"><span class="typeNameLink">ConcurrentHashSet</span></a>&lt;<a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt; - <a href="../com/huazheng/tunny/tools/collection/type/package-summary.html">com.huazheng.tunny.tools.collection.type</a>中的类</dt>
<dd>
<div class="block">JDK并没有提供ConcurrenHashSet，考虑到JDK的HashSet也是基于HashMap实现的，因此ConcurrenHashSet也由ConcurrenHashMap完成。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html#ConcurrentHashSet--">ConcurrentHashSet()</a></span> - 类 的构造器com.huazheng.tunny.tools.collection.type.<a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="com.huazheng.tunny.tools.collection.type中的类">ConcurrentHashSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSetTest.html#concurrentHashSet--">concurrentHashSet()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.<a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSetTest.html" title="com.huazheng.tunny.tools.collection.type中的类">ConcurrentHashSetTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSetTest.html" title="com.huazheng.tunny.tools.collection.type中的类"><span class="typeNameLink">ConcurrentHashSetTest</span></a> - <a href="../com/huazheng/tunny/tools/collection/type/package-summary.html">com.huazheng.tunny.tools.collection.type</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSetTest.html#ConcurrentHashSetTest--">ConcurrentHashSetTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.collection.type.<a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSetTest.html" title="com.huazheng.tunny.tools.collection.type中的类">ConcurrentHashSetTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/Concurrents.html" title="com.huazheng.tunny.tools.concurrent中的类"><span class="typeNameLink">Concurrents</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/package-summary.html">com.huazheng.tunny.tools.concurrent</a>中的类</dt>
<dd>
<div class="block">并发常用工具类</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/Concurrents.html#Concurrents--">Concurrents()</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/Concurrents.html" title="com.huazheng.tunny.tools.concurrent中的类">Concurrents</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/ConcurrentsTest.html" title="com.huazheng.tunny.tools.concurrent中的类"><span class="typeNameLink">ConcurrentsTest</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/package-summary.html">com.huazheng.tunny.tools.concurrent</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/ConcurrentsTest.html#ConcurrentsTest--">ConcurrentsTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/ConcurrentsTest.html" title="com.huazheng.tunny.tools.concurrent中的类">ConcurrentsTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ArrayUtilTest.html#contact--">contact()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ArrayUtilTest.html" title="com.huazheng.tunny.tools.collection中的类">ArrayUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html#contains-java.lang.Object-">contains(Object)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.<a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="com.huazheng.tunny.tools.collection.type中的类">ConcurrentHashSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html#containsAll-java.util.Collection-">containsAll(Collection&lt;?&gt;)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.<a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="com.huazheng.tunny.tools.collection.type中的类">ConcurrentHashSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html#containsKey-int-">containsKey(int)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">IntObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html#containsKey-java.lang.Object-">containsKey(Object)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">IntObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectMap.html#containsKey-int-">containsKey(int)</a></span> - 接口 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口">IntObjectMap</a></dt>
<dd>
<div class="block">Indicates whether or not this map contains a value for the specified key.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#containsKey-long-">containsKey(long)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">LongObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#containsKey-java.lang.Object-">containsKey(Object)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">LongObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html#containsKey-long-">containsKey(long)</a></span> - 接口 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口">LongObjectMap</a></dt>
<dd>
<div class="block">Indicates whether or not this map contains a value for the specified key.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html#containsValue-java.lang.Object-">containsValue(Object)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">IntObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#containsValue-java.lang.Object-">containsValue(Object)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">LongObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.ControllableQueue.html#ControllableQueue-int-">ControllableQueue(int)</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.ControllableQueue.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">QueuableCachedThreadPool.ControllableQueue</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/UnitConverterTest.html#convertDurationMillis--">convertDurationMillis()</a></span> - 类 中的方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/UnitConverterTest.html" title="com.huazheng.tunny.tools.number中的类">UnitConverterTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#convertReflectionExceptionToUnchecked-java.lang.Exception-">convertReflectionExceptionToUnchecked(Exception)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtil</a></dt>
<dd>
<div class="block">将反射时的checked exception转换为unchecked exception.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.html#convertReflectionExceptionToUnchecked--">convertReflectionExceptionToUnchecked()</a></span> - 类 中的方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/UnitConverterTest.html#convertSizeBytes--">convertSizeBytes()</a></span> - 类 中的方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/UnitConverterTest.html" title="com.huazheng.tunny.tools.number中的类">UnitConverterTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/UnitConverterTest.html#convertToSizeUnit--">convertToSizeUnit()</a></span> - 类 中的方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/UnitConverterTest.html" title="com.huazheng.tunny.tools.number中的类">UnitConverterTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/UnitConverterTest.html#convertToTimeUnit--">convertToTimeUnit()</a></span> - 类 中的方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/UnitConverterTest.html" title="com.huazheng.tunny.tools.number中的类">UnitConverterTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#copy-java.io.File-java.io.File-">copy(File, File)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">复制文件或目录, not following links.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#copy-java.nio.file.Path-java.nio.file.Path-">copy(Path, Path)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">复制文件或目录, not following links.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/IOUtil.html#copy-java.io.Reader-java.io.Writer-">copy(Reader, Writer)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/IOUtil.html" title="com.huazheng.tunny.tools.io中的类">IOUtil</a></dt>
<dd>
<div class="block">在Reader与Writer间复制内容</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/IOUtil.html#copy-java.io.InputStream-java.io.OutputStream-">copy(InputStream, OutputStream)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/IOUtil.html" title="com.huazheng.tunny.tools.io中的类">IOUtil</a></dt>
<dd>
<div class="block">在InputStream与OutputStream间复制内容</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.html#copyArrayObject--">copyArrayObject()</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#copyDir-java.io.File-java.io.File-">copyDir(File, File)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">复制目录</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#copyDir-java.nio.file.Path-java.nio.file.Path-">copyDir(Path, Path)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">复制目录</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#copyFile-java.io.File-java.io.File-">copyFile(File, File)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">文件复制.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#copyFile-java.nio.file.Path-java.nio.file.Path-">copyFile(Path, Path)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">文件复制.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.html#copyListObject--">copyListObject()</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.html#copySingleObject--">copySingleObject()</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/Concurrents.html#countDownLatch-int-">countDownLatch(int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/Concurrents.html" title="com.huazheng.tunny.tools.concurrent中的类">Concurrents</a></dt>
<dd>
<div class="block">返回CountDownLatch, 每条线程减1，减到0时正在latch.wait()的进程继续进行</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/HashUtilTest.html#crc32--">crc32()</a></span> - 类 中的方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/HashUtilTest.html" title="com.huazheng.tunny.tools.text中的类">HashUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/HashUtil.html#crc32AsInt-java.lang.String-">crc32AsInt(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/HashUtil.html" title="com.huazheng.tunny.tools.text中的类">HashUtil</a></dt>
<dd>
<div class="block">对输入字符串进行crc32散列返回int, 返回值有可能是负数.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/HashUtil.html#crc32AsInt-byte:A-">crc32AsInt(byte[])</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/HashUtil.html" title="com.huazheng.tunny.tools.text中的类">HashUtil</a></dt>
<dd>
<div class="block">对输入字符串进行crc32散列返回int, 返回值有可能是负数.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/HashUtil.html#crc32AsLong-java.lang.String-">crc32AsLong(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/HashUtil.html" title="com.huazheng.tunny.tools.text中的类">HashUtil</a></dt>
<dd>
<div class="block">对输入字符串进行crc32散列，与php兼容，在64bit系统下返回永远是正数的long
 
 Guava也有crc32实现, 但返回值无法返回long，所以统一使用JDK默认实现</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/HashUtil.html#crc32AsLong-byte:A-">crc32AsLong(byte[])</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/HashUtil.html" title="com.huazheng.tunny.tools.text中的类">HashUtil</a></dt>
<dd>
<div class="block">对输入字符串进行crc32散列，与php兼容，在64bit系统下返回永远是正数的long
 
 Guava也有crc32实现, 但返回值无法返回long，所以统一使用JDK默认实现</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/Sampler.html#create-java.lang.Double-">create(Double)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/Sampler.html" title="com.huazheng.tunny.tools.concurrent中的类">Sampler</a></dt>
<dd>
<div class="block">优化的创建函数，如果为0或100时，返回更直接的采样器</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/vip/vjtools/test/log/LogbackListAppender.html#create-java.lang.Class-">create(Class&lt;?&gt;)</a></span> - 类 中的静态方法com.vip.vjtools.test.log.<a href="../com/vip/vjtools/test/log/LogbackListAppender.html" title="com.vip.vjtools.test.log中的类">LogbackListAppender</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/vip/vjtools/test/log/LogbackListAppender.html#create-java.lang.String-">create(String)</a></span> - 类 中的静态方法com.vip.vjtools.test.log.<a href="../com/vip/vjtools/test/log/LogbackListAppender.html" title="com.vip.vjtools.test.log中的类">LogbackListAppender</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MoreMaps.html#createConcurrentCounterMap--">createConcurrentCounterMap()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MoreMaps.html" title="com.huazheng.tunny.tools.collection中的类">MoreMaps</a></dt>
<dd>
<div class="block">以Guava的AtomicLongMap，实现线程安全的HashMap<E,AtomicLong>结构的Counter</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MoreQueues.html#createConcurrentStack--">createConcurrentStack()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MoreQueues.html" title="com.huazheng.tunny.tools.collection中的类">MoreQueues</a></dt>
<dd>
<div class="block">支持后进先出的无阻塞的并发栈，用ConcurrentLinkedDeque实现，经过Collections#asLifoQueue()转换顺序
 
 另对于BlockingQueue接口， JDK暂无Lifo倒转实现，因此只能直接使用未调转顺序的LinkedBlockingDeque</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtil.html#createIfAbsentReturnLast-java.util.concurrent.ConcurrentMap-K-com.huazheng.tunny.tools.collection.MapUtil.ValueCreator-">createIfAbsentReturnLast(ConcurrentMap&lt;K, V&gt;, K, MapUtil.ValueCreator&lt;? extends V&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtil.html" title="com.huazheng.tunny.tools.collection中的类">MapUtil</a></dt>
<dd>
<div class="block">如果Key不存在则创建，返回最后存储在Map中的Value.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MoreMaps.html#createListMultiValueMap-int-int-">createListMultiValueMap(int, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MoreMaps.html" title="com.huazheng.tunny.tools.collection中的类">MoreMaps</a></dt>
<dd>
<div class="block">以Guava的MultiMap，实现的HashMap<E,List<V>>结构的一个Key对应多个值的map.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MoreQueues.html#createLRUQueue-int-">createLRUQueue(int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MoreQueues.html" title="com.huazheng.tunny.tools.collection中的类">MoreQueues</a></dt>
<dd>
<div class="block">LRUQueue, 如果Queue已满，则删除最旧的元素.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/XmlMapper.html#createMarshaller-java.lang.Class-java.lang.String-">createMarshaller(Class, String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/XmlMapper.html" title="com.huazheng.tunny.tools.mapper中的类">XmlMapper</a></dt>
<dd>
<div class="block">创建Marshaller并设定encoding(可为null).</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MoreMaps.html#createMutableIntValueMap-int-float-">createMutableIntValueMap(int, float)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MoreMaps.html" title="com.huazheng.tunny.tools.collection中的类">MoreMaps</a></dt>
<dd>
<div class="block">创建值为可更改的Integer的HashMap.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MoreMaps.html#createMutableLongValueMap-int-float-">createMutableLongValueMap(int, float)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MoreMaps.html" title="com.huazheng.tunny.tools.collection中的类">MoreMaps</a></dt>
<dd>
<div class="block">创建值为可更改的Long的HashMap.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MoreMaps.html#createPrimitiveIntKeyMap-int-float-">createPrimitiveIntKeyMap(int, float)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MoreMaps.html" title="com.huazheng.tunny.tools.collection中的类">MoreMaps</a></dt>
<dd>
<div class="block">创建移植自Netty的key为int的优化HashMap</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MoreMaps.html#createPrimitiveLongKeyMap-int-float-">createPrimitiveLongKeyMap(int, float)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MoreMaps.html" title="com.huazheng.tunny.tools.collection中的类">MoreMaps</a></dt>
<dd>
<div class="block">创建移植自Netty的key为long的优化HashMap</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MoreMaps.html#createRangeMap--">createRangeMap()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MoreMaps.html" title="com.huazheng.tunny.tools.collection中的类">MoreMaps</a></dt>
<dd>
<div class="block">以Guava TreeRangeMap实现的, 一段范围的Key指向同一个Value的Map</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MoreLists.html#createSortedArrayList--">createSortedArrayList()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MoreLists.html" title="com.huazheng.tunny.tools.collection中的类">MoreLists</a></dt>
<dd>
<div class="block">排序的ArrayList.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MoreLists.html#createSortedArrayList-java.util.Comparator-">createSortedArrayList(Comparator&lt;? super T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MoreLists.html" title="com.huazheng.tunny.tools.collection中的类">MoreLists</a></dt>
<dd>
<div class="block">排序的ArrayList.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MoreMaps.html#createSortedSetMultiValueMap--">createSortedSetMultiValueMap()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MoreMaps.html" title="com.huazheng.tunny.tools.collection中的类">MoreMaps</a></dt>
<dd>
<div class="block">以Guava的MultiMap，实现的HashMap<E,TreeSet<V>>结构的一个Key对应多个值的map.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MoreMaps.html#createSortedSetMultiValueMap-java.util.Comparator-">createSortedSetMultiValueMap(Comparator&lt;V&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MoreMaps.html" title="com.huazheng.tunny.tools.collection中的类">MoreMaps</a></dt>
<dd>
<div class="block">以Guava的MultiMap，实现的HashMap<E,TreeSet<V>>结构的一个Key对应多个值的map.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MoreQueues.html#createStack-int-">createStack(int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MoreQueues.html" title="com.huazheng.tunny.tools.collection中的类">MoreQueues</a></dt>
<dd>
<div class="block">支持后进先出的栈，用ArrayDeque实现, 经过Collections#asLifoQueue()转换顺序
 
 需设置初始长度，默认为16，数组满时成倍扩容</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#createTempDir--">createTempDir()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">在临时目录创建临时目录，命名为${毫秒级时间戳}-${同一毫秒内的随机数}.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#createTempFile--">createTempFile()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">在临时目录创建临时文件，命名为tmp-${random.nextLong()}.tmp</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#createTempFile-java.lang.String-java.lang.String-">createTempFile(String, String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">在临时目录创建临时文件，命名为${prefix}${random.nextLong()}${suffix}</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/XmlMapper.html#createUnmarshaller-java.lang.Class-">createUnmarshaller(Class)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/XmlMapper.html" title="com.huazheng.tunny.tools.mapper中的类">XmlMapper</a></dt>
<dd>
<div class="block">创建UnMarshaller.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MoreMaps.html#createWeakKeyConcurrentMap-int-int-">createWeakKeyConcurrentMap(int, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MoreMaps.html" title="com.huazheng.tunny.tools.collection中的类">MoreMaps</a></dt>
<dd>
<div class="block">创建Key为弱引用的ConcurrentMap，Key对象可被回收.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MoreMaps.html#createWeakValueConcurrentMap-int-int-">createWeakValueConcurrentMap(int, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MoreMaps.html" title="com.huazheng.tunny.tools.collection中的类">MoreMaps</a></dt>
<dd>
<div class="block">创建Value为弱引用的ConcurrentMap，Value对象可被回收.</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/security/CryptoUtil.html" title="com.huazheng.tunny.tools.security中的类"><span class="typeNameLink">CryptoUtil</span></a> - <a href="../com/huazheng/tunny/tools/security/package-summary.html">com.huazheng.tunny.tools.security</a>中的类</dt>
<dd>
<div class="block">支持HMAC-SHA1消息签名 及 DES/AES对称加密的工具类.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/security/CryptoUtil.html#CryptoUtil--">CryptoUtil()</a></span> - 类 的构造器com.huazheng.tunny.tools.security.<a href="../com/huazheng/tunny/tools/security/CryptoUtil.html" title="com.huazheng.tunny.tools.security中的类">CryptoUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/security/CryptoUtilTest.html" title="com.huazheng.tunny.tools.security中的类"><span class="typeNameLink">CryptoUtilTest</span></a> - <a href="../com/huazheng/tunny/tools/security/package-summary.html">com.huazheng.tunny.tools.security</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/security/CryptoUtilTest.html#CryptoUtilTest--">CryptoUtilTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.security.<a href="../com/huazheng/tunny/tools/security/CryptoUtilTest.html" title="com.huazheng.tunny.tools.security中的类">CryptoUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/text/CsvUtil.html" title="com.huazheng.tunny.tools.text中的类"><span class="typeNameLink">CsvUtil</span></a> - <a href="../com/huazheng/tunny/tools/text/package-summary.html">com.huazheng.tunny.tools.text</a>中的类</dt>
<dd>
<div class="block">从Jodd移植
 
 https://github.com/oblac/jodd/blob/master/jodd-core/src/main/java/jodd/util/CsvUtil.java
 
 Helps with CSV strings.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/CsvUtil.html#CsvUtil--">CsvUtil()</a></span> - 类 的构造器com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/CsvUtil.html" title="com.huazheng.tunny.tools.text中的类">CsvUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/text/CsvUtilTest.html" title="com.huazheng.tunny.tools.text中的类"><span class="typeNameLink">CsvUtilTest</span></a> - <a href="../com/huazheng/tunny/tools/text/package-summary.html">com.huazheng.tunny.tools.text</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/CsvUtilTest.html#CsvUtilTest--">CsvUtilTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/CsvUtilTest.html" title="com.huazheng.tunny.tools.text中的类">CsvUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/ClockUtil.Clock.html#currentDate--">currentDate()</a></span> - 接口 中的方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/ClockUtil.Clock.html" title="com.huazheng.tunny.tools.time中的接口">ClockUtil.Clock</a></dt>
<dd>
<div class="block">系统当前时间</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/ClockUtil.html#currentDate--">currentDate()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/ClockUtil.html" title="com.huazheng.tunny.tools.time中的类">ClockUtil</a></dt>
<dd>
<div class="block">系统当前时间</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/ClockUtil.DefaultClock.html#currentDate--">currentDate()</a></span> - 类 中的方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/ClockUtil.DefaultClock.html" title="com.huazheng.tunny.tools.time中的类">ClockUtil.DefaultClock</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/ClockUtil.DummyClock.html#currentDate--">currentDate()</a></span> - 类 中的方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/ClockUtil.DummyClock.html" title="com.huazheng.tunny.tools.time中的类">ClockUtil.DummyClock</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/ClockUtil.Clock.html#currentTimeMillis--">currentTimeMillis()</a></span> - 接口 中的方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/ClockUtil.Clock.html" title="com.huazheng.tunny.tools.time中的接口">ClockUtil.Clock</a></dt>
<dd>
<div class="block">系统当前时间戳</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/ClockUtil.html#currentTimeMillis--">currentTimeMillis()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/ClockUtil.html" title="com.huazheng.tunny.tools.time中的类">ClockUtil</a></dt>
<dd>
<div class="block">系统当前时间戳</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/ClockUtil.DefaultClock.html#currentTimeMillis--">currentTimeMillis()</a></span> - 类 中的方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/ClockUtil.DefaultClock.html" title="com.huazheng.tunny.tools.time中的类">ClockUtil.DefaultClock</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/ClockUtil.DummyClock.html#currentTimeMillis--">currentTimeMillis()</a></span> - 类 中的方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/ClockUtil.DummyClock.html" title="com.huazheng.tunny.tools.time中的类">ClockUtil.DummyClock</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/Concurrents.html#cyclicBarrier-int-">cyclicBarrier(int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/Concurrents.html" title="com.huazheng.tunny.tools.concurrent中的类">Concurrents</a></dt>
<dd>
<div class="block">返回CyclicBarrier，每条线程减1并等待，减到0时，所有线程继续运行</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-2.html">上一个字母</a></li>
<li><a href="index-4.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-3.html" target="_top">框架</a></li>
<li><a href="index-3.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
