<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:36 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>S - 索引</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="S - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-18.html">上一个字母</a></li>
<li><a href="index-20.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-19.html" target="_top">框架</a></li>
<li><a href="index-19.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a name="I:S">
<!--   -->
</a>
<h2 class="title">S</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtil.html#safeRunnable-java.lang.Runnable-">safeRunnable(Runnable)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtil.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolUtil</a></dt>
<dd>
<div class="block">防止用户没有捕捉异常导致中断了线程池中的线程, 使得SchedulerService无法继续执行.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/Concurrents.html#sampler-double-">sampler(double)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/Concurrents.html" title="com.huazheng.tunny.tools.concurrent中的类">Concurrents</a></dt>
<dd>
<div class="block">返回采样器.</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/Sampler.html" title="com.huazheng.tunny.tools.concurrent中的类"><span class="typeNameLink">Sampler</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/package-summary.html">com.huazheng.tunny.tools.concurrent</a>中的类</dt>
<dd>
<div class="block">采样器
 
 移植 Twitter Common, 优化使用ThreadLocalRandom
 
 https://github.com/twitter/commons/blob/master/src/java/com/twitter/common/util/Sampler.java</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/Sampler.html#Sampler--">Sampler()</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/Sampler.html" title="com.huazheng.tunny.tools.concurrent中的类">Sampler</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/Sampler.html#Sampler-double-">Sampler(double)</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/Sampler.html" title="com.huazheng.tunny.tools.concurrent中的类">Sampler</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/Sampler.AlwaysSampler.html" title="com.huazheng.tunny.tools.concurrent中的类"><span class="typeNameLink">Sampler.AlwaysSampler</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/package-summary.html">com.huazheng.tunny.tools.concurrent</a>中的类</dt>
<dd>
<div class="block">采样率为100时，总是返回true</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/Sampler.NeverSampler.html" title="com.huazheng.tunny.tools.concurrent中的类"><span class="typeNameLink">Sampler.NeverSampler</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/package-summary.html">com.huazheng.tunny.tools.concurrent</a>中的类</dt>
<dd>
<div class="block">采样率为0时，总是返回false</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/SamplerTest.html" title="com.huazheng.tunny.tools.concurrent中的类"><span class="typeNameLink">SamplerTest</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/package-summary.html">com.huazheng.tunny.tools.concurrent</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/SamplerTest.html#SamplerTest--">SamplerTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/SamplerTest.html" title="com.huazheng.tunny.tools.concurrent中的类">SamplerTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.html#scheduledPool--">scheduledPool()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilderTest.html#scheduledPool--">scheduledPool()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilderTest.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilderTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.ScheduledThreadPoolBuilder.html#ScheduledThreadPoolBuilder--">ScheduledThreadPoolBuilder()</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.ScheduledThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.ScheduledThreadPoolBuilder</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#secureRandom--">secureRandom()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">使用性能更好的SHA1PRNG, Tomcat的sessionId生成也用此算法.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/Sampler.AlwaysSampler.html#select--">select()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/Sampler.AlwaysSampler.html" title="com.huazheng.tunny.tools.concurrent中的类">Sampler.AlwaysSampler</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/Sampler.NeverSampler.html#select--">select()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/Sampler.NeverSampler.html" title="com.huazheng.tunny.tools.concurrent中的类">Sampler.NeverSampler</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/Sampler.html#select--">select()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/Sampler.html" title="com.huazheng.tunny.tools.concurrent中的类">Sampler</a></dt>
<dd>
<div class="block">判断当前请求是否命中采样</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/SortedArrayList.html#set-int-E-">set(int, E)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.<a href="../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="com.huazheng.tunny.tools.collection.type中的类">SortedArrayList</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Student.html#setAge-int-">setAge(int)</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Student.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.Student</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.StudentVO.html#setAge-int-">setAge(int)</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.StudentVO.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.StudentVO</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Student.html#setCourse-java.util.List-">setCourse(List&lt;String&gt;)</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Student.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.Student</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.StudentVO.html#setCourse-java.util.List-">setCourse(List&lt;String&gt;)</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.StudentVO.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.StudentVO</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html#setDaemon-java.lang.Boolean-">setDaemon(Boolean)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.CachedThreadPoolBuilder</a></dt>
<dd>
<div class="block">与threadFactory互斥, 优先使用ThreadFactory
 
 默认为NULL，不进行设置，使用JDK的默认值.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.FixedThreadPoolBuilder.html#setDaemon-java.lang.Boolean-">setDaemon(Boolean)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.FixedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.FixedThreadPoolBuilder</a></dt>
<dd>
<div class="block">与threadFactory互斥, 优先使用ThreadFactory
 
 默认为NULL，不进行设置，使用JDK的默认值.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.QueuableCachedThreadPoolBuilder.html#setDaemon-java.lang.Boolean-">setDaemon(Boolean)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.QueuableCachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.QueuableCachedThreadPoolBuilder</a></dt>
<dd>
<div class="block">与threadFactory互斥, 优先使用ThreadFactory
 
 默认为NULL，不进行设置，使用JDK的默认值.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtilTest.html#setDay--">setDay()</a></span> - 类 中的方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtilTest.html" title="com.huazheng.tunny.tools.time中的类">DateUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#setDays-java.util.Date-int-">setDays(Date, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">设置日期, 1-31.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.TestBean.html#setDefaultValue-java.lang.String-">setDefaultValue(String)</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.TestBean.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapperTest.TestBean</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.TestBean.html#setEmptyValue-java.util.List-">setEmptyValue(List&lt;String&gt;)</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.TestBean.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapperTest.TestBean</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/ThreadDumpper.html#setEnable-boolean-">setEnable(boolean)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/ThreadDumpper.html" title="com.huazheng.tunny.tools.concurrent中的类">ThreadDumpper</a></dt>
<dd>
<div class="block">快速关闭打印</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#setField-java.lang.Object-java.lang.reflect.Field-java.lang.Object-">setField(Object, Field, Object)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtil</a></dt>
<dd>
<div class="block">使用预先获取的Field, 直接读取对象属性值, 不经过setter函数.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#setFieldValue-java.lang.Object-java.lang.String-java.lang.Object-">setFieldValue(Object, String, Object)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtil</a></dt>
<dd>
<div class="block">直接设置对象属性值, 无视private/protected修饰符, 不经过setter函数.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#setHours-java.util.Date-int-">setHours(Date, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">设置小时, 0-23.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.TestBean3.html#setId-int-">setId(int)</a></span> - 类 中的方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.TestBean3.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtilTest.TestBean3</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html#setKeepAliveSecs-int-">setKeepAliveSecs(int)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.CachedThreadPoolBuilder</a></dt>
<dd>
<div class="block">JDK默认值60秒太高，如高达1000线程时，要低于16QPS时才会开始回收线程, 因此改为默认10秒.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.QueuableCachedThreadPoolBuilder.html#setKeepAliveSecs-int-">setKeepAliveSecs(int)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.QueuableCachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.QueuableCachedThreadPoolBuilder</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/ThreadDumpper.html#setLeastInterval-int-">setLeastInterval(int)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/ThreadDumpper.html" title="com.huazheng.tunny.tools.concurrent中的类">ThreadDumpper</a></dt>
<dd>
<div class="block">打印ThreadDump的最小时间间隔，单位为秒，默认为0不限制.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html#setMaxSize-int-">setMaxSize(int)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.CachedThreadPoolBuilder</a></dt>
<dd>
<div class="block">Max默认Integer.MAX_VALUE的，建议设置</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.QueuableCachedThreadPoolBuilder.html#setMaxSize-int-">setMaxSize(int)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.QueuableCachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.QueuableCachedThreadPoolBuilder</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/ThreadDumpper.html#setMaxStackLevel-int-">setMaxStackLevel(int)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/ThreadDumpper.html" title="com.huazheng.tunny.tools.concurrent中的类">ThreadDumpper</a></dt>
<dd>
<div class="block">打印StackTrace的最大深度, 默认为8</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/type/CloneableException.html#setMessage-java.lang.String-">setMessage(String)</a></span> - 异常错误 中的方法com.huazheng.tunny.tools.base.type.<a href="../com/huazheng/tunny/tools/base/type/CloneableException.html" title="com.huazheng.tunny.tools.base.type中的类">CloneableException</a></dt>
<dd>
<div class="block">简便函数, 重新设定Message</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html#setMessage-java.lang.String-">setMessage(String)</a></span> - 异常错误 中的方法com.huazheng.tunny.tools.base.type.<a href="../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html" title="com.huazheng.tunny.tools.base.type中的类">CloneableRuntimeException</a></dt>
<dd>
<div class="block">简便函数, 重新设定Message</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#setMilliseconds-java.util.Date-int-">setMilliseconds(Date, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">设置毫秒.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html#setMinSize-int-">setMinSize(int)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.CachedThreadPoolBuilder</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.QueuableCachedThreadPoolBuilder.html#setMinSize-int-">setMinSize(int)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.QueuableCachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.QueuableCachedThreadPoolBuilder</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#setMinutes-java.util.Date-int-">setMinutes(Date, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">设置分钟, 0-59.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#setMonths-java.util.Date-int-">setMonths(Date, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">设置月份, 1-12.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Student.html#setName-java.lang.String-">setName(String)</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Student.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.Student</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.StudentVO.html#setName-java.lang.String-">setName(String)</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.StudentVO.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.StudentVO</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Teacher.html#setName-java.lang.String-">setName(String)</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Teacher.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.Teacher</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.TeacherVO.html#setName-java.lang.String-">setName(String)</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.TeacherVO.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.TeacherVO</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.TestBean.html#setName-java.lang.String-">setName(String)</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.TestBean.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapperTest.TestBean</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/ClockUtil.DummyClock.html#setNanoTime-long-">setNanoTime(long)</a></span> - 类 中的方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/ClockUtil.DummyClock.html" title="com.huazheng.tunny.tools.time中的类">ClockUtil.DummyClock</a></dt>
<dd>
<div class="block">设置nanotime.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.TestBean.html#setNullValue-java.lang.String-">setNullValue(String)</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.TestBean.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapperTest.TestBean</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.ControllableQueue.html#setParent-com.huazheng.tunny.tools.concurrent.threadpool.QueuableCachedThreadPool-">setParent(QueuableCachedThreadPool)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.ControllableQueue.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">QueuableCachedThreadPool.ControllableQueue</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.FixedThreadPoolBuilder.html#setPoolSize-int-">setPoolSize(int)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.FixedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.FixedThreadPoolBuilder</a></dt>
<dd>
<div class="block">Pool大小，默认为1，即singleThreadPool</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.ScheduledThreadPoolBuilder.html#setPoolSize-int-">setPoolSize(int)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.ScheduledThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.ScheduledThreadPoolBuilder</a></dt>
<dd>
<div class="block">默认为1</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.ListenableProperties.html#setProperty-java.lang.String-java.lang.String-">setProperty(String, String)</a></span> - 类 中的方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.ListenableProperties.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtil.ListenableProperties</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#setProperty-java.lang.Object-java.lang.String-java.lang.Object-">setProperty(Object, String, Object)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtil</a></dt>
<dd>
<div class="block">先尝试用Setter函数写入, 如果不存在则直接写入变量, 按传入value的类型匹配函数.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.TestBean.html#setPublicField-int-">setPublicField(int)</a></span> - 类 中的方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.TestBean.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtilTest.TestBean</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.FixedThreadPoolBuilder.html#setQueueSize-int-">setQueueSize(int)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.FixedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.FixedThreadPoolBuilder</a></dt>
<dd>
<div class="block">不设置时默认为-1, 使用无限长的LinkedBlockingQueue.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.QueuableCachedThreadPoolBuilder.html#setQueueSize-int-">setQueueSize(int)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.QueuableCachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.QueuableCachedThreadPoolBuilder</a></dt>
<dd>
<div class="block">LinkedQueue长度, 默认100</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html#setRejectHanlder-java.util.concurrent.RejectedExecutionHandler-">setRejectHanlder(RejectedExecutionHandler)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.CachedThreadPoolBuilder</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.FixedThreadPoolBuilder.html#setRejectHanlder-java.util.concurrent.RejectedExecutionHandler-">setRejectHanlder(RejectedExecutionHandler)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.FixedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.FixedThreadPoolBuilder</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.QueuableCachedThreadPoolBuilder.html#setRejectHanlder-java.util.concurrent.RejectedExecutionHandler-">setRejectHanlder(RejectedExecutionHandler)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.QueuableCachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.QueuableCachedThreadPoolBuilder</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#setSeconds-java.util.Date-int-">setSeconds(Date, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">设置秒, 0-59.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/ExceptionUtil.html#setStackTrace-T-java.lang.Class-java.lang.String-">setStackTrace(T, Class&lt;?&gt;, String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/ExceptionUtil.html" title="com.huazheng.tunny.tools.base中的类">ExceptionUtil</a></dt>
<dd>
<div class="block">copy from Netty, 为静态异常设置StackTrace.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/type/CloneableException.html#setStackTrace-java.lang.Class-java.lang.String-">setStackTrace(Class&lt;?&gt;, String)</a></span> - 异常错误 中的方法com.huazheng.tunny.tools.base.type.<a href="../com/huazheng/tunny/tools/base/type/CloneableException.html" title="com.huazheng.tunny.tools.base.type中的类">CloneableException</a></dt>
<dd>
<div class="block">简便函数，定义静态异常时使用</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html#setStackTrace-java.lang.Class-java.lang.String-">setStackTrace(Class&lt;?&gt;, String)</a></span> - 异常错误 中的方法com.huazheng.tunny.tools.base.type.<a href="../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html" title="com.huazheng.tunny.tools.base.type中的类">CloneableRuntimeException</a></dt>
<dd>
<div class="block">简便函数，定义静态异常时使用</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Student.html#setTeacher-com.huazheng.tunny.tools.mapper.BeanMapperTest.Teacher-">setTeacher(BeanMapperTest.Teacher)</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Student.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.Student</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.StudentVO.html#setTeacher-com.huazheng.tunny.tools.mapper.BeanMapperTest.TeacherVO-">setTeacher(BeanMapperTest.TeacherVO)</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.StudentVO.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.StudentVO</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html#setThreadFactory-java.util.concurrent.ThreadFactory-">setThreadFactory(ThreadFactory)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.CachedThreadPoolBuilder</a></dt>
<dd>
<div class="block">与threadNamePrefix互斥, 优先使用ThreadFactory</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.FixedThreadPoolBuilder.html#setThreadFactory-java.util.concurrent.ThreadFactory-">setThreadFactory(ThreadFactory)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.FixedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.FixedThreadPoolBuilder</a></dt>
<dd>
<div class="block">与threadNamePrefix互斥, 优先使用ThreadFactory</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.QueuableCachedThreadPoolBuilder.html#setThreadFactory-java.util.concurrent.ThreadFactory-">setThreadFactory(ThreadFactory)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.QueuableCachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.QueuableCachedThreadPoolBuilder</a></dt>
<dd>
<div class="block">与threadNamePrefix互斥, 优先使用ThreadFactory</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.ScheduledThreadPoolBuilder.html#setThreadFactory-java.util.concurrent.ThreadFactory-">setThreadFactory(ThreadFactory)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.ScheduledThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.ScheduledThreadPoolBuilder</a></dt>
<dd>
<div class="block">与threadNamePrefix互斥, 优先使用ThreadFactory</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html#setThreadNamePrefix-java.lang.String-">setThreadNamePrefix(String)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.CachedThreadPoolBuilder</a></dt>
<dd>
<div class="block">与threadFactory互斥, 优先使用ThreadFactory</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.FixedThreadPoolBuilder.html#setThreadNamePrefix-java.lang.String-">setThreadNamePrefix(String)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.FixedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.FixedThreadPoolBuilder</a></dt>
<dd>
<div class="block">与ThreadFactory互斥, 优先使用ThreadFactory</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.QueuableCachedThreadPoolBuilder.html#setThreadNamePrefix-java.lang.String-">setThreadNamePrefix(String)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.QueuableCachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.QueuableCachedThreadPoolBuilder</a></dt>
<dd>
<div class="block">与threadFactory互斥, 优先使用ThreadFactory</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.ScheduledThreadPoolBuilder.html#setThreadNamePrefix-java.lang.String-">setThreadNamePrefix(String)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.ScheduledThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.ScheduledThreadPoolBuilder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/collection/SetUtil.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">SetUtil</span></a> - <a href="../com/huazheng/tunny/tools/collection/package-summary.html">com.huazheng.tunny.tools.collection</a>中的类</dt>
<dd>
<div class="block">关于Set的工具集合.
 
 1.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/SetUtil.html#SetUtil--">SetUtil()</a></span> - 类 的构造器com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/SetUtil.html" title="com.huazheng.tunny.tools.collection中的类">SetUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/collection/SetUtilTest.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">SetUtilTest</span></a> - <a href="../com/huazheng/tunny/tools/collection/package-summary.html">com.huazheng.tunny.tools.collection</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/SetUtilTest.html#SetUtilTest--">SetUtilTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/SetUtilTest.html" title="com.huazheng.tunny.tools.collection中的类">SetUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectMap.PrimitiveEntry.html#setValue-V-">setValue(V)</a></span> - 接口 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectMap.PrimitiveEntry.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口">IntObjectMap.PrimitiveEntry</a></dt>
<dd>
<div class="block">Sets the value for this entry.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.PrimitiveEntry.html#setValue-V-">setValue(V)</a></span> - 接口 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.PrimitiveEntry.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口">LongObjectMap.PrimitiveEntry</a></dt>
<dd>
<div class="block">Sets the value for this entry.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#setYears-java.util.Date-int-">setYears(Date, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">设置年份, 公元纪年.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.BClass.html#sfield">sfield</a></span> - 类 中的变量com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.BClass.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtilTest.BClass</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/HashUtil.html#sha1-byte:A-">sha1(byte[])</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/HashUtil.html" title="com.huazheng.tunny.tools.text中的类">HashUtil</a></dt>
<dd>
<div class="block">对输入字符串进行sha1散列.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/HashUtil.html#sha1-java.lang.String-">sha1(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/HashUtil.html" title="com.huazheng.tunny.tools.text中的类">HashUtil</a></dt>
<dd>
<div class="block">对输入字符串进行sha1散列, 编码默认为UTF8.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/HashUtil.html#sha1-byte:A-byte:A-">sha1(byte[], byte[])</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/HashUtil.html" title="com.huazheng.tunny.tools.text中的类">HashUtil</a></dt>
<dd>
<div class="block">对输入字符串进行sha1散列，带salt达到更高的安全性.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/HashUtil.html#sha1-java.lang.String-byte:A-">sha1(String, byte[])</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/HashUtil.html" title="com.huazheng.tunny.tools.text中的类">HashUtil</a></dt>
<dd>
<div class="block">对输入字符串进行sha1散列，带salt达到更高的安全性.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/HashUtil.html#sha1-byte:A-byte:A-int-">sha1(byte[], byte[], int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/HashUtil.html" title="com.huazheng.tunny.tools.text中的类">HashUtil</a></dt>
<dd>
<div class="block">对输入字符串进行sha1散列，带salt而且迭代达到更高更高的安全性.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/HashUtil.html#sha1-java.lang.String-byte:A-int-">sha1(String, byte[], int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/HashUtil.html" title="com.huazheng.tunny.tools.text中的类">HashUtil</a></dt>
<dd>
<div class="block">对输入字符串进行sha1散列，带salt而且迭代达到更高更高的安全性.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/HashUtil.html#sha1File-java.io.InputStream-">sha1File(InputStream)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/HashUtil.html" title="com.huazheng.tunny.tools.text中的类">HashUtil</a></dt>
<dd>
<div class="block">对文件进行sha1散列.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ArrayUtil.html#shuffle-T:A-">shuffle(T[])</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ArrayUtil.html" title="com.huazheng.tunny.tools.collection中的类">ArrayUtil</a></dt>
<dd>
<div class="block">将传入的数组乱序</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ArrayUtil.html#shuffle-T:A-java.util.Random-">shuffle(T[], Random)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ArrayUtil.html" title="com.huazheng.tunny.tools.collection中的类">ArrayUtil</a></dt>
<dd>
<div class="block">将传入的数组乱序</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ArrayUtilTest.html#shuffle--">shuffle()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ArrayUtilTest.html" title="com.huazheng.tunny.tools.collection中的类">ArrayUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtil.html#shuffle-java.util.List-">shuffle(List&lt;?&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtil.html" title="com.huazheng.tunny.tools.collection中的类">ListUtil</a></dt>
<dd>
<div class="block">随机乱序，使用默认的Random.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtil.html#shuffle-java.util.List-java.util.Random-">shuffle(List&lt;?&gt;, Random)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtil.html" title="com.huazheng.tunny.tools.collection中的类">ListUtil</a></dt>
<dd>
<div class="block">随机乱序，使用传入的Random.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FilePathUtil.html#simplifyPath-java.lang.String-">simplifyPath(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FilePathUtil.html" title="com.huazheng.tunny.tools.io中的类">FilePathUtil</a></dt>
<dd>
<div class="block">将路径整理，如 "a/..</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtil.html#singletonList-T-">singletonList(T)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtil.html" title="com.huazheng.tunny.tools.collection中的类">ListUtil</a></dt>
<dd>
<div class="block">返回只含一个元素但结构特殊的List，节约空间.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtil.html#singletonMap-K-V-">singletonMap(K, V)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtil.html" title="com.huazheng.tunny.tools.collection中的类">MapUtil</a></dt>
<dd>
<div class="block">返回一个只含一个元素但结构特殊的Map，节约空间.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/SetUtil.html#singletonSet-T-">singletonSet(T)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/SetUtil.html" title="com.huazheng.tunny.tools.collection中的类">SetUtil</a></dt>
<dd>
<div class="block">返回只含一个元素但结构特殊的Set，节约空间.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html#size--">size()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.<a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="com.huazheng.tunny.tools.collection.type中的类">ConcurrentHashSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html#size--">size()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">IntObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#size--">size()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">LongObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/number/SizeUnit.html" title="com.huazheng.tunny.tools.number中的枚举"><span class="typeNameLink">SizeUnit</span></a> - <a href="../com/huazheng/tunny/tools/number/package-summary.html">com.huazheng.tunny.tools.number</a>中的枚举</dt>
<dd>
<div class="block">Representation of basic size units，just like TimeUnit.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/ThreadUtil.html#sleep-long-">sleep(long)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/ThreadUtil.html" title="com.huazheng.tunny.tools.concurrent中的类">ThreadUtil</a></dt>
<dd>
<div class="block">sleep等待, 单位为毫秒, 已捕捉并处理InterruptedException.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/ThreadUtil.html#sleep-long-java.util.concurrent.TimeUnit-">sleep(long, TimeUnit)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/ThreadUtil.html" title="com.huazheng.tunny.tools.concurrent中的类">ThreadUtil</a></dt>
<dd>
<div class="block">sleep等待，已捕捉并处理InterruptedException.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtil.html#sort-java.util.List-">sort(List&lt;T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtil.html" title="com.huazheng.tunny.tools.collection中的类">ListUtil</a></dt>
<dd>
<div class="block">升序排序, 采用JDK认为最优的排序算法, 使用元素自身的compareTo()方法</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtil.html#sort-java.util.List-java.util.Comparator-">sort(List&lt;T&gt;, Comparator&lt;? super T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtil.html" title="com.huazheng.tunny.tools.collection中的类">ListUtil</a></dt>
<dd>
<div class="block">升序排序, 采用JDK认为最优的排序算法, 使用Comparetor.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtilTest.html#sortAndSearch--">sortAndSearch()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtilTest.html" title="com.huazheng.tunny.tools.collection中的类">ListUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtilTest.html#sortAndTop--">sortAndTop()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtilTest.html" title="com.huazheng.tunny.tools.collection中的类">MapUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtil.html#sortByValue-java.util.Map-boolean-">sortByValue(Map&lt;K, V&gt;, boolean)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtil.html" title="com.huazheng.tunny.tools.collection中的类">MapUtil</a></dt>
<dd>
<div class="block">对一个Map按Value进行排序，返回排序LinkedHashMap，多用于Value是Counter的情况.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtil.html#sortByValue-java.util.Map-java.util.Comparator-">sortByValue(Map&lt;K, V&gt;, Comparator&lt;? super V&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtil.html" title="com.huazheng.tunny.tools.collection中的类">MapUtil</a></dt>
<dd>
<div class="block">对一个Map按Value进行排序，返回排序LinkedHashMap.</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="com.huazheng.tunny.tools.collection.type中的类"><span class="typeNameLink">SortedArrayList</span></a>&lt;<a href="../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&gt; - <a href="../com/huazheng/tunny/tools/collection/type/package-summary.html">com.huazheng.tunny.tools.collection.type</a>中的类</dt>
<dd>
<div class="block">从Jodd整体复制，部分指定了index的操作不支持，如 add(index, element)
 
 修改包括：改进Comparator泛型定义，findInsertionPoint的位移改进
 
 https://github.com/oblac/jodd/blob/master/jodd-core/src/main/java/jodd/util/collection/SortedArrayList.java
 
 An extension of <code>ArrayList</code> that insures that all of the items
 added are sorted.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/SortedArrayList.html#SortedArrayList-java.util.Comparator-">SortedArrayList(Comparator&lt;? super E&gt;)</a></span> - 类 的构造器com.huazheng.tunny.tools.collection.type.<a href="../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="com.huazheng.tunny.tools.collection.type中的类">SortedArrayList</a></dt>
<dd>
<div class="block">Constructs a new <code>SortedArrayList</code>.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/SortedArrayList.html#SortedArrayList--">SortedArrayList()</a></span> - 类 的构造器com.huazheng.tunny.tools.collection.type.<a href="../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="com.huazheng.tunny.tools.collection.type中的类">SortedArrayList</a></dt>
<dd>
<div class="block">Constructs a new <code>SortedArrayList</code> expecting
 elements are comparable.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/SortedArrayList.html#SortedArrayList-java.util.Collection-">SortedArrayList(Collection&lt;? extends E&gt;)</a></span> - 类 的构造器com.huazheng.tunny.tools.collection.type.<a href="../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="com.huazheng.tunny.tools.collection.type中的类">SortedArrayList</a></dt>
<dd>
<div class="block">Constructs a new <code>SortedArrayList</code> expecting
 elements are comparable.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/SortedArrayListTest.html#sortedArrayList--">sortedArrayList()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.<a href="../com/huazheng/tunny/tools/collection/type/SortedArrayListTest.html" title="com.huazheng.tunny.tools.collection.type中的类">SortedArrayListTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/collection/type/SortedArrayListTest.html" title="com.huazheng.tunny.tools.collection.type中的类"><span class="typeNameLink">SortedArrayListTest</span></a> - <a href="../com/huazheng/tunny/tools/collection/type/package-summary.html">com.huazheng.tunny.tools.collection.type</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/SortedArrayListTest.html#SortedArrayListTest--">SortedArrayListTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.collection.type.<a href="../com/huazheng/tunny/tools/collection/type/SortedArrayListTest.html" title="com.huazheng.tunny.tools.collection.type中的类">SortedArrayListTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtil.html#sortReverse-java.util.List-">sortReverse(List&lt;T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtil.html" title="com.huazheng.tunny.tools.collection中的类">ListUtil</a></dt>
<dd>
<div class="block">倒序排序, 采用JDK认为最优的排序算法,使用元素自身的compareTo()方法</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtil.html#sortReverse-java.util.List-java.util.Comparator-">sortReverse(List&lt;T&gt;, Comparator&lt;? super T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtil.html" title="com.huazheng.tunny.tools.collection中的类">ListUtil</a></dt>
<dd>
<div class="block">倒序排序, 采用JDK认为最优的排序算法, 使用Comparator</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/CsvUtil.html#SPACE">SPACE</a></span> - 类 中的静态变量com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/CsvUtil.html" title="com.huazheng.tunny.tools.text中的类">CsvUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/CsvUtil.html#SPECIAL_CHARS">SPECIAL_CHARS</a></span> - 类 中的静态变量com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/CsvUtil.html" title="com.huazheng.tunny.tools.text中的类">CsvUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/MoreStringUtil.html#split-java.lang.String-char-">split(String, char)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/MoreStringUtil.html" title="com.huazheng.tunny.tools.text中的类">MoreStringUtil</a></dt>
<dd>
<div class="block">高性能的Split，针对char的分隔符号，比JDK String自带的高效.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/MoreStringUtil.html#split-java.lang.String-char-int-">split(String, char, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/MoreStringUtil.html" title="com.huazheng.tunny.tools.text中的类">MoreStringUtil</a></dt>
<dd>
<div class="block">高性能的Split，针对char的分隔符号，比JDK String自带的高效.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/MoreStringUtilTest.html#split--">split()</a></span> - 类 中的方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/MoreStringUtilTest.html" title="com.huazheng.tunny.tools.text中的类">MoreStringUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/MathUtil.html#sqrt-int-java.math.RoundingMode-">sqrt(int, RoundingMode)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/MathUtil.html" title="com.huazheng.tunny.tools.number中的类">MathUtil</a></dt>
<dd>
<div class="block">开方</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/MathUtil.html#sqrt-long-java.math.RoundingMode-">sqrt(long, RoundingMode)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/MathUtil.html" title="com.huazheng.tunny.tools.number中的类">MathUtil</a></dt>
<dd>
<div class="block">开方</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/QueueUtilTest.html#stack--">stack()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/QueueUtilTest.html" title="com.huazheng.tunny.tools.collection中的类">QueueUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/ExceptionUtil.html#stackTraceText-java.lang.Throwable-">stackTraceText(Throwable)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/ExceptionUtil.html" title="com.huazheng.tunny.tools.base中的类">ExceptionUtil</a></dt>
<dd>
<div class="block">将StackTrace[]转换为String, 供Logger或e.printStackTrace()外的其他地方使用.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/vip/vjtools/test/rule/TestProgress.html#starting-org.junit.runner.Description-">starting(Description)</a></span> - 类 中的方法com.vip.vjtools.test.rule.<a href="../com/vip/vjtools/test/rule/TestProgress.html" title="com.vip.vjtools.test.rule中的类">TestProgress</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/MoreStringUtil.html#startWith-java.lang.CharSequence-char-">startWith(CharSequence, char)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/MoreStringUtil.html" title="com.huazheng.tunny.tools.text中的类">MoreStringUtil</a></dt>
<dd>
<div class="block">判断字符串是否以字母开头
 
 如果字符串为Null或空，返回false</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/ExceptionUtilTest.html#staticException--">staticException()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/ExceptionUtilTest.html" title="com.huazheng.tunny.tools.base中的类">ExceptionUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/ValueValidator.Validator.html#STRICT_BOOL_VALUE_VALIDATOR">STRICT_BOOL_VALUE_VALIDATOR</a></span> - 接口 中的静态变量com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/ValueValidator.Validator.html" title="com.huazheng.tunny.tools.base中的接口">ValueValidator.Validator</a></dt>
<dd>
<div class="block">校验器: BOOL字符串较验</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/ValueValidator.Validator.html#STRING_EMPTY_VALUE_VALIDATOR">STRING_EMPTY_VALUE_VALIDATOR</a></span> - 接口 中的静态变量com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/ValueValidator.Validator.html" title="com.huazheng.tunny.tools.base中的接口">ValueValidator.Validator</a></dt>
<dd>
<div class="block">校验器: 字符串不为空串较验</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/net/IPUtilTest.html#stringAndInt--">stringAndInt()</a></span> - 类 中的方法com.huazheng.tunny.tools.net.<a href="../com/huazheng/tunny/tools/net/IPUtilTest.html" title="com.huazheng.tunny.tools.net中的类">IPUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/text/StringBuilderHolder.html" title="com.huazheng.tunny.tools.text中的类"><span class="typeNameLink">StringBuilderHolder</span></a> - <a href="../com/huazheng/tunny/tools/text/package-summary.html">com.huazheng.tunny.tools.text</a>中的类</dt>
<dd>
<div class="block">参考Netty的InternalThreadLocalMap 与 BigDecimal, 放在threadLocal中重用的StringBuilder, 节约StringBuilder内部的char[]
 
 参考文章：《StringBuilder在高性能场景下的正确用法》http://calvin1978.blogcn.com/articles/stringbuilder.html
 
 不过仅在String对象较大时才有明显效果，否则抵不上访问ThreadLocal的消耗.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/StringBuilderHolder.html#StringBuilderHolder-int-">StringBuilderHolder(int)</a></span> - 类 的构造器com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/StringBuilderHolder.html" title="com.huazheng.tunny.tools.text中的类">StringBuilderHolder</a></dt>
<dd>
<div class="block">创建独立的Holder.</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/text/StringBuilderHolderTest.html" title="com.huazheng.tunny.tools.text中的类"><span class="typeNameLink">StringBuilderHolderTest</span></a> - <a href="../com/huazheng/tunny/tools/text/package-summary.html">com.huazheng.tunny.tools.text</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/StringBuilderHolderTest.html#StringBuilderHolderTest--">StringBuilderHolderTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/StringBuilderHolderTest.html" title="com.huazheng.tunny.tools.text中的类">StringBuilderHolderTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html" title="com.huazheng.tunny.tools.io.type中的类"><span class="typeNameLink">StringBuilderWriter</span></a> - <a href="../com/huazheng/tunny/tools/io/type/package-summary.html">com.huazheng.tunny.tools.io.type</a>中的类</dt>
<dd>
<div class="block">JDK的java.io.StringWriter使用StringBuffer，移植Commons IO使用StringBuilder的版本.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html#StringBuilderWriter--">StringBuilderWriter()</a></span> - 类 的构造器com.huazheng.tunny.tools.io.type.<a href="../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html" title="com.huazheng.tunny.tools.io.type中的类">StringBuilderWriter</a></dt>
<dd>
<div class="block">Constructs a new <code>StringBuilder</code> instance with default capacity.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html#StringBuilderWriter-int-">StringBuilderWriter(int)</a></span> - 类 的构造器com.huazheng.tunny.tools.io.type.<a href="../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html" title="com.huazheng.tunny.tools.io.type中的类">StringBuilderWriter</a></dt>
<dd>
<div class="block">Constructs a new <code>StringBuilder</code> instance with the specified capacity.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html#StringBuilderWriter-java.lang.StringBuilder-">StringBuilderWriter(StringBuilder)</a></span> - 类 的构造器com.huazheng.tunny.tools.io.type.<a href="../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html" title="com.huazheng.tunny.tools.io.type中的类">StringBuilderWriter</a></dt>
<dd>
<div class="block">Constructs a new instance with the specified <code>StringBuilder</code>.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtilTest.html#stringSystemProperty--">stringSystemProperty()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtilTest.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/jsr166e/Striped64.html" title="com.huazheng.tunny.tools.concurrent.jsr166e中的类"><span class="typeNameLink">Striped64</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/jsr166e/package-summary.html">com.huazheng.tunny.tools.concurrent.jsr166e</a>中的类</dt>
<dd>
<div class="block">移植
 http://gee.cs.oswego.edu/cgi-bin/viewcvs.cgi/jsr166/src/jsr166e/Striped64.java Revision 1.10
 
 A package-local class holding common representation and mechanics
 for classes supporting dynamic striping on 64bit values.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Student.html#Student--">Student()</a></span> - 类 的构造器com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Student.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.Student</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Student.html#Student-java.lang.String-int-com.huazheng.tunny.tools.mapper.BeanMapperTest.Teacher-java.util.List-">Student(String, int, BeanMapperTest.Teacher, List&lt;String&gt;)</a></span> - 类 的构造器com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Student.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.Student</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.StudentVO.html#StudentVO--">StudentVO()</a></span> - 类 的构造器com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.StudentVO.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.StudentVO</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.StudentVO.html#StudentVO-java.lang.String-int-com.huazheng.tunny.tools.mapper.BeanMapperTest.TeacherVO-java.util.List-">StudentVO(String, int, BeanMapperTest.TeacherVO, List&lt;String&gt;)</a></span> - 类 的构造器com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.StudentVO.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.StudentVO</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#subDays-java.util.Date-int-">subDays(Date, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">减一天</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#subHours-java.util.Date-int-">subHours(Date, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">减一小时</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#subMinutes-java.util.Date-int-">subMinutes(Date, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">减一分钟</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#subMonths-java.util.Date-int-">subMonths(Date, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">减一月</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#subSeconds-java.util.Date-int-">subSeconds(Date, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">减一秒.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#subWeeks-java.util.Date-int-">subWeeks(Date, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">减一周</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#sum--">sum()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.jsr166e.<a href="../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html" title="com.huazheng.tunny.tools.concurrent.jsr166e中的类">LongAdder</a></dt>
<dd>
<div class="block">Returns the current sum.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#sumThenReset--">sumThenReset()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.jsr166e.<a href="../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html" title="com.huazheng.tunny.tools.concurrent.jsr166e中的类">LongAdder</a></dt>
<dd>
<div class="block">Equivalent in effect to <a href="../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#sum--"><code>LongAdder.sum()</code></a> followed by <a href="../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#reset--"><code>LongAdder.reset()</code></a>.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtil.html#synchronizedList-java.util.List-">synchronizedList(List&lt;T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtil.html" title="com.huazheng.tunny.tools.collection中的类">ListUtil</a></dt>
<dd>
<div class="block">返回包装后同步的List，所有方法都会被synchronized原语同步.</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">SystemPropertiesUtil</span></a> - <a href="../com/huazheng/tunny/tools/base/package-summary.html">com.huazheng.tunny.tools.base</a>中的类</dt>
<dd>
<div class="block">关于SystemProperties的工具类
 
 1.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#SystemPropertiesUtil--">SystemPropertiesUtil()</a></span> - 类 的构造器com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.ListenableProperties.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">SystemPropertiesUtil.ListenableProperties</span></a> - <a href="../com/huazheng/tunny/tools/base/package-summary.html">com.huazheng.tunny.tools.base</a>中的类</dt>
<dd>
<div class="block">Properties 本质上是一个HashTable，每次读写都会加锁，所以不支持频繁的System.getProperty(name)来检查系统内容变化 因此扩展了Properties子类,
 在其所关心的属性变化时进行通知.</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.PropertiesListener.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">SystemPropertiesUtil.PropertiesListener</span></a> - <a href="../com/huazheng/tunny/tools/base/package-summary.html">com.huazheng.tunny.tools.base</a>中的类</dt>
<dd>
<div class="block">获取所关心的Property变更的Listener基类.</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtilTest.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">SystemPropertiesUtilTest</span></a> - <a href="../com/huazheng/tunny/tools/base/package-summary.html">com.huazheng.tunny.tools.base</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtilTest.html#SystemPropertiesUtilTest--">SystemPropertiesUtilTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtilTest.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtilTest.TestPropertiesListener.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">SystemPropertiesUtilTest.TestPropertiesListener</span></a> - <a href="../com/huazheng/tunny/tools/base/package-summary.html">com.huazheng.tunny.tools.base</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtilTest.html#systemProperty--">systemProperty()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtilTest.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtilTest</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-18.html">上一个字母</a></li>
<li><a href="index-20.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-19.html" target="_top">框架</a></li>
<li><a href="index-19.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
