<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:36 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>R - 索引</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="R - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-17.html">上一个字母</a></li>
<li><a href="index-19.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-18.html" target="_top">框架</a></li>
<li><a href="index-18.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a name="I:R">
<!--   -->
</a>
<h2 class="title">R</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#randomAsciiFixLength-int-">randomAsciiFixLength(int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">随机ASCII字符(含字母，数字及其他符号)，固定长度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#randomAsciiFixLength-java.util.Random-int-">randomAsciiFixLength(Random, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">随机ASCII字符(含字母，数字及其他符号)，固定长度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#randomAsciiRandomLength-int-int-">randomAsciiRandomLength(int, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">随机ASCII字符(含字母，数字及其他符号)，随机长度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#randomAsciiRandomLength-java.util.Random-int-int-">randomAsciiRandomLength(Random, int, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">随机ASCII字符(含字母，数字及其他符号)，随机长度</div>
</dd>
<dt><a href="../com/vip/vjtools/test/data/RandomData.html" title="com.vip.vjtools.test.data中的类"><span class="typeNameLink">RandomData</span></a> - <a href="../com/vip/vjtools/test/data/package-summary.html">com.vip.vjtools.test.data</a>中的类</dt>
<dd>
<div class="block">随机测试数据生成工具类.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/vip/vjtools/test/data/RandomData.html#RandomData--">RandomData()</a></span> - 类 的构造器com.vip.vjtools.test.data.<a href="../com/vip/vjtools/test/data/RandomData.html" title="com.vip.vjtools.test.data中的类">RandomData</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/vip/vjtools/test/data/RandomData.html#randomId--">randomId()</a></span> - 类 中的静态方法com.vip.vjtools.test.data.<a href="../com/vip/vjtools/test/data/RandomData.html" title="com.vip.vjtools.test.data中的类">RandomData</a></dt>
<dd>
<div class="block">返回随机ID.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#randomLetterFixLength-int-">randomLetterFixLength(int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">随机字母，固定长度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#randomLetterFixLength-java.util.Random-int-">randomLetterFixLength(Random, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">随机字母，固定长度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#randomLetterRandomLength-int-int-">randomLetterRandomLength(int, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">随机字母，随机长度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#randomLetterRandomLength-java.util.Random-int-int-">randomLetterRandomLength(Random, int, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">随机字母，随机长度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/vip/vjtools/test/data/RandomData.html#randomName-java.lang.String-">randomName(String)</a></span> - 类 中的静态方法com.vip.vjtools.test.data.<a href="../com/vip/vjtools/test/data/RandomData.html" title="com.vip.vjtools.test.data中的类">RandomData</a></dt>
<dd>
<div class="block">返回随机名称, prefix字符串+5位随机数字.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtilTest.html#randomNumber--">randomNumber()</a></span> - 类 中的方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtilTest.html" title="com.huazheng.tunny.tools.number中的类">RandomUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/vip/vjtools/test/data/RandomData.html#randomOne-java.util.List-">randomOne(List&lt;T&gt;)</a></span> - 类 中的静态方法com.vip.vjtools.test.data.<a href="../com/vip/vjtools/test/data/RandomData.html" title="com.vip.vjtools.test.data中的类">RandomData</a></dt>
<dd>
<div class="block">从输入list中随机返回一个对象.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/vip/vjtools/test/data/RandomData.html#randomSome-java.util.List-int-">randomSome(List&lt;T&gt;, int)</a></span> - 类 中的静态方法com.vip.vjtools.test.data.<a href="../com/vip/vjtools/test/data/RandomData.html" title="com.vip.vjtools.test.data中的类">RandomData</a></dt>
<dd>
<div class="block">从输入list中随机返回n个对象.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/vip/vjtools/test/data/RandomData.html#randomSome-java.util.List-">randomSome(List&lt;T&gt;)</a></span> - 类 中的静态方法com.vip.vjtools.test.data.<a href="../com/vip/vjtools/test/data/RandomData.html" title="com.vip.vjtools.test.data中的类">RandomData</a></dt>
<dd>
<div class="block">从输入list中随机返回随机个对象.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#randomStringFixLength-int-">randomStringFixLength(int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">随机字母或数字，固定长度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#randomStringFixLength-java.util.Random-int-">randomStringFixLength(Random, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">随机字母或数字，固定长度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#randomStringRandomLength-int-int-">randomStringRandomLength(int, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">随机字母或数字，随机长度</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#randomStringRandomLength-java.util.Random-int-int-">randomStringRandomLength(Random, int, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">随机字母或数字，随机长度</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类"><span class="typeNameLink">RandomUtil</span></a> - <a href="../com/huazheng/tunny/tools/number/package-summary.html">com.huazheng.tunny.tools.number</a>中的类</dt>
<dd>
<div class="block">随机数工具集.
 
 1.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#RandomUtil--">RandomUtil()</a></span> - 类 的构造器com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/number/RandomUtilTest.html" title="com.huazheng.tunny.tools.number中的类"><span class="typeNameLink">RandomUtilTest</span></a> - <a href="../com/huazheng/tunny/tools/number/package-summary.html">com.huazheng.tunny.tools.number</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtilTest.html#RandomUtilTest--">RandomUtilTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtilTest.html" title="com.huazheng.tunny.tools.number中的类">RandomUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/Concurrents.html#rateLimiter-int-">rateLimiter(int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/Concurrents.html" title="com.huazheng.tunny.tools.concurrent中的类">Concurrents</a></dt>
<dd>
<div class="block">返回令牌桶算法的RateLimiter</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/IOUtilTest.html#read--">read()</a></span> - 类 中的方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/IOUtilTest.html" title="com.huazheng.tunny.tools.io中的类">IOUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/IOUtil.html#readLine-java.io.InputStream-">readLine(InputStream)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/IOUtil.html" title="com.huazheng.tunny.tools.io中的类">IOUtil</a></dt>
<dd>
<div class="block">读取一行数据，比如System.in的用户输入</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/IOUtil.html#readLine-java.io.Reader-">readLine(Reader)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/IOUtil.html" title="com.huazheng.tunny.tools.io中的类">IOUtil</a></dt>
<dd>
<div class="block">读取一行数据</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtilTest.html#readWrite--">readWrite()</a></span> - 类 中的方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtilTest.html" title="com.huazheng.tunny.tools.io中的类">FileUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html" title="com.huazheng.tunny.tools.reflect中的类"><span class="typeNameLink">ReflectionUtil</span></a> - <a href="../com/huazheng/tunny/tools/reflect/package-summary.html">com.huazheng.tunny.tools.reflect</a>中的类</dt>
<dd>
<div class="block">反射工具类.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#ReflectionUtil--">ReflectionUtil()</a></span> - 类 的构造器com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.html" title="com.huazheng.tunny.tools.reflect中的类"><span class="typeNameLink">ReflectionUtilTest</span></a> - <a href="../com/huazheng/tunny/tools/reflect/package-summary.html">com.huazheng.tunny.tools.reflect</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.html#ReflectionUtilTest--">ReflectionUtilTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.ParentBean.html" title="com.huazheng.tunny.tools.reflect中的类"><span class="typeNameLink">ReflectionUtilTest.ParentBean</span></a>&lt;<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.ParentBean.html" title="ReflectionUtilTest.ParentBean中的类型参数">T</a>,<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.ParentBean.html" title="ReflectionUtilTest.ParentBean中的类型参数">ID</a>&gt; - <a href="../com/huazheng/tunny/tools/reflect/package-summary.html">com.huazheng.tunny.tools.reflect</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.TestBean.html" title="com.huazheng.tunny.tools.reflect中的类"><span class="typeNameLink">ReflectionUtilTest.TestBean</span></a> - <a href="../com/huazheng/tunny/tools/reflect/package-summary.html">com.huazheng.tunny.tools.reflect</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.TestBean2.html" title="com.huazheng.tunny.tools.reflect中的类"><span class="typeNameLink">ReflectionUtilTest.TestBean2</span></a> - <a href="../com/huazheng/tunny/tools/reflect/package-summary.html">com.huazheng.tunny.tools.reflect</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.TestBean3.html" title="com.huazheng.tunny.tools.reflect中的类"><span class="typeNameLink">ReflectionUtilTest.TestBean3</span></a> - <a href="../com/huazheng/tunny/tools/reflect/package-summary.html">com.huazheng.tunny.tools.reflect</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/TextValidator.html#REGEX_MOBILE_EXACT">REGEX_MOBILE_EXACT</a></span> - 类 中的静态变量com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/TextValidator.html" title="com.huazheng.tunny.tools.text中的类">TextValidator</a></dt>
<dd>
<div class="block">正则：手机号（精确）, 已知3位前缀＋8位数字
 
 移动：134(0-8)、135、136、137、138、139、147、150、151、152、157、158、159、178、182、183、184、187、188、198
 
 
 联通：130、131、132、145、155、156、166、171、175、176、185、186
 
 
 电信：133、153、173、177、180、181、189、199
 
 
 全球星：1349
 
 
 虚拟运营商：170
 </div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.ListenableProperties.html#register-com.huazheng.tunny.tools.base.SystemPropertiesUtil.PropertiesListener-">register(SystemPropertiesUtil.PropertiesListener)</a></span> - 类 中的方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.ListenableProperties.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtil.ListenableProperties</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#registerSystemPropertiesListener-com.huazheng.tunny.tools.base.SystemPropertiesUtil.PropertiesListener-">registerSystemPropertiesListener(SystemPropertiesUtil.PropertiesListener)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtil</a></dt>
<dd>
<div class="block">Properties 本质上是一个HashTable，每次读写都会加锁，所以不支持频繁的System.getProperty(name)来检查系统内容变化 因此扩展了一个ListenableProperties,
 在其所关心的属性变化时进行通知.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/AbortPolicyWithReport.html#rejectedExecution-java.lang.Runnable-java.util.concurrent.ThreadPoolExecutor-">rejectedExecution(Runnable, ThreadPoolExecutor)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/AbortPolicyWithReport.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">AbortPolicyWithReport</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html#remove-java.lang.Object-">remove(Object)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.<a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="com.huazheng.tunny.tools.collection.type中的类">ConcurrentHashSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html#remove-int-">remove(int)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">IntObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html#remove-java.lang.Object-">remove(Object)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">IntObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectMap.html#remove-int-">remove(int)</a></span> - 接口 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口">IntObjectMap</a></dt>
<dd>
<div class="block">Removes the entry with the specified key.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#remove-long-">remove(long)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">LongObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#remove-java.lang.Object-">remove(Object)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">LongObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html#remove-long-">remove(long)</a></span> - 接口 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口">LongObjectMap</a></dt>
<dd>
<div class="block">Removes the entry with the specified key.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html#removeAll-java.util.Collection-">removeAll(Collection&lt;?&gt;)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.<a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="com.huazheng.tunny.tools.collection.type中的类">ConcurrentHashSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/MoreStringUtil.html#removeEnd-java.lang.String-char-">removeEnd(String, char)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/MoreStringUtil.html" title="com.huazheng.tunny.tools.text中的类">MoreStringUtil</a></dt>
<dd>
<div class="block">如果结尾字符为c, 去除掉该字符.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/vip/vjtools/test/log/LogbackListAppender.html#removeFromLogger-java.lang.String-">removeFromLogger(String)</a></span> - 类 中的方法com.vip.vjtools.test.log.<a href="../com/vip/vjtools/test/log/LogbackListAppender.html" title="com.vip.vjtools.test.log中的类">LogbackListAppender</a></dt>
<dd>
<div class="block">将此appender从logger中移除.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/vip/vjtools/test/log/LogbackListAppender.html#removeFromLogger-java.lang.Class-">removeFromLogger(Class&lt;?&gt;)</a></span> - 类 中的方法com.vip.vjtools.test.log.<a href="../com/vip/vjtools/test/log/LogbackListAppender.html" title="com.vip.vjtools.test.log中的类">LogbackListAppender</a></dt>
<dd>
<div class="block">将此appender从logger中移除.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/vip/vjtools/test/log/LogbackListAppender.html#removeFromRootLogger--">removeFromRootLogger()</a></span> - 类 中的方法com.vip.vjtools.test.log.<a href="../com/vip/vjtools/test/log/LogbackListAppender.html" title="com.vip.vjtools.test.log中的类">LogbackListAppender</a></dt>
<dd>
<div class="block">将此appender从root logger中移除.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/MoreStringUtil.html#replaceFirst-java.lang.String-char-char-">replaceFirst(String, char, char)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/MoreStringUtil.html" title="com.huazheng.tunny.tools.text中的类">MoreStringUtil</a></dt>
<dd>
<div class="block">String 有replace(char,char)，但缺少单独replace first/last的</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/MoreStringUtil.html#replaceLast-java.lang.String-char-char-">replaceLast(String, char, char)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/MoreStringUtil.html" title="com.huazheng.tunny.tools.text中的类">MoreStringUtil</a></dt>
<dd>
<div class="block">String 有replace(char,char)替换全部char，但缺少单独replace first/last</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#reset--">reset()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.jsr166e.<a href="../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html" title="com.huazheng.tunny.tools.concurrent.jsr166e中的类">LongAdder</a></dt>
<dd>
<div class="block">Resets variables maintaining the sum to zero.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/type/ThreadLocalContext.html#reset--">reset()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.concurrent.type.<a href="../com/huazheng/tunny/tools/concurrent/type/ThreadLocalContext.html" title="com.huazheng.tunny.tools.concurrent.type中的类">ThreadLocalContext</a></dt>
<dd>
<div class="block">清理ThreadLocal的Context内容.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/URLResourceTest.html#resource--">resource()</a></span> - 类 中的方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/URLResourceTest.html" title="com.huazheng.tunny.tools.io中的类">URLResourceTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/ResourceUtilTest.html#resourceNameTest--">resourceNameTest()</a></span> - 类 中的方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/ResourceUtilTest.html" title="com.huazheng.tunny.tools.io中的类">ResourceUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/io/ResourceUtil.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">ResourceUtil</span></a> - <a href="../com/huazheng/tunny/tools/io/package-summary.html">com.huazheng.tunny.tools.io</a>中的类</dt>
<dd>
<div class="block">针对Jar包内的文件的工具类.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/ResourceUtil.html#ResourceUtil--">ResourceUtil()</a></span> - 类 的构造器com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/ResourceUtil.html" title="com.huazheng.tunny.tools.io中的类">ResourceUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/io/ResourceUtilTest.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">ResourceUtilTest</span></a> - <a href="../com/huazheng/tunny/tools/io/package-summary.html">com.huazheng.tunny.tools.io</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/ResourceUtilTest.html#ResourceUtilTest--">ResourceUtilTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/ResourceUtilTest.html" title="com.huazheng.tunny.tools.io中的类">ResourceUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html#retainAll-java.util.Collection-">retainAll(Collection&lt;?&gt;)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.<a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="com.huazheng.tunny.tools.collection.type中的类">ConcurrentHashSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtil.html#reverse-java.util.List-">reverse(List&lt;T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtil.html" title="com.huazheng.tunny.tools.collection中的类">ListUtil</a></dt>
<dd>
<div class="block">返回一个倒转顺序访问的List，仅仅是一个倒序的View，不会实际多生成一个List</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/ThreadDumpperTest.LongRunTask.html#run--">run()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/ThreadDumpperTest.LongRunTask.html" title="com.huazheng.tunny.tools.concurrent中的类">ThreadDumpperTest.LongRunTask</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPoolTest.LongRunTask.html#run--">run()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPoolTest.LongRunTask.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">QueuableCachedThreadPoolTest.LongRunTask</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/base/RuntimeUtil.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">RuntimeUtil</span></a> - <a href="../com/huazheng/tunny/tools/base/package-summary.html">com.huazheng.tunny.tools.base</a>中的类</dt>
<dd>
<div class="block">运行时工具类
 
 1.取得当前进程PID, JVM参数
 
 2.注册JVM关闭钩子, 获得CPU核数
 
 3.通过StackTrace 获得当前方法的类名方法名，调用者的类名方法名(获取StackTrace有消耗，不要滥用)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/RuntimeUtil.html#RuntimeUtil--">RuntimeUtil()</a></span> - 类 的构造器com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/RuntimeUtil.html" title="com.huazheng.tunny.tools.base中的类">RuntimeUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/base/RuntimeUtilTest.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">RuntimeUtilTest</span></a> - <a href="../com/huazheng/tunny/tools/base/package-summary.html">com.huazheng.tunny.tools.base</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/RuntimeUtilTest.html#RuntimeUtilTest--">RuntimeUtilTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/RuntimeUtilTest.html" title="com.huazheng.tunny.tools.base中的类">RuntimeUtilTest</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-17.html">上一个字母</a></li>
<li><a href="index-19.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-18.html" target="_top">框架</a></li>
<li><a href="index-18.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
