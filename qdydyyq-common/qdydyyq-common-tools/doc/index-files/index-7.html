<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:36 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>G - 索引</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="G - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-6.html">上一个字母</a></li>
<li><a href="index-8.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-7.html" target="_top">框架</a></li>
<li><a href="index-7.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a name="I:G">
<!--   -->
</a>
<h2 class="title">G</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtilTest.html#general--">general()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtilTest.html" title="com.huazheng.tunny.tools.collection中的类">ListUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtilTest.html#generalMethod--">generalMethod()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtilTest.html" title="com.huazheng.tunny.tools.collection中的类">MapUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/security/CryptoUtil.html#generateAesKey--">generateAesKey()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.security.<a href="../com/huazheng/tunny/tools/security/CryptoUtil.html" title="com.huazheng.tunny.tools.security中的类">CryptoUtil</a></dt>
<dd>
<div class="block">生成AES密钥,返回字节数组, 默认长度为128位(16字节).</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/security/CryptoUtil.html#generateAesKey-int-">generateAesKey(int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.security.<a href="../com/huazheng/tunny/tools/security/CryptoUtil.html" title="com.huazheng.tunny.tools.security中的类">CryptoUtil</a></dt>
<dd>
<div class="block">生成AES密钥,可选长度为128,192,256位.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/EnumUtil.html#generateBits-java.lang.Class-java.lang.Iterable-">generateBits(Class&lt;E&gt;, Iterable&lt;? extends E&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/EnumUtil.html" title="com.huazheng.tunny.tools.base中的类">EnumUtil</a></dt>
<dd>
<div class="block">将若干个枚举值转换为long(按bits 1,2,4,8...的方式叠加)，用于使用long保存多个选项的情况.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/EnumUtil.html#generateBits-java.lang.Class-E...-">generateBits(Class&lt;E&gt;, E...)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/EnumUtil.html" title="com.huazheng.tunny.tools.base中的类">EnumUtil</a></dt>
<dd>
<div class="block">将若干个枚举值转换为long(按bits 1,2,4,8...的方式叠加)，用于使用long保存多个选项的情况.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/security/CryptoUtil.html#generateHmacSha1Key--">generateHmacSha1Key()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.security.<a href="../com/huazheng/tunny/tools/security/CryptoUtil.html" title="com.huazheng.tunny.tools.security中的类">CryptoUtil</a></dt>
<dd>
<div class="block">生成HMAC-SHA1密钥,返回字节数组,长度为160位(20字节).</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/security/CryptoUtil.html#generateIV--">generateIV()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.security.<a href="../com/huazheng/tunny/tools/security/CryptoUtil.html" title="com.huazheng.tunny.tools.security中的类">CryptoUtil</a></dt>
<dd>
<div class="block">生成随机向量,默认大小为cipher.getBlockSize(), 16字节.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/HashUtil.html#generateSalt-int-">generateSalt(int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/HashUtil.html" title="com.huazheng.tunny.tools.text中的类">HashUtil</a></dt>
<dd>
<div class="block">用SecureRandom生成随机的byte[]作为salt.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtilTest.html#generateString--">generateString()</a></span> - 类 中的方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtilTest.html" title="com.huazheng.tunny.tools.number中的类">RandomUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtil.ValueCreator.html#get--">get()</a></span> - 接口 中的方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtil.ValueCreator.html" title="com.huazheng.tunny.tools.collection中的接口">MapUtil.ValueCreator</a></dt>
<dd>
<div class="block">创建对象</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html#get-int-">get(int)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">IntObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html#get-java.lang.Object-">get(Object)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">IntObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectMap.html#get-int-">get(int)</a></span> - 接口 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口">IntObjectMap</a></dt>
<dd>
<div class="block">Gets the value in the map with the specified key.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#get-long-">get(long)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">LongObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#get-java.lang.Object-">get(Object)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">LongObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html#get-long-">get(long)</a></span> - 接口 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口">LongObjectMap</a></dt>
<dd>
<div class="block">Gets the value in the map with the specified key.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html#get--">get()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.type.<a href="../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html" title="com.huazheng.tunny.tools.concurrent.type中的类">BasicFuture</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html#get-long-java.util.concurrent.TimeUnit-">get(long, TimeUnit)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.type.<a href="../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html" title="com.huazheng.tunny.tools.concurrent.type中的类">BasicFuture</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/type/ThreadLocalContext.html#get-java.lang.String-">get(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.concurrent.type.<a href="../com/huazheng/tunny/tools/concurrent/type/ThreadLocalContext.html" title="com.huazheng.tunny.tools.concurrent.type中的类">ThreadLocalContext</a></dt>
<dd>
<div class="block">取出ThreadLocal的上下文信息.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/StringBuilderHolder.html#get--">get()</a></span> - 类 中的方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/StringBuilderHolder.html" title="com.huazheng.tunny.tools.text中的类">StringBuilderHolder</a></dt>
<dd>
<div class="block">获取独立Holder的StringBuilder.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#getAccessibleMethodByName-java.lang.Class-java.lang.String-">getAccessibleMethodByName(Class, String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtil</a></dt>
<dd>
<div class="block">循环向上转型, 获取对象的DeclaredMethod,并强制设置为可访问.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Student.html#getAge--">getAge()</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Student.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.Student</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.StudentVO.html#getAge--">getAge()</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.StudentVO.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.StudentVO</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/AnnotationUtil.html#getAllAnnotations-java.lang.Class-">getAllAnnotations(Class&lt;?&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/AnnotationUtil.html" title="com.huazheng.tunny.tools.reflect中的类">AnnotationUtil</a></dt>
<dd>
<div class="block">递归Class所有的Annotation，一个最彻底的实现.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.html#getAllClass--">getAllClass()</a></span> - 类 中的方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassUtil.html#getAllInterfaces-java.lang.Class-">getAllInterfaces(Class&lt;?&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtil</a></dt>
<dd>
<div class="block">递归返回本类及所有基类继承的接口，及接口继承的接口，比Spring中的相同实现完整</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/vip/vjtools/test/log/LogbackListAppender.html#getAllLogs--">getAllLogs()</a></span> - 类 中的方法com.vip.vjtools.test.log.<a href="../com/vip/vjtools/test/log/LogbackListAppender.html" title="com.vip.vjtools.test.log中的类">LogbackListAppender</a></dt>
<dd>
<div class="block">返回之前append的所有log.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassUtil.html#getAllSuperclasses-java.lang.Class-">getAllSuperclasses(Class&lt;?&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtil</a></dt>
<dd>
<div class="block">递归返回所有的SupperClasses，包含Object.class</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.html#getAndSetFieldValue--">getAndSetFieldValue()</a></span> - 类 中的方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/AnnotationUtil.html#getAnnotatedFields-java.lang.Class-java.lang.Class-">getAnnotatedFields(Class&lt;? extends Object&gt;, Class&lt;T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/AnnotationUtil.html" title="com.huazheng.tunny.tools.reflect中的类">AnnotationUtil</a></dt>
<dd>
<div class="block">找出所有标注了该annotation的属性，循环遍历父类，包含private属性.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/AnnotationUtil.html#getAnnotatedPublicFields-java.lang.Class-java.lang.Class-">getAnnotatedPublicFields(Class&lt;? extends Object&gt;, Class&lt;T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/AnnotationUtil.html" title="com.huazheng.tunny.tools.reflect中的类">AnnotationUtil</a></dt>
<dd>
<div class="block">找出所有标注了该annotation的公共属性，循环遍历父类.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/AnnotationUtil.html#getAnnotatedPublicMethods-java.lang.Class-java.lang.Class-">getAnnotatedPublicMethods(Class&lt;?&gt;, Class&lt;T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/AnnotationUtil.html" title="com.huazheng.tunny.tools.reflect中的类">AnnotationUtil</a></dt>
<dd>
<div class="block">找出所有标注了该annotation的公共方法(含父类的公共函数)，循环其接口.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/PropertiesUtil.html#getBoolean-java.util.Properties-java.lang.String-java.lang.Boolean-">getBoolean(Properties, String, Boolean)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/PropertiesUtil.html" title="com.huazheng.tunny.tools.base中的类">PropertiesUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#getBoolean-java.lang.String-">getBoolean(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtil</a></dt>
<dd>
<div class="block">读取Boolean类型的系统变量，为空时返回null，代表未设置，而不是Boolean.getBoolean()的false.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#getBoolean-java.lang.String-java.lang.Boolean-">getBoolean(String, Boolean)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtil</a></dt>
<dd>
<div class="block">读取Boolean类型的系统变量，为空时返回默认值, 而不是Boolean.getBoolean()的false.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#getBoolean-java.lang.String-java.lang.String-java.lang.Boolean-">getBoolean(String, String, Boolean)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtil</a></dt>
<dd>
<div class="block">合并系统变量(-D)，环境变量 和默认值，以系统变量优先</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html#getBuilder--">getBuilder()</a></span> - 类 中的方法com.huazheng.tunny.tools.io.type.<a href="../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html" title="com.huazheng.tunny.tools.io.type中的类">StringBuilderWriter</a></dt>
<dd>
<div class="block">Returns the underlying builder.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/RuntimeUtil.html#getCallerClass--">getCallerClass()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/RuntimeUtil.html" title="com.huazheng.tunny.tools.base中的类">RuntimeUtil</a></dt>
<dd>
<div class="block">通过StackTrace，获得调用者的类名.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/RuntimeUtil.html#getCallerMethod--">getCallerMethod()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/RuntimeUtil.html" title="com.huazheng.tunny.tools.base中的类">RuntimeUtil</a></dt>
<dd>
<div class="block">通过StackTrace，获得调用者的"类名.方法名()"
 
 获取StackTrace有消耗，不要滥用</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassUtil.html#getClassGenericType-java.lang.Class-">getClassGenericType(Class)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtil</a></dt>
<dd>
<div class="block">通过反射, 获得Class定义中声明的泛型参数的类型,
 
 注意泛型必须定义在父类处.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassUtil.html#getClassGenericType-java.lang.Class-int-">getClassGenericType(Class, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtil</a></dt>
<dd>
<div class="block">通过反射, 获得Class定义中声明的父类的泛型参数的类型.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/SortedArrayList.html#getComparator--">getComparator()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.<a href="../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="com.huazheng.tunny.tools.collection.type中的类">SortedArrayList</a></dt>
<dd>
<div class="block">Returns comparator assigned to this collection, if such exist.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/RuntimeUtil.html#getCores--">getCores()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/RuntimeUtil.html" title="com.huazheng.tunny.tools.base中的类">RuntimeUtil</a></dt>
<dd>
<div class="block">获取CPU核数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Student.html#getCourse--">getCourse()</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Student.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.Student</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.StudentVO.html#getCourse--">getCourse()</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.StudentVO.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.StudentVO</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/RuntimeUtil.html#getCurrentClass--">getCurrentClass()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/RuntimeUtil.html" title="com.huazheng.tunny.tools.base中的类">RuntimeUtil</a></dt>
<dd>
<div class="block">通过StackTrace，获得当前方法的类名.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/RuntimeUtil.html#getCurrentMethod--">getCurrentMethod()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/RuntimeUtil.html" title="com.huazheng.tunny.tools.base中的类">RuntimeUtil</a></dt>
<dd>
<div class="block">通过StackTrace，获得当前方法的"类名.方法名()"
 
 获取StackTrace有消耗，不要滥用</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#getDayOfWeek-java.util.Date-">getDayOfWeek(Date)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">获得日期是一周的第几天.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtilTest.html#getDayOfWeek--">getDayOfWeek()</a></span> - 类 中的方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtilTest.html" title="com.huazheng.tunny.tools.time中的类">DateUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#getDayOfYear-java.util.Date-">getDayOfYear(Date)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">获得日期是一年的第几天，返回值从1开始</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassLoaderUtil.html#getDefaultClassLoader--">getDefaultClassLoader()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassLoaderUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ClassLoaderUtil</a></dt>
<dd>
<div class="block">Copy from Spring, 按顺序获取默认ClassLoader
 
 1.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.TestBean.html#getDefaultValue--">getDefaultValue()</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.TestBean.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapperTest.TestBean</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/PropertiesUtil.html#getDouble-java.util.Properties-java.lang.String-java.lang.Double-">getDouble(Properties, String, Double)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/PropertiesUtil.html" title="com.huazheng.tunny.tools.base中的类">PropertiesUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#getDouble-java.lang.String-">getDouble(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtil</a></dt>
<dd>
<div class="block">读取Double类型的系统变量，为空时返回null.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#getDouble-java.lang.String-java.lang.Double-">getDouble(String, Double)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtil</a></dt>
<dd>
<div class="block">读取Double类型的系统变量，为空时返回默认值.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#getDouble-java.lang.String-java.lang.String-java.lang.Double-">getDouble(String, String, Double)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtil</a></dt>
<dd>
<div class="block">合并系统变量(-D)，环境变量 和默认值，以系统变量优先</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.TestBean.html#getEmptyValue--">getEmptyValue()</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.TestBean.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapperTest.TestBean</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#getField-java.lang.Class-java.lang.String-">getField(Class, String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtil</a></dt>
<dd>
<div class="block">循环向上转型, 获取对象的DeclaredField, 并强制设置为可访问.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#getFieldValue-java.lang.Object-java.lang.String-">getFieldValue(Object, String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtil</a></dt>
<dd>
<div class="block">直接读取对象属性值, 无视private/protected修饰符, 不经过getter函数.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#getFieldValue-java.lang.Object-java.lang.reflect.Field-">getFieldValue(Object, Field)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtil</a></dt>
<dd>
<div class="block">使用已获取的Field, 直接读取对象属性值, 不经过getter函数.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#getFileExtension-java.io.File-">getFileExtension(File)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">获取文件名的扩展名部分(不包含.)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#getFileExtension-java.lang.String-">getFileExtension(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">获取文件名的扩展名部分(不包含.)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#getFileName-java.lang.String-">getFileName(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">获取文件名(不包含路径)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/CollectionUtil.html#getFirst-java.util.Collection-">getFirst(Collection&lt;T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/CollectionUtil.html" title="com.huazheng.tunny.tools.collection中的类">CollectionUtil</a></dt>
<dd>
<div class="block">取得Collection的第一个元素，如果collection为空返回null.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtil.html#getFirst-java.util.List-">getFirst(List&lt;T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtil.html" title="com.huazheng.tunny.tools.collection中的类">ListUtil</a></dt>
<dd>
<div class="block">获取第一个元素, 如果List为空返回 null.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/vip/vjtools/test/log/LogbackListAppender.html#getFirstLog--">getFirstLog()</a></span> - 类 中的方法com.vip.vjtools.test.log.<a href="../com/vip/vjtools/test/log/LogbackListAppender.html" title="com.vip.vjtools.test.log中的类">LogbackListAppender</a></dt>
<dd>
<div class="block">返回之前append的第一个log.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/vip/vjtools/test/log/LogbackListAppender.html#getFirstMessage--">getFirstMessage()</a></span> - 类 中的方法com.vip.vjtools.test.log.<a href="../com/vip/vjtools/test/log/LogbackListAppender.html" title="com.vip.vjtools.test.log中的类">LogbackListAppender</a></dt>
<dd>
<div class="block">返回之前append的第一个log的内容.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#getGetterMethod-java.lang.Class-java.lang.String-">getGetterMethod(Class&lt;?&gt;, String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtil</a></dt>
<dd>
<div class="block">循环遍历，按属性名获取前缀为get或is的函数，并设为可访问</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/StringBuilderHolder.html#getGlobal--">getGlobal()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/StringBuilderHolder.html" title="com.huazheng.tunny.tools.text中的类">StringBuilderHolder</a></dt>
<dd>
<div class="block">获取公共Holder的StringBuilder.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/net/NetUtil.html#getHostName--">getHostName()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.net.<a href="../com/huazheng/tunny/tools/net/NetUtil.html" title="com.huazheng.tunny.tools.net中的类">NetUtil</a></dt>
<dd>
<div class="block">获得本地HostName</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.TestBean3.html#getId--">getId()</a></span> - 类 中的方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.TestBean3.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtilTest.TestBean3</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/PropertiesUtil.html#getInt-java.util.Properties-java.lang.String-java.lang.Integer-">getInt(Properties, String, Integer)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/PropertiesUtil.html" title="com.huazheng.tunny.tools.base中的类">PropertiesUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#getInteger-java.lang.String-">getInteger(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtil</a></dt>
<dd>
<div class="block">读取Integer类型的系统变量，为空时返回null.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#getInteger-java.lang.String-java.lang.Integer-">getInteger(String, Integer)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtil</a></dt>
<dd>
<div class="block">读取Integer类型的系统变量，为空时返回默认值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#getInteger-java.lang.String-java.lang.String-java.lang.Integer-">getInteger(String, String, Integer)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtil</a></dt>
<dd>
<div class="block">合并系统变量(-D)，环境变量 和默认值，以系统变量优先</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FilePathUtil.html#getJarPath-java.lang.Class-">getJarPath(Class&lt;?&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FilePathUtil.html" title="com.huazheng.tunny.tools.io中的类">FilePathUtil</a></dt>
<dd>
<div class="block">获得参数clazz所在的Jar文件的绝对路径</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FilePathUtilTest.html#getJarPath--">getJarPath()</a></span> - 类 中的方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FilePathUtilTest.html" title="com.huazheng.tunny.tools.io中的类">FilePathUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/XmlMapper.html#getJaxbContext-java.lang.Class-">getJaxbContext(Class)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/XmlMapper.html" title="com.huazheng.tunny.tools.mapper中的类">XmlMapper</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/CollectionUtil.html#getLast-java.util.Collection-">getLast(Collection&lt;T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/CollectionUtil.html" title="com.huazheng.tunny.tools.collection中的类">CollectionUtil</a></dt>
<dd>
<div class="block">获取Collection的最后一个元素，如果collection为空返回null.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtil.html#getLast-java.util.List-">getLast(List&lt;T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtil.html" title="com.huazheng.tunny.tools.collection中的类">ListUtil</a></dt>
<dd>
<div class="block">获取最后一个元素，如果List为空返回null.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/vip/vjtools/test/log/LogbackListAppender.html#getLastLog--">getLastLog()</a></span> - 类 中的方法com.vip.vjtools.test.log.<a href="../com/vip/vjtools/test/log/LogbackListAppender.html" title="com.vip.vjtools.test.log中的类">LogbackListAppender</a></dt>
<dd>
<div class="block">返回之前append的最后一个log.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/vip/vjtools/test/log/LogbackListAppender.html#getLastMessage--">getLastMessage()</a></span> - 类 中的方法com.vip.vjtools.test.log.<a href="../com/vip/vjtools/test/log/LogbackListAppender.html" title="com.vip.vjtools.test.log中的类">LogbackListAppender</a></dt>
<dd>
<div class="block">返回之前append的最后一个log的内容.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/type/Pair.html#getLeft--">getLeft()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.type.<a href="../com/huazheng/tunny/tools/base/type/Pair.html" title="com.huazheng.tunny.tools.base.type中的类">Pair</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/type/Triple.html#getLeft--">getLeft()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.type.<a href="../com/huazheng/tunny/tools/base/type/Triple.html" title="com.huazheng.tunny.tools.base.type中的类">Triple</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/net/NetUtil.html#getLocalAddress--">getLocalAddress()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.net.<a href="../com/huazheng/tunny/tools/net/NetUtil.html" title="com.huazheng.tunny.tools.net中的类">NetUtil</a></dt>
<dd>
<div class="block">获得本地地址</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/net/NetUtil.html#getLocalHost--">getLocalHost()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.net.<a href="../com/huazheng/tunny/tools/net/NetUtil.html" title="com.huazheng.tunny.tools.net中的类">NetUtil</a></dt>
<dd>
<div class="block">获得本地Ip地址</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/vip/vjtools/test/log/LogbackListAppender.html#getLogsCount--">getLogsCount()</a></span> - 类 中的方法com.vip.vjtools.test.log.<a href="../com/vip/vjtools/test/log/LogbackListAppender.html" title="com.vip.vjtools.test.log中的类">LogbackListAppender</a></dt>
<dd>
<div class="block">返回Log的数量。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/PropertiesUtil.html#getLong-java.util.Properties-java.lang.String-java.lang.Long-">getLong(Properties, String, Long)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/PropertiesUtil.html" title="com.huazheng.tunny.tools.base中的类">PropertiesUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#getLong-java.lang.String-">getLong(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtil</a></dt>
<dd>
<div class="block">读取Long类型的系统变量，为空时返回null.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#getLong-java.lang.String-java.lang.Long-">getLong(String, Long)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtil</a></dt>
<dd>
<div class="block">读取Integer类型的系统变量，为空时返回默认值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#getLong-java.lang.String-java.lang.String-java.lang.Long-">getLong(String, String, Long)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtil</a></dt>
<dd>
<div class="block">合并系统变量(-D)，环境变量 和默认值，以系统变量优先</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/JsonMapper.html#getMapper--">getMapper()</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/JsonMapper.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapper</a></dt>
<dd>
<div class="block">取出Mapper做进一步的设置或使用其他序列化API.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/type/CloneableException.html#getMessage--">getMessage()</a></span> - 异常错误 中的方法com.huazheng.tunny.tools.base.type.<a href="../com/huazheng/tunny/tools/base/type/CloneableException.html" title="com.huazheng.tunny.tools.base.type中的类">CloneableException</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html#getMessage--">getMessage()</a></span> - 异常错误 中的方法com.huazheng.tunny.tools.base.type.<a href="../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html" title="com.huazheng.tunny.tools.base.type中的类">CloneableRuntimeException</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/type/UncheckedException.html#getMessage--">getMessage()</a></span> - 异常错误 中的方法com.huazheng.tunny.tools.base.type.<a href="../com/huazheng/tunny/tools/base/type/UncheckedException.html" title="com.huazheng.tunny.tools.base.type中的类">UncheckedException</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.html#getMessage--">getMessage()</a></span> - 类 中的方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#getMethod-java.lang.Class-java.lang.String-java.lang.Class...-">getMethod(Class&lt;?&gt;, String, Class&lt;?&gt;...)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtil</a></dt>
<dd>
<div class="block">循环向上转型, 获取对象的DeclaredMethod, 并强制设置为可访问.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/type/Triple.html#getMiddle--">getMiddle()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.type.<a href="../com/huazheng/tunny/tools/base/type/Triple.html" title="com.huazheng.tunny.tools.base.type中的类">Triple</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#getMonthLength-java.util.Date-">getMonthLength(Date)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">获取某个月有多少天, 考虑闰年等因数, 移植Jodd Core的TimeUtil</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#getMonthLength-int-int-">getMonthLength(int, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">获取某个月有多少天, 考虑闰年等因数, 移植Jodd Core的TimeUtil</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtilTest.html#getName--">getName()</a></span> - 类 中的方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtilTest.html" title="com.huazheng.tunny.tools.io中的类">FileUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Student.html#getName--">getName()</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Student.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.Student</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.StudentVO.html#getName--">getName()</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.StudentVO.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.StudentVO</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Teacher.html#getName--">getName()</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Teacher.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.Teacher</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.TeacherVO.html#getName--">getName()</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.TeacherVO.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.TeacherVO</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.TestBean.html#getName--">getName()</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.TestBean.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapperTest.TestBean</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.TestBean.html#getNullValue--">getNullValue()</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.TestBean.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapperTest.TestBean</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassUtil.html#getPackageName-java.lang.Class-">getPackageName(Class&lt;?&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtil</a></dt>
<dd>
<div class="block">返回PackageName</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassUtil.html#getPackageName-java.lang.String-">getPackageName(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtil</a></dt>
<dd>
<div class="block">返回PackageName</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FilePathUtil.html#getParentPath-java.lang.String-">getParentPath(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FilePathUtil.html" title="com.huazheng.tunny.tools.io中的类">FilePathUtil</a></dt>
<dd>
<div class="block">获得上层目录的路径</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/RuntimeUtil.html#getPid--">getPid()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/RuntimeUtil.html" title="com.huazheng.tunny.tools.base中的类">RuntimeUtil</a></dt>
<dd>
<div class="block">获得当前进程的PID
 
 当失败时返回-1</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#getProperty-java.lang.Object-java.lang.String-">getProperty(Object, String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtil</a></dt>
<dd>
<div class="block">先尝试用Getter函数读取, 如果不存在则直接读取变量.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.TestBean.html#getPublicField--">getPublicField()</a></span> - 类 中的方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.TestBean.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtilTest.TestBean</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtilTest.html#getRandom--">getRandom()</a></span> - 类 中的方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtilTest.html" title="com.huazheng.tunny.tools.number中的类">RandomUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/ResourceUtil.html#getResourcesQuietly-java.lang.String-">getResourcesQuietly(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/ResourceUtil.html" title="com.huazheng.tunny.tools.io中的类">ResourceUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/ResourceUtil.html#getResourcesQuietly-java.lang.String-java.lang.ClassLoader-">getResourcesQuietly(String, ClassLoader)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/ResourceUtil.html" title="com.huazheng.tunny.tools.io中的类">ResourceUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/type/Pair.html#getRight--">getRight()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.type.<a href="../com/huazheng/tunny/tools/base/type/Pair.html" title="com.huazheng.tunny.tools.base.type中的类">Pair</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/type/Triple.html#getRight--">getRight()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.type.<a href="../com/huazheng/tunny/tools/base/type/Triple.html" title="com.huazheng.tunny.tools.base.type中的类">Triple</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/ExceptionUtil.html#getRootCause-java.lang.Throwable-">getRootCause(Throwable)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/ExceptionUtil.html" title="com.huazheng.tunny.tools.base中的类">ExceptionUtil</a></dt>
<dd>
<div class="block">获取异常的Root Cause.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/ExceptionUtilTest.html#getRootCause--">getRootCause()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/ExceptionUtilTest.html" title="com.huazheng.tunny.tools.base中的类">ExceptionUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#getSetterMethod-java.lang.Class-java.lang.String-java.lang.Class-">getSetterMethod(Class&lt;?&gt;, String, Class&lt;?&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtil</a></dt>
<dd>
<div class="block">循环遍历，按属性名获取前缀为set的函数，并设为可访问</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassUtil.html#getShortClassName-java.lang.Class-">getShortClassName(Class&lt;?&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtil</a></dt>
<dd>
<div class="block">返回短Class名, 不包含PackageName.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassUtil.html#getShortClassName-java.lang.String-">getShortClassName(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtil</a></dt>
<dd>
<div class="block">返回Class名，不包含PackageName
 
 内部类的话，返回"主类.内部类"</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/ExceptionUtilTest.html#getStackTraceAsString--">getStackTraceAsString()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/ExceptionUtilTest.html" title="com.huazheng.tunny.tools.base中的类">ExceptionUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/PropertiesUtil.html#getString-java.util.Properties-java.lang.String-java.lang.String-">getString(Properties, String, String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/PropertiesUtil.html" title="com.huazheng.tunny.tools.base中的类">PropertiesUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#getString-java.lang.String-">getString(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtil</a></dt>
<dd>
<div class="block">读取String类型的系统变量，为空时返回null.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#getString-java.lang.String-java.lang.String-">getString(String, String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtil</a></dt>
<dd>
<div class="block">读取String类型的系统变量，为空时返回默认值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html#getString-java.lang.String-java.lang.String-java.lang.String-">getString(String, String, String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtil</a></dt>
<dd>
<div class="block">合并系统变量(-D)，环境变量 和默认值，以系统变量优先</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.html#getSubmittedCount--">getSubmittedCount()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">QueuableCachedThreadPool</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.html#getSuperClassGenericType--">getSuperClassGenericType()</a></span> - 类 中的方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Student.html#getTeacher--">getTeacher()</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Student.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.Student</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.StudentVO.html#getTeacher--">getTeacher()</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.StudentVO.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.StudentVO</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/RuntimeUtil.html#getUpTime--">getUpTime()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/RuntimeUtil.html" title="com.huazheng.tunny.tools.base中的类">RuntimeUtil</a></dt>
<dd>
<div class="block">返回应用启动到现在的毫秒数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/RuntimeUtil.html#getVmArguments--">getVmArguments()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/RuntimeUtil.html" title="com.huazheng.tunny.tools.base中的类">RuntimeUtil</a></dt>
<dd>
<div class="block">返回输入的JVM参数列表</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#getWeekOfMonth-java.util.Date-">getWeekOfMonth(Date)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">获得日期是一月的第几周，返回值从1开始.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#getWeekOfYear-java.util.Date-">getWeekOfYear(Date)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">获得日期是一年的第几周，返回值从1开始.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtilTest.html#getXXofXX--">getXXofXX()</a></span> - 类 中的方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtilTest.html" title="com.huazheng.tunny.tools.time中的类">DateUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtil.html#gracefulShutdown-java.util.concurrent.ExecutorService-int-">gracefulShutdown(ExecutorService, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtil.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolUtil</a></dt>
<dd>
<div class="block">按照ExecutorService JavaDoc示例代码编写的Graceful Shutdown方法.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtil.html#gracefulShutdown-java.util.concurrent.ExecutorService-int-java.util.concurrent.TimeUnit-">gracefulShutdown(ExecutorService, int, TimeUnit)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtil.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtilTest.html#gracefulShutdown--">gracefulShutdown()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtilTest.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtilTest.html#guavaBuildList--">guavaBuildList()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtilTest.html" title="com.huazheng.tunny.tools.collection中的类">ListUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtilTest.html#guavaBuildMap--">guavaBuildMap()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtilTest.html" title="com.huazheng.tunny.tools.collection中的类">MapUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/QueueUtilTest.html#guavaBuildSet--">guavaBuildSet()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/QueueUtilTest.html" title="com.huazheng.tunny.tools.collection中的类">QueueUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/SetUtilTest.html#guavaBuildSet--">guavaBuildSet()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/SetUtilTest.html" title="com.huazheng.tunny.tools.collection中的类">SetUtilTest</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-6.html">上一个字母</a></li>
<li><a href="index-8.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-7.html" target="_top">框架</a></li>
<li><a href="index-7.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
