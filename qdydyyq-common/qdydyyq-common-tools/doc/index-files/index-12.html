<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:36 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>L - 索引</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="L - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-11.html">上一个字母</a></li>
<li><a href="index-13.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-12.html" target="_top">框架</a></li>
<li><a href="index-12.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a name="I:L">
<!--   -->
</a>
<h2 class="title">L</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/Platforms.html#LINE_SEPARATOR">LINE_SEPARATOR</a></span> - 类 中的静态变量com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/Platforms.html" title="com.huazheng.tunny.tools.base中的类">Platforms</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/Platforms.html#LINUX_FILE_PATH_SEPARATOR_CHAR">LINUX_FILE_PATH_SEPARATOR_CHAR</a></span> - 类 中的静态变量com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/Platforms.html" title="com.huazheng.tunny.tools.base中的类">Platforms</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileTreeWalker.html#listAll-java.io.File-">listAll(File)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileTreeWalker.html" title="com.huazheng.tunny.tools.io中的类">FileTreeWalker</a></dt>
<dd>
<div class="block">前序递归列出所有文件, 包含文件与目录，及根目录本身.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/CollectionUtilTest.html#listCompare--">listCompare()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/CollectionUtilTest.html" title="com.huazheng.tunny.tools.collection中的类">CollectionUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.ListenableProperties.html#ListenableProperties-java.util.Properties-">ListenableProperties(Properties)</a></span> - 类 的构造器com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.ListenableProperties.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtil.ListenableProperties</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtilTest.html#listenableProperties--">listenableProperties()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtilTest.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.ListenableProperties.html#listeners">listeners</a></span> - 类 中的变量com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.ListenableProperties.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtil.ListenableProperties</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileTreeWalker.html#listFile-java.io.File-">listFile(File)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileTreeWalker.html" title="com.huazheng.tunny.tools.io中的类">FileTreeWalker</a></dt>
<dd>
<div class="block">前序递归列出所有文件, 只包含文件.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileTreeWalkerTest.html#listFile--">listFile()</a></span> - 类 中的方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileTreeWalkerTest.html" title="com.huazheng.tunny.tools.io中的类">FileTreeWalkerTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileTreeWalker.html#listFileWithAntPath-java.io.File-java.lang.String-">listFileWithAntPath(File, String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileTreeWalker.html" title="com.huazheng.tunny.tools.io中的类">FileTreeWalker</a></dt>
<dd>
<div class="block">前序递归列出所有文件, 列出符合ant path风格表达式的文件
 
 如 ("/a/b/hello.txt", "he.*\.txt") 将被返回</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileTreeWalker.html#listFileWithExtension-java.io.File-java.lang.String-">listFileWithExtension(File, String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileTreeWalker.html" title="com.huazheng.tunny.tools.io中的类">FileTreeWalker</a></dt>
<dd>
<div class="block">前序递归列出所有文件, 列出后缀名匹配的文件.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileTreeWalker.html#listFileWithRegexFileName-java.io.File-java.lang.String-">listFileWithRegexFileName(File, String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileTreeWalker.html" title="com.huazheng.tunny.tools.io中的类">FileTreeWalker</a></dt>
<dd>
<div class="block">前序递归列出所有文件, 列出文件名匹配正则表达式的文件
 
 如 ("/a/b/hello.txt", "he.*\.txt") 将被返回</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileTreeWalker.html#listFileWithWildcardFileName-java.io.File-java.lang.String-">listFileWithWildcardFileName(File, String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileTreeWalker.html" title="com.huazheng.tunny.tools.io中的类">FileTreeWalker</a></dt>
<dd>
<div class="block">前序递归列出所有文件, 列出文件名匹配通配符的文件
 
 如 ("/a/b/hello.txt", "he*") 将被返回</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.TestBean.html#listType-java.util.List-">listType(List&lt;?&gt;)</a></span> - 类 中的方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.TestBean.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtilTest.TestBean</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/collection/ListUtil.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">ListUtil</span></a> - <a href="../com/huazheng/tunny/tools/collection/package-summary.html">com.huazheng.tunny.tools.collection</a>中的类</dt>
<dd>
<div class="block">关于List的工具集合.
 
 1.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtil.html#ListUtil--">ListUtil()</a></span> - 类 的构造器com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtil.html" title="com.huazheng.tunny.tools.collection中的类">ListUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/collection/ListUtilTest.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">ListUtilTest</span></a> - <a href="../com/huazheng/tunny/tools/collection/package-summary.html">com.huazheng.tunny.tools.collection</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtilTest.html#ListUtilTest--">ListUtilTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtilTest.html" title="com.huazheng.tunny.tools.collection中的类">ListUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/PropertiesUtil.html#loadFromFile-java.lang.String-">loadFromFile(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/PropertiesUtil.html" title="com.huazheng.tunny.tools.base中的类">PropertiesUtil</a></dt>
<dd>
<div class="block">从文件路径加载properties.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/PropertiesUtil.html#loadFromString-java.lang.String-">loadFromString(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/PropertiesUtil.html" title="com.huazheng.tunny.tools.base中的类">PropertiesUtil</a></dt>
<dd>
<div class="block">从字符串内容加载Properties</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/PropertiesUtilTest.html#loadProperties--">loadProperties()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/PropertiesUtilTest.html" title="com.huazheng.tunny.tools.base中的类">PropertiesUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/net/NetUtilTest.html#localhost--">localhost()</a></span> - 类 中的方法com.huazheng.tunny.tools.net.<a href="../com/huazheng/tunny/tools/net/NetUtilTest.html" title="com.huazheng.tunny.tools.net中的类">NetUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/vip/vjtools/test/log/LogbackListAppender.html" title="com.vip.vjtools.test.log中的类"><span class="typeNameLink">LogbackListAppender</span></a> - <a href="../com/vip/vjtools/test/log/package-summary.html">com.vip.vjtools.test.log</a>中的类</dt>
<dd>
<div class="block">在List中保存日志的Appender, 用于测试Logback的日志输出.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/vip/vjtools/test/log/LogbackListAppender.html#LogbackListAppender--">LogbackListAppender()</a></span> - 类 的构造器com.vip.vjtools.test.log.<a href="../com/vip/vjtools/test/log/LogbackListAppender.html" title="com.vip.vjtools.test.log中的类">LogbackListAppender</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/vip/vjtools/test/log/LogbackListAppenderTest.html" title="com.vip.vjtools.test.log中的类"><span class="typeNameLink">LogbackListAppenderTest</span></a> - <a href="../com/vip/vjtools/test/log/package-summary.html">com.vip.vjtools.test.log</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/vip/vjtools/test/log/LogbackListAppenderTest.html#LogbackListAppenderTest--">LogbackListAppenderTest()</a></span> - 类 的构造器com.vip.vjtools.test.log.<a href="../com/vip/vjtools/test/log/LogbackListAppenderTest.html" title="com.vip.vjtools.test.log中的类">LogbackListAppenderTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/AbortPolicyWithReport.html#logger">logger</a></span> - 类 中的静态变量com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/AbortPolicyWithReport.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">AbortPolicyWithReport</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/BeanUtilTest.html#logic--">logic()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/BeanUtilTest.html" title="com.huazheng.tunny.tools.base中的类">BeanUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/Concurrents.html#longAdder--">longAdder()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/Concurrents.html" title="com.huazheng.tunny.tools.concurrent中的类">Concurrents</a></dt>
<dd>
<div class="block">返回没有激烈CAS冲突的LongAdder, 并发的＋1将在不同的Counter里进行，只在取值时将多个Counter求和.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/ConcurrentsTest.html#longAdder--">longAdder()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/ConcurrentsTest.html" title="com.huazheng.tunny.tools.concurrent中的类">ConcurrentsTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html" title="com.huazheng.tunny.tools.concurrent.jsr166e中的类"><span class="typeNameLink">LongAdder</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/jsr166e/package-summary.html">com.huazheng.tunny.tools.concurrent.jsr166e</a>中的类</dt>
<dd>
<div class="block">移植
 http://gee.cs.oswego.edu/cgi-bin/viewcvs.cgi/jsr166/src/jsr166e/LongAdder.java Revision.1.17
 
 One or more variables that together maintain an initially zero
 <code>long</code> sum.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#LongAdder--">LongAdder()</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.jsr166e.<a href="../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html" title="com.huazheng.tunny.tools.concurrent.jsr166e中的类">LongAdder</a></dt>
<dd>
<div class="block">Creates a new adder with initial sum of zero.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ArrayUtil.html#longAsList-long...-">longAsList(long...)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ArrayUtil.html" title="com.huazheng.tunny.tools.collection中的类">ArrayUtil</a></dt>
<dd>
<div class="block">Arrays.asList()的加强版, 返回一个底层为原始类型long的List
 
 与保存Long相比节约空间，同时只在读取数据时AutoBoxing.</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类"><span class="typeNameLink">LongObjectHashMap</span></a>&lt;<a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&gt; - <a href="../com/huazheng/tunny/tools/collection/type/primitive/package-summary.html">com.huazheng.tunny.tools.collection.type.primitive</a>中的类</dt>
<dd>
<div class="block">移植Netty 4.1.6的Key为原子类型的集合类, 在数据结构上与HashMap不一样，空间占用与读写性能俱比原来更优.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#LongObjectHashMap--">LongObjectHashMap()</a></span> - 类 的构造器com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">LongObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#LongObjectHashMap-int-">LongObjectHashMap(int)</a></span> - 类 的构造器com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">LongObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#LongObjectHashMap-int-float-">LongObjectHashMap(int, float)</a></span> - 类 的构造器com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">LongObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口"><span class="typeNameLink">LongObjectMap</span></a>&lt;<a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html" title="LongObjectMap中的类型参数">V</a>&gt; - <a href="../com/huazheng/tunny/tools/collection/type/primitive/package-summary.html">com.huazheng.tunny.tools.collection.type.primitive</a>中的接口</dt>
<dd>
<div class="block">Interface for a primitive map that uses <code>long</code>s as keys.</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.PrimitiveEntry.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口"><span class="typeNameLink">LongObjectMap.PrimitiveEntry</span></a>&lt;<a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.PrimitiveEntry.html" title="LongObjectMap.PrimitiveEntry中的类型参数">V</a>&gt; - <a href="../com/huazheng/tunny/tools/collection/type/primitive/package-summary.html">com.huazheng.tunny.tools.collection.type.primitive</a>中的接口</dt>
<dd>
<div class="block">A primitive entry in the map, provided by the iterator from <a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html#entries--"><code>LongObjectMap.entries()</code></a></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/ThreadDumpperTest.LongRunTask.html#LongRunTask-java.util.concurrent.CountDownLatch-">LongRunTask(CountDownLatch)</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/ThreadDumpperTest.LongRunTask.html" title="com.huazheng.tunny.tools.concurrent中的类">ThreadDumpperTest.LongRunTask</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPoolTest.LongRunTask.html#LongRunTask--">LongRunTask()</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPoolTest.LongRunTask.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">QueuableCachedThreadPoolTest.LongRunTask</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtilTest.html#longSystemProperty--">longSystemProperty()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtilTest.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#longValue--">longValue()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.jsr166e.<a href="../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html" title="com.huazheng.tunny.tools.concurrent.jsr166e中的类">LongAdder</a></dt>
<dd>
<div class="block">Equivalent to <a href="../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#sum--"><code>LongAdder.sum()</code></a>.</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-11.html">上一个字母</a></li>
<li><a href="index-13.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-12.html" target="_top">框架</a></li>
<li><a href="index-12.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
