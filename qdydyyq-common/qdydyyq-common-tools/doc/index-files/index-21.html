<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:36 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>U - 索引</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="U - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-20.html">上一个字母</a></li>
<li><a href="index-22.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-21.html" target="_top">框架</a></li>
<li><a href="index-21.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a name="I:U">
<!--   -->
</a>
<h2 class="title">U</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.BClass.html#ufield">ufield</a></span> - 类 中的变量com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.BClass.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtilTest.BClass</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/ExceptionUtil.html#unchecked-java.lang.Throwable-">unchecked(Throwable)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/ExceptionUtil.html" title="com.huazheng.tunny.tools.base中的类">ExceptionUtil</a></dt>
<dd>
<div class="block">将CheckedException转换为RuntimeException重新抛出, 可以减少函数签名中的CheckExcetpion定义.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/ExceptionUtilTest.html#unchecked--">unchecked()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/ExceptionUtilTest.html" title="com.huazheng.tunny.tools.base中的类">ExceptionUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/base/type/UncheckedException.html" title="com.huazheng.tunny.tools.base.type中的类"><span class="typeNameLink">UncheckedException</span></a> - <a href="../com/huazheng/tunny/tools/base/type/package-summary.html">com.huazheng.tunny.tools.base.type</a>中的异常错误</dt>
<dd>
<div class="block">CheckedException的wrapper.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/type/UncheckedException.html#UncheckedException-java.lang.Throwable-">UncheckedException(Throwable)</a></span> - 异常错误 的构造器com.huazheng.tunny.tools.base.type.<a href="../com/huazheng/tunny/tools/base/type/UncheckedException.html" title="com.huazheng.tunny.tools.base.type中的类">UncheckedException</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/EscapeUtil.html#unescapeHtml-java.lang.String-">unescapeHtml(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/EscapeUtil.html" title="com.huazheng.tunny.tools.text中的类">EscapeUtil</a></dt>
<dd>
<div class="block">Html解码，将HTML4格式的字符串转码解码为普通字符串.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/EscapeUtil.html#unescapeXml-java.lang.String-">unescapeXml(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/EscapeUtil.html" title="com.huazheng.tunny.tools.text中的类">EscapeUtil</a></dt>
<dd>
<div class="block">Xml转码，XML格式的字符串解码为普通字符串.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtil.html#union-java.util.List-java.util.List-">union(List&lt;? extends E&gt;, List&lt;? extends E&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtil.html" title="com.huazheng.tunny.tools.collection中的类">ListUtil</a></dt>
<dd>
<div class="block">list1,list2的并集（在list1或list2中的对象），产生新List
 
 对比Apache Common Collection4 ListUtils, 优化了初始大小</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/SetUtil.html#unionView-java.util.Set-java.util.Set-">unionView(Set&lt;? extends E&gt;, Set&lt;? extends E&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/SetUtil.html" title="com.huazheng.tunny.tools.collection中的类">SetUtil</a></dt>
<dd>
<div class="block">set1, set2的并集（在set1或set2的对象）的只读view，不复制产生新的Set对象.</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/number/UnitConverter.html" title="com.huazheng.tunny.tools.number中的类"><span class="typeNameLink">UnitConverter</span></a> - <a href="../com/huazheng/tunny/tools/number/package-summary.html">com.huazheng.tunny.tools.number</a>中的类</dt>
<dd>
<div class="block">1.将带单位的时间，大小字符串转换为数字. copy from Facebook
 https://github.com/facebook/jcommon/blob/master/config/src/main/java/com/facebook/config/ConfigUtil.java

 2.将数字转为带单位的字符串</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/UnitConverter.html#UnitConverter--">UnitConverter()</a></span> - 类 的构造器com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/UnitConverter.html" title="com.huazheng.tunny.tools.number中的类">UnitConverter</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/number/UnitConverterTest.html" title="com.huazheng.tunny.tools.number中的类"><span class="typeNameLink">UnitConverterTest</span></a> - <a href="../com/huazheng/tunny/tools/number/package-summary.html">com.huazheng.tunny.tools.number</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/UnitConverterTest.html#UnitConverterTest--">UnitConverterTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/UnitConverterTest.html" title="com.huazheng.tunny.tools.number中的类">UnitConverterTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtil.html#unmodifiableList-java.util.List-">unmodifiableList(List&lt;? extends T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtil.html" title="com.huazheng.tunny.tools.collection中的类">ListUtil</a></dt>
<dd>
<div class="block">返回包装后不可修改的List.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtil.html#unmodifiableMap-java.util.Map-">unmodifiableMap(Map&lt;? extends K, ? extends V&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtil.html" title="com.huazheng.tunny.tools.collection中的类">MapUtil</a></dt>
<dd>
<div class="block">返回包装后不可修改的Map.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/SetUtil.html#unmodifiableSet-java.util.Set-">unmodifiableSet(Set&lt;? extends T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/SetUtil.html" title="com.huazheng.tunny.tools.collection中的类">SetUtil</a></dt>
<dd>
<div class="block">返回包装后不可修改的Set.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtil.html#unmodifiableSortedMap-java.util.SortedMap-">unmodifiableSortedMap(SortedMap&lt;K, ? extends V&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtil.html" title="com.huazheng.tunny.tools.collection中的类">MapUtil</a></dt>
<dd>
<div class="block">返回包装后不可修改的有序Map.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/ExceptionUtil.html#unwrap-java.lang.Throwable-">unwrap(Throwable)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/ExceptionUtil.html" title="com.huazheng.tunny.tools.base中的类">ExceptionUtil</a></dt>
<dd>
<div class="block">如果是著名的包裹类，从cause中获得真正异常.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/ExceptionUtilTest.html#unwrap--">unwrap()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/ExceptionUtilTest.html" title="com.huazheng.tunny.tools.base中的类">ExceptionUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/ExceptionUtil.html#unwrapAndUnchecked-java.lang.Throwable-">unwrapAndUnchecked(Throwable)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/ExceptionUtil.html" title="com.huazheng.tunny.tools.base中的类">ExceptionUtil</a></dt>
<dd>
<div class="block">组合unwrap与unchecked，用于处理反射/Callable的异常</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassUtil.html#unwrapCglib-java.lang.Object-">unwrapCglib(Object)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtil</a></dt>
<dd>
<div class="block">获取CGLib处理过后的实体的原Class.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/JsonMapper.html#update-java.lang.String-java.lang.Object-">update(String, Object)</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/JsonMapper.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapper</a></dt>
<dd>
<div class="block">当JSON里只含有Bean的部分属性時，更新一個已存在Bean，只覆盖該部分的属性.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.html#update--">update()</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapperTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/ClockUtil.DummyClock.html#updateNow-java.util.Date-">updateNow(Date)</a></span> - 类 中的方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/ClockUtil.DummyClock.html" title="com.huazheng.tunny.tools.time中的类">ClockUtil.DummyClock</a></dt>
<dd>
<div class="block">重新设置日期.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/ClockUtil.DummyClock.html#updateNow-long-">updateNow(long)</a></span> - 类 中的方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/ClockUtil.DummyClock.html" title="com.huazheng.tunny.tools.time中的类">ClockUtil.DummyClock</a></dt>
<dd>
<div class="block">重新设置时间.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/EscapeUtil.html#urlDecode-java.lang.String-">urlDecode(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/EscapeUtil.html" title="com.huazheng.tunny.tools.text中的类">EscapeUtil</a></dt>
<dd>
<div class="block">URL 解码, Encode默认为UTF-8.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/EscapeUtil.html#urlEncode-java.lang.String-">urlEncode(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/EscapeUtil.html" title="com.huazheng.tunny.tools.text中的类">EscapeUtil</a></dt>
<dd>
<div class="block">URL 编码, Encode默认为UTF-8.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/EscapeUtilTest.html#urlEncode--">urlEncode()</a></span> - 类 中的方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/EscapeUtilTest.html" title="com.huazheng.tunny.tools.text中的类">EscapeUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/io/URLResourceTest.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">URLResourceTest</span></a> - <a href="../com/huazheng/tunny/tools/io/package-summary.html">com.huazheng.tunny.tools.io</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/URLResourceTest.html#URLResourceTest--">URLResourceTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/URLResourceTest.html" title="com.huazheng.tunny.tools.io中的类">URLResourceTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/io/URLResourceUtil.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">URLResourceUtil</span></a> - <a href="../com/huazheng/tunny/tools/io/package-summary.html">com.huazheng.tunny.tools.io</a>中的类</dt>
<dd>
<div class="block">兼容文件url为无前缀, classpath:, file:// 三种方式的Resource读取工具集
 
 e.g: classpath:com/myapp/config.xml, file:///data/config.xml, /data/config.xml
 
 参考Spring ResourceUtils</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/URLResourceUtil.html#URLResourceUtil--">URLResourceUtil()</a></span> - 类 的构造器com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/URLResourceUtil.html" title="com.huazheng.tunny.tools.io中的类">URLResourceUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/Charsets.html#US_ASCII">US_ASCII</a></span> - 类 中的静态变量com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/Charsets.html" title="com.huazheng.tunny.tools.text中的类">Charsets</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/ClockUtil.html#useDefaultClock--">useDefaultClock()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/ClockUtil.html" title="com.huazheng.tunny.tools.time中的类">ClockUtil</a></dt>
<dd>
<div class="block">重置为默认Clock</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/ClockUtil.html#useDummyClock--">useDummyClock()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/ClockUtil.html" title="com.huazheng.tunny.tools.time中的类">ClockUtil</a></dt>
<dd>
<div class="block">切换为DummyClock，使用系统时间为初始时间, 单个测试完成后需要调用useDefaultClock()切换回去.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/ClockUtil.html#useDummyClock-long-">useDummyClock(long)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/ClockUtil.html" title="com.huazheng.tunny.tools.time中的类">ClockUtil</a></dt>
<dd>
<div class="block">切换为DummyClock，单个测试完成后需要调用useDefaultClock()切换回去.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/ClockUtil.html#useDummyClock-java.util.Date-">useDummyClock(Date)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/ClockUtil.html" title="com.huazheng.tunny.tools.time中的类">ClockUtil</a></dt>
<dd>
<div class="block">切换为DummyClock，单个测试完成后需要调用useDefaultClock()切换回去.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/Platforms.html#USER_HOME">USER_HOME</a></span> - 类 中的静态变量com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/Platforms.html" title="com.huazheng.tunny.tools.base中的类">Platforms</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/MoreStringUtil.html#utf8EncodedLength-java.lang.CharSequence-">utf8EncodedLength(CharSequence)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/MoreStringUtil.html" title="com.huazheng.tunny.tools.text中的类">MoreStringUtil</a></dt>
<dd>
<div class="block">计算字符串被UTF8编码后的字节数 via guava</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/MoreStringUtilTest.html#utf8EncodedLength--">utf8EncodedLength()</a></span> - 类 中的方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/MoreStringUtilTest.html" title="com.huazheng.tunny.tools.text中的类">MoreStringUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/Charsets.html#UTF_8">UTF_8</a></span> - 类 中的静态变量com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/Charsets.html" title="com.huazheng.tunny.tools.text中的类">Charsets</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/Charsets.html#UTF_8_NAME">UTF_8_NAME</a></span> - 类 中的静态变量com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/Charsets.html" title="com.huazheng.tunny.tools.text中的类">Charsets</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-20.html">上一个字母</a></li>
<li><a href="index-22.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-21.html" target="_top">框架</a></li>
<li><a href="index-21.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
