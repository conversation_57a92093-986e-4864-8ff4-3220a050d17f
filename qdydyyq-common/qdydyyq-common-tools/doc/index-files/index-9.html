<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:36 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>I - 索引</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="I - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-8.html">上一个字母</a></li>
<li><a href="index-10.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-9.html" target="_top">框架</a></li>
<li><a href="index-9.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a name="I:I">
<!--   -->
</a>
<h2 class="title">I</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/ClockUtil.DummyClock.html#increaseTime-int-">increaseTime(int)</a></span> - 类 中的方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/ClockUtil.DummyClock.html" title="com.huazheng.tunny.tools.time中的类">ClockUtil.DummyClock</a></dt>
<dd>
<div class="block">滚动时间.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#increment--">increment()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.jsr166e.<a href="../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html" title="com.huazheng.tunny.tools.concurrent.jsr166e中的类">LongAdder</a></dt>
<dd>
<div class="block">Equivalent to <code>add(1)</code>.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/net/IPUtilTest.html#inetAddress--">inetAddress()</a></span> - 类 中的方法com.huazheng.tunny.tools.net.<a href="../com/huazheng/tunny/tools/net/IPUtilTest.html" title="com.huazheng.tunny.tools.net中的类">IPUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.TestBean.html#inspectPrivateField--">inspectPrivateField()</a></span> - 类 中的方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.TestBean.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtilTest.TestBean</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.TestBean.html#inspectPublicField--">inspectPublicField()</a></span> - 类 中的方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.TestBean.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtilTest.TestBean</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/JsonMapper.html#INSTANCE">INSTANCE</a></span> - 类 中的静态变量com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/JsonMapper.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapper</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ArrayUtil.html#intAsList-int...-">intAsList(int...)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ArrayUtil.html" title="com.huazheng.tunny.tools.collection中的类">ArrayUtil</a></dt>
<dd>
<div class="block">Arrays.asList()的加强版, 返回一个底层为原始类型int的List
 
 与保存Integer相比节约空间，同时只在读取数据时AutoBoxing.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/ValueValidator.Validator.html#INTEGER_GT_ZERO_VALIDATOR">INTEGER_GT_ZERO_VALIDATOR</a></span> - 接口 中的静态变量com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/ValueValidator.Validator.html" title="com.huazheng.tunny.tools.base中的接口">ValueValidator.Validator</a></dt>
<dd>
<div class="block">校验器: 数值配置不为null, 且大于0较验</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.TestBean.html#integerType-java.lang.Integer-">integerType(Integer)</a></span> - 类 中的方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.TestBean.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtilTest.TestBean</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtil.html#intersection-java.util.List-java.util.List-">intersection(List&lt;? extends T&gt;, List&lt;? extends T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtil.html" title="com.huazheng.tunny.tools.collection中的类">ListUtil</a></dt>
<dd>
<div class="block">list1, list2的交集（同时在list1和list2的对象），产生新List
 
 copy from Apache Common Collection4 ListUtils，但其做了不合理的去重，因此重新改为性能较低但不去重的版本
 
 与List.retainAll()相比，考虑了的List中相同元素出现的次数, 如"a"在list1出现两次，而在list2中只出现一次，则交集里会保留一个"a".</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/SetUtil.html#intersectionView-java.util.Set-java.util.Set-">intersectionView(Set&lt;E&gt;, Set&lt;?&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/SetUtil.html" title="com.huazheng.tunny.tools.collection中的类">SetUtil</a></dt>
<dd>
<div class="block">set1, set2的交集（同时在set1和set2的对象）的只读view，不复制产生新的Set对象.</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类"><span class="typeNameLink">IntObjectHashMap</span></a>&lt;<a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html" title="IntObjectHashMap中的类型参数">V</a>&gt; - <a href="../com/huazheng/tunny/tools/collection/type/primitive/package-summary.html">com.huazheng.tunny.tools.collection.type.primitive</a>中的类</dt>
<dd>
<div class="block">移植Netty 4.1.9的Key为原子类型的集合类, 在数据结构上与HashMap不一样，空间占用与读写性能俱比原来更优.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html#IntObjectHashMap--">IntObjectHashMap()</a></span> - 类 的构造器com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">IntObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html#IntObjectHashMap-int-">IntObjectHashMap(int)</a></span> - 类 的构造器com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">IntObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html#IntObjectHashMap-int-float-">IntObjectHashMap(int, float)</a></span> - 类 的构造器com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">IntObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口"><span class="typeNameLink">IntObjectMap</span></a>&lt;<a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectMap.html" title="IntObjectMap中的类型参数">V</a>&gt; - <a href="../com/huazheng/tunny/tools/collection/type/primitive/package-summary.html">com.huazheng.tunny.tools.collection.type.primitive</a>中的接口</dt>
<dd>
<div class="block">Interface for a primitive map that uses <code>int</code>s as keys.</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectMap.PrimitiveEntry.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口"><span class="typeNameLink">IntObjectMap.PrimitiveEntry</span></a>&lt;<a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectMap.PrimitiveEntry.html" title="IntObjectMap.PrimitiveEntry中的类型参数">V</a>&gt; - <a href="../com/huazheng/tunny/tools/collection/type/primitive/package-summary.html">com.huazheng.tunny.tools.collection.type.primitive</a>中的接口</dt>
<dd>
<div class="block">A primitive entry in the map, provided by the iterator from <a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectMap.html#entries--"><code>IntObjectMap.entries()</code></a></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtilTest.html#intSystemProperty--">intSystemProperty()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtilTest.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/net/IPUtil.html#intToIpv4String-int-">intToIpv4String(int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.net.<a href="../com/huazheng/tunny/tools/net/IPUtil.html" title="com.huazheng.tunny.tools.net中的类">IPUtil</a></dt>
<dd>
<div class="block">int转换到IPV4 String, from Netty NetUtil</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.TestBean.html#intType-int-">intType(int)</a></span> - 类 中的方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.TestBean.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtilTest.TestBean</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#intValue--">intValue()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.jsr166e.<a href="../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html" title="com.huazheng.tunny.tools.concurrent.jsr166e中的类">LongAdder</a></dt>
<dd>
<div class="block">Returns the <a href="../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#sum--"><code>LongAdder.sum()</code></a> as an <code>int</code> after a narrowing
 primitive conversion.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#invokeConstructor-java.lang.Class-java.lang.Object...-">invokeConstructor(Class&lt;T&gt;, Object...)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtil</a></dt>
<dd>
<div class="block">调用构造函数.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.html#invokeConstructor--">invokeConstructor()</a></span> - 类 中的方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#invokeGetter-java.lang.Object-java.lang.String-">invokeGetter(Object, String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtil</a></dt>
<dd>
<div class="block">调用Getter方法, 无视private/protected修饰符.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.html#invokeGetterAndSetter--">invokeGetterAndSetter()</a></span> - 类 中的方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#invokeMethod-java.lang.Object-java.lang.String-java.lang.Object...-">invokeMethod(Object, String, Object...)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtil</a></dt>
<dd>
<div class="block">反射调用对象方法, 无视private/protected修饰符.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#invokeMethod-java.lang.Object-java.lang.String-java.lang.Object:A-java.lang.Class:A-">invokeMethod(Object, String, Object[], Class&lt;?&gt;[])</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtil</a></dt>
<dd>
<div class="block">反射调用对象方法, 无视private/protected修饰符.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#invokeMethod-java.lang.Object-java.lang.reflect.Method-java.lang.Object...-">invokeMethod(Object, Method, Object...)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtil</a></dt>
<dd>
<div class="block">调用预先获取的Method，用于反复调用的场景</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.html#invokeMethod--">invokeMethod()</a></span> - 类 中的方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#invokeMethodByName-java.lang.Object-java.lang.String-java.lang.Object:A-">invokeMethodByName(Object, String, Object[])</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtil</a></dt>
<dd>
<div class="block">反射调用对象方法, 无视private/protected修饰符
 
 只匹配函数名，如果有多个同名函数调用第一个.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#invokeSetter-java.lang.Object-java.lang.String-java.lang.Object-">invokeSetter(Object, String, Object)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtil</a></dt>
<dd>
<div class="block">调用Setter方法, 无视private/protected修饰符, 按传入value的类型匹配函数.</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/io/IOUtil.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">IOUtil</span></a> - <a href="../com/huazheng/tunny/tools/io/package-summary.html">com.huazheng.tunny.tools.io</a>中的类</dt>
<dd>
<div class="block">IO Stream/Reader相关工具集.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/IOUtil.html#IOUtil--">IOUtil()</a></span> - 类 的构造器com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/IOUtil.html" title="com.huazheng.tunny.tools.io中的类">IOUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/io/IOUtilTest.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">IOUtilTest</span></a> - <a href="../com/huazheng/tunny/tools/io/package-summary.html">com.huazheng.tunny.tools.io</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/IOUtilTest.html#IOUtilTest--">IOUtilTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/IOUtilTest.html" title="com.huazheng.tunny.tools.io中的类">IOUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/net/IPUtil.html" title="com.huazheng.tunny.tools.net中的类"><span class="typeNameLink">IPUtil</span></a> - <a href="../com/huazheng/tunny/tools/net/package-summary.html">com.huazheng.tunny.tools.net</a>中的类</dt>
<dd>
<div class="block">InetAddress工具类，基于Guava的InetAddresses.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/net/IPUtil.html#IPUtil--">IPUtil()</a></span> - 类 的构造器com.huazheng.tunny.tools.net.<a href="../com/huazheng/tunny/tools/net/IPUtil.html" title="com.huazheng.tunny.tools.net中的类">IPUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/net/IPUtilTest.html" title="com.huazheng.tunny.tools.net中的类"><span class="typeNameLink">IPUtilTest</span></a> - <a href="../com/huazheng/tunny/tools/net/package-summary.html">com.huazheng.tunny.tools.net</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/net/IPUtilTest.html#IPUtilTest--">IPUtilTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.net.<a href="../com/huazheng/tunny/tools/net/IPUtilTest.html" title="com.huazheng.tunny.tools.net中的类">IPUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/net/IPUtil.html#ipv4StringToInt-java.lang.String-">ipv4StringToInt(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.net.<a href="../com/huazheng/tunny/tools/net/IPUtil.html" title="com.huazheng.tunny.tools.net中的类">IPUtil</a></dt>
<dd>
<div class="block">Ipv4 String 转换到int</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/Platforms.html#IS_ATLEASET_JAVA7">IS_ATLEASET_JAVA7</a></span> - 类 中的静态变量com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/Platforms.html" title="com.huazheng.tunny.tools.base中的类">Platforms</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/Platforms.html#IS_ATLEASET_JAVA8">IS_ATLEASET_JAVA8</a></span> - 类 中的静态变量com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/Platforms.html" title="com.huazheng.tunny.tools.base中的类">Platforms</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/Platforms.html#IS_JAVA7">IS_JAVA7</a></span> - 类 中的静态变量com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/Platforms.html" title="com.huazheng.tunny.tools.base中的类">Platforms</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/Platforms.html#IS_JAVA8">IS_JAVA8</a></span> - 类 中的静态变量com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/Platforms.html" title="com.huazheng.tunny.tools.base中的类">Platforms</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/Platforms.html#IS_LINUX">IS_LINUX</a></span> - 类 中的静态变量com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/Platforms.html" title="com.huazheng.tunny.tools.base中的类">Platforms</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/Platforms.html#IS_UNIX">IS_UNIX</a></span> - 类 中的静态变量com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/Platforms.html" title="com.huazheng.tunny.tools.base中的类">Platforms</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/Platforms.html#IS_WINDOWS">IS_WINDOWS</a></span> - 类 中的静态变量com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/Platforms.html" title="com.huazheng.tunny.tools.base中的类">Platforms</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#isBetween-java.util.Date-java.util.Date-java.util.Date-">isBetween(Date, Date, Date)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">判断日期是否在范围内，包含相等的日期</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html#isCancelled--">isCancelled()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.type.<a href="../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html" title="com.huazheng.tunny.tools.concurrent.type中的类">BasicFuture</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/ExceptionUtil.html#isCausedBy-java.lang.Throwable-java.lang.Class...-">isCausedBy(Throwable, Class&lt;? extends Exception&gt;...)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/ExceptionUtil.html" title="com.huazheng.tunny.tools.base中的类">ExceptionUtil</a></dt>
<dd>
<div class="block">判断异常是否由某些底层的异常引起.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/TextValidator.html#isDate-java.lang.CharSequence-">isDate(CharSequence)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/TextValidator.html" title="com.huazheng.tunny.tools.text中的类">TextValidator</a></dt>
<dd>
<div class="block">验证yyyy-MM-dd格式的日期校验，已考虑平闰年</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/TextValidatorTest.html#isDate--">isDate()</a></span> - 类 中的方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/TextValidatorTest.html" title="com.huazheng.tunny.tools.text中的类">TextValidatorTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#isDirExists-java.lang.String-">isDirExists(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">判断目录是否存在, from Jodd</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#isDirExists-java.nio.file.Path-">isDirExists(Path)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#isDirExists-java.io.File-">isDirExists(File)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">判断目录是否存在, from Jodd</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html#isDone--">isDone()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.type.<a href="../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html" title="com.huazheng.tunny.tools.concurrent.type中的类">BasicFuture</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/TextValidator.html#isEmail-java.lang.CharSequence-">isEmail(CharSequence)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/TextValidator.html" title="com.huazheng.tunny.tools.text中的类">TextValidator</a></dt>
<dd>
<div class="block">验证邮箱</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/TextValidatorTest.html#isEmail--">isEmail()</a></span> - 类 中的方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/TextValidatorTest.html" title="com.huazheng.tunny.tools.text中的类">TextValidatorTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/CollectionUtil.html#isEmpty-java.util.Collection-">isEmpty(Collection&lt;?&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/CollectionUtil.html" title="com.huazheng.tunny.tools.collection中的类">CollectionUtil</a></dt>
<dd>
<div class="block">判断是否为空.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtil.html#isEmpty-java.util.List-">isEmpty(List&lt;?&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtil.html" title="com.huazheng.tunny.tools.collection中的类">ListUtil</a></dt>
<dd>
<div class="block">判断是否为空.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtil.html#isEmpty-java.util.Map-">isEmpty(Map&lt;?, ?&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtil.html" title="com.huazheng.tunny.tools.collection中的类">MapUtil</a></dt>
<dd>
<div class="block">判断是否为空.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html#isEmpty--">isEmpty()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.<a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="com.huazheng.tunny.tools.collection.type中的类">ConcurrentHashSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html#isEmpty--">isEmpty()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">IntObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#isEmpty--">isEmpty()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">LongObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/vip/vjtools/test/log/LogbackListAppender.html#isEmpty--">isEmpty()</a></span> - 类 中的方法com.vip.vjtools.test.log.<a href="../com/vip/vjtools/test/log/LogbackListAppender.html" title="com.vip.vjtools.test.log中的类">LogbackListAppender</a></dt>
<dd>
<div class="block">判断是否有log.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#isFileExists-java.lang.String-">isFileExists(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">判断文件是否存在, from Jodd.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#isFileExists-java.io.File-">isFileExists(File)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">判断文件是否存在, from Jodd.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#isFileExists-java.nio.file.Path-">isFileExists(Path)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">判断文件是否存在, from Jodd.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtil.html#isHexNumber-java.lang.String-">isHexNumber(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtil.html" title="com.huazheng.tunny.tools.number中的类">NumberUtil</a></dt>
<dd>
<div class="block">判断字符串是否16进制</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/TextValidator.html#isIdCard-java.lang.CharSequence-">isIdCard(CharSequence)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/TextValidator.html" title="com.huazheng.tunny.tools.text中的类">TextValidator</a></dt>
<dd>
<div class="block">验证15或18位身份证号码</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/TextValidatorTest.html#isIdCard--">isIdCard()</a></span> - 类 中的方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/TextValidatorTest.html" title="com.huazheng.tunny.tools.text中的类">TextValidatorTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/TextValidator.html#isIp-java.lang.CharSequence-">isIp(CharSequence)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/TextValidator.html" title="com.huazheng.tunny.tools.text中的类">TextValidator</a></dt>
<dd>
<div class="block">验证IP地址</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/TextValidatorTest.html#isIp--">isIp()</a></span> - 类 中的方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/TextValidatorTest.html" title="com.huazheng.tunny.tools.text中的类">TextValidatorTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#isLeapYear-java.util.Date-">isLeapYear(Date)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">是否闰年.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#isLeapYear-int-">isLeapYear(int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">是否闰年，copy from Jodd Core的TimeUtil
 
 参数是公元计数, 如2016</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtilTest.html#isLeapYear--">isLeapYear()</a></span> - 类 中的方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtilTest.html" title="com.huazheng.tunny.tools.time中的类">DateUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/security/CryptoUtil.html#isMacValid-byte:A-byte:A-byte:A-">isMacValid(byte[], byte[], byte[])</a></span> - 类 中的静态方法com.huazheng.tunny.tools.security.<a href="../com/huazheng/tunny/tools/security/CryptoUtil.html" title="com.huazheng.tunny.tools.security中的类">CryptoUtil</a></dt>
<dd>
<div class="block">校验HMAC-SHA1签名是否正确.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/TextValidator.html#isMatch-java.util.regex.Pattern-java.lang.CharSequence-">isMatch(Pattern, CharSequence)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/TextValidator.html" title="com.huazheng.tunny.tools.text中的类">TextValidator</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/TextValidator.html#isMobileExact-java.lang.CharSequence-">isMobileExact(CharSequence)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/TextValidator.html" title="com.huazheng.tunny.tools.text中的类">TextValidator</a></dt>
<dd>
<div class="block">验证手机号（精确）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/TextValidatorTest.html#isMobileExact--">isMobileExact()</a></span> - 类 中的方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/TextValidatorTest.html" title="com.huazheng.tunny.tools.text中的类">TextValidatorTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/TextValidator.html#isMobileSimple-java.lang.CharSequence-">isMobileSimple(CharSequence)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/TextValidator.html" title="com.huazheng.tunny.tools.text中的类">TextValidator</a></dt>
<dd>
<div class="block">验证手机号（简单）</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/TextValidatorTest.html#isMobileSimple--">isMobileSimple()</a></span> - 类 中的方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/TextValidatorTest.html" title="com.huazheng.tunny.tools.text中的类">TextValidatorTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/CollectionUtil.html#isNotEmpty-java.util.Collection-">isNotEmpty(Collection&lt;?&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/CollectionUtil.html" title="com.huazheng.tunny.tools.collection中的类">CollectionUtil</a></dt>
<dd>
<div class="block">判断是否不为空.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtil.html#isNotEmpty-java.util.List-">isNotEmpty(List&lt;?&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtil.html" title="com.huazheng.tunny.tools.collection中的类">ListUtil</a></dt>
<dd>
<div class="block">判断是否不为空.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtil.html#isNotEmpty-java.util.Map-">isNotEmpty(Map&lt;?, ?&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtil.html" title="com.huazheng.tunny.tools.collection中的类">MapUtil</a></dt>
<dd>
<div class="block">判断是否为空.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtil.html#isNumber-java.lang.String-">isNumber(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtil.html" title="com.huazheng.tunny.tools.number中的类">NumberUtil</a></dt>
<dd>
<div class="block">判断字符串是否合法数字</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtilTest.html#isNumber--">isNumber()</a></span> - 类 中的方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtilTest.html" title="com.huazheng.tunny.tools.number中的类">NumberUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/Charsets.html#ISO_8859_1">ISO_8859_1</a></span> - 类 中的静态变量com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/Charsets.html" title="com.huazheng.tunny.tools.text中的类">Charsets</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/Charsets.html#ISO_8859_1_NAME">ISO_8859_1_NAME</a></span> - 类 中的静态变量com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/Charsets.html" title="com.huazheng.tunny.tools.text中的类">Charsets</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html#ISO_FORMAT">ISO_FORMAT</a></span> - 类 中的静态变量com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html" title="com.huazheng.tunny.tools.time中的类">DateFormatUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html#ISO_ON_DATE_FORMAT">ISO_ON_DATE_FORMAT</a></span> - 类 中的静态变量com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html" title="com.huazheng.tunny.tools.time中的类">DateFormatUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html#ISO_ON_SECOND_FORMAT">ISO_ON_SECOND_FORMAT</a></span> - 类 中的静态变量com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html" title="com.huazheng.tunny.tools.time中的类">DateFormatUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateFormatUtilTest.html#isoDateFormat--">isoDateFormat()</a></span> - 类 中的方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateFormatUtilTest.html" title="com.huazheng.tunny.tools.time中的类">DateFormatUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/net/NetUtil.html#isPortAvailable-int-">isPortAvailable(int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.net.<a href="../com/huazheng/tunny/tools/net/NetUtil.html" title="com.huazheng.tunny.tools.net中的类">NetUtil</a></dt>
<dd>
<div class="block">测试端口是否空闲可用, from Spring SocketUtils</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/MathUtil.html#isPowerOfTwo-int-">isPowerOfTwo(int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/MathUtil.html" title="com.huazheng.tunny.tools.number中的类">MathUtil</a></dt>
<dd>
<div class="block">是否2的倍数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/MathUtil.html#isPowerOfTwo-long-">isPowerOfTwo(long)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/MathUtil.html" title="com.huazheng.tunny.tools.number中的类">MathUtil</a></dt>
<dd>
<div class="block">是否2的倍数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassLoaderUtil.html#isPresent-java.lang.String-java.lang.ClassLoader-">isPresent(String, ClassLoader)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassLoaderUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ClassLoaderUtil</a></dt>
<dd>
<div class="block">探测类是否存在classpath中</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#isSameDay-java.util.Date-java.util.Date-">isSameDay(Date, Date)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">是否同一天.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtilTest.html#isSameDay--">isSameDay()</a></span> - 类 中的方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtilTest.html" title="com.huazheng.tunny.tools.time中的类">DateUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#isSameTime-java.util.Date-java.util.Date-">isSameTime(Date, Date)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">是否同一时刻.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassUtil.html#isSubClassOrInterfaceOf-java.lang.Class-java.lang.Class-">isSubClassOrInterfaceOf(Class, Class)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtil</a></dt>
<dd>
<div class="block">https://github.com/linkedin/linkedin-utils/blob/master/org.linkedin.util-core/src/main/java/org/linkedin/util/reflect/ReflectUtils.java
 
 The purpose of this method is somewhat to provide a better naming / documentation than the javadoc of
 <code>Class.isAssignableFrom</code> method.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/TextValidator.html#isTel-java.lang.CharSequence-">isTel(CharSequence)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/TextValidator.html" title="com.huazheng.tunny.tools.text中的类">TextValidator</a></dt>
<dd>
<div class="block">验证固定电话号码</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/TextValidatorTest.html#isTel--">isTel()</a></span> - 类 中的方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/TextValidatorTest.html" title="com.huazheng.tunny.tools.text中的类">TextValidatorTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/TextValidator.html#isUrl-java.lang.CharSequence-">isUrl(CharSequence)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/TextValidator.html" title="com.huazheng.tunny.tools.text中的类">TextValidator</a></dt>
<dd>
<div class="block">验证URL</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/TextValidatorTest.html#isUrl--">isUrl()</a></span> - 类 中的方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/TextValidatorTest.html" title="com.huazheng.tunny.tools.text中的类">TextValidatorTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html#iterator--">iterator()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.<a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="com.huazheng.tunny.tools.collection.type中的类">ConcurrentHashSet</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-8.html">上一个字母</a></li>
<li><a href="index-10.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-9.html" target="_top">框架</a></li>
<li><a href="index-9.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
