<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:36 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>N - 索引</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="N - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-13.html">上一个字母</a></li>
<li><a href="index-15.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-14.html" target="_top">框架</a></li>
<li><a href="index-14.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a name="I:N">
<!--   -->
</a>
<h2 class="title">N</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Student.html#name">name</a></span> - 类 中的变量com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Student.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.Student</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.StudentVO.html#name">name</a></span> - 类 中的变量com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.StudentVO.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.StudentVO</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/ClockUtil.Clock.html#nanoTime--">nanoTime()</a></span> - 接口 中的方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/ClockUtil.Clock.html" title="com.huazheng.tunny.tools.time中的接口">ClockUtil.Clock</a></dt>
<dd>
<div class="block">操作系统启动到现在的纳秒数，与系统时间是完全独立的两个时间体系</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/ClockUtil.DefaultClock.html#nanoTime--">nanoTime()</a></span> - 类 中的方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/ClockUtil.DefaultClock.html" title="com.huazheng.tunny.tools.time中的类">ClockUtil.DefaultClock</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/ClockUtil.DummyClock.html#nanoTime--">nanoTime()</a></span> - 类 中的方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/ClockUtil.DummyClock.html" title="com.huazheng.tunny.tools.time中的类">ClockUtil.DummyClock</a></dt>
<dd>
<div class="block">获取nanotime</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/ClockUtil.html#nanoTime--">nanoTime()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/ClockUtil.html" title="com.huazheng.tunny.tools.time中的类">ClockUtil</a></dt>
<dd>
<div class="block">操作系统启动到现在的纳秒数，与系统时间是完全独立的两个时间体系</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/BooleanUtil.html#negate-boolean-">negate(boolean)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/BooleanUtil.html" title="com.huazheng.tunny.tools.base中的类">BooleanUtil</a></dt>
<dd>
<div class="block">取反</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/BooleanUtil.html#negate-java.lang.Boolean-">negate(Boolean)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/BooleanUtil.html" title="com.huazheng.tunny.tools.base中的类">BooleanUtil</a></dt>
<dd>
<div class="block">取反</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/net/NetUtil.html" title="com.huazheng.tunny.tools.net中的类"><span class="typeNameLink">NetUtil</span></a> - <a href="../com/huazheng/tunny/tools/net/package-summary.html">com.huazheng.tunny.tools.net</a>中的类</dt>
<dd>
<div class="block">关于网络的工具类.
 
 1.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/net/NetUtil.html#NetUtil--">NetUtil()</a></span> - 类 的构造器com.huazheng.tunny.tools.net.<a href="../com/huazheng/tunny/tools/net/NetUtil.html" title="com.huazheng.tunny.tools.net中的类">NetUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/net/NetUtilTest.html" title="com.huazheng.tunny.tools.net中的类"><span class="typeNameLink">NetUtilTest</span></a> - <a href="../com/huazheng/tunny/tools/net/package-summary.html">com.huazheng.tunny.tools.net</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/net/NetUtilTest.html#NetUtilTest--">NetUtilTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.net.<a href="../com/huazheng/tunny/tools/net/NetUtilTest.html" title="com.huazheng.tunny.tools.net中的类">NetUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/Sampler.NeverSampler.html#NeverSampler--">NeverSampler()</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/Sampler.NeverSampler.html" title="com.huazheng.tunny.tools.concurrent中的类">Sampler.NeverSampler</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ArrayUtil.html#newArray-java.lang.Class-int-">newArray(Class&lt;T&gt;, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ArrayUtil.html" title="com.huazheng.tunny.tools.collection中的类">ArrayUtil</a></dt>
<dd>
<div class="block">传入类型与大小创建数组.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/QueueUtil.html#newArrayBlockingQueue-int-">newArrayBlockingQueue(int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/QueueUtil.html" title="com.huazheng.tunny.tools.collection中的类">QueueUtil</a></dt>
<dd>
<div class="block">创建并发阻塞情况下，长度受限，更节约内存，但共用一把锁的队列（无双端队列实现）.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/QueueUtil.html#newArrayDeque-int-">newArrayDeque(int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/QueueUtil.html" title="com.huazheng.tunny.tools.collection中的类">QueueUtil</a></dt>
<dd>
<div class="block">创建ArrayDeque (JDK无ArrayQueue)
 
 需设置初始长度，默认为16，数组满时成倍扩容</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtil.html#newArrayList--">newArrayList()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtil.html" title="com.huazheng.tunny.tools.collection中的类">ListUtil</a></dt>
<dd>
<div class="block">根据等号左边的类型，构造类型正确的ArrayList.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtil.html#newArrayList-T...-">newArrayList(T...)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtil.html" title="com.huazheng.tunny.tools.collection中的类">ListUtil</a></dt>
<dd>
<div class="block">根据等号左边的类型，构造类型正确的ArrayList, 并初始化元素.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtil.html#newArrayList-java.lang.Iterable-">newArrayList(Iterable&lt;T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtil.html" title="com.huazheng.tunny.tools.collection中的类">ListUtil</a></dt>
<dd>
<div class="block">根据等号左边的类型，构造类型正确的ArrayList, 并初始化元素.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtil.html#newArrayListWithCapacity-int-">newArrayListWithCapacity(int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtil.html" title="com.huazheng.tunny.tools.collection中的类">ListUtil</a></dt>
<dd>
<div class="block">根据等号左边的类型，构造类型正确的ArrayList, 并初始化数组大小.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/QueueUtil.html#newBlockingDeque-int-">newBlockingDeque(int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/QueueUtil.html" title="com.huazheng.tunny.tools.collection中的类">QueueUtil</a></dt>
<dd>
<div class="block">创建并发阻塞情况下，长度受限，头队尾两把锁, 但使用更多内存的双端队列.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/QueueUtil.html#newBlockingUnlimitDeque--">newBlockingUnlimitDeque()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/QueueUtil.html" title="com.huazheng.tunny.tools.collection中的类">QueueUtil</a></dt>
<dd>
<div class="block">创建并发阻塞情况下，长度不受限的双端队列.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/QueueUtil.html#newBlockingUnlimitQueue--">newBlockingUnlimitQueue()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/QueueUtil.html" title="com.huazheng.tunny.tools.collection中的类">QueueUtil</a></dt>
<dd>
<div class="block">创建并发阻塞情况下，长度不受限的队列.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtil.html#newConcurrentHashMap--">newConcurrentHashMap()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtil.html" title="com.huazheng.tunny.tools.collection中的类">MapUtil</a></dt>
<dd>
<div class="block">根据等号左边的类型，构造类型正确的ConcurrentHashMap.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/SetUtil.html#newConcurrentHashSet--">newConcurrentHashSet()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/SetUtil.html" title="com.huazheng.tunny.tools.collection中的类">SetUtil</a></dt>
<dd>
<div class="block">根据等号左边的类型，构造类型正确的ConcurrentHashSet</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/QueueUtil.html#newConcurrentNonBlockingDeque--">newConcurrentNonBlockingDeque()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/QueueUtil.html" title="com.huazheng.tunny.tools.collection中的类">QueueUtil</a></dt>
<dd>
<div class="block">创建无阻塞情况下，性能最优的并发双端队列</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/QueueUtil.html#newConcurrentNonBlockingQueue--">newConcurrentNonBlockingQueue()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/QueueUtil.html" title="com.huazheng.tunny.tools.collection中的类">QueueUtil</a></dt>
<dd>
<div class="block">创建无阻塞情况下，性能最优的并发队列</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtil.html#newConcurrentSortedMap--">newConcurrentSortedMap()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtil.html" title="com.huazheng.tunny.tools.collection中的类">MapUtil</a></dt>
<dd>
<div class="block">根据等号左边的类型，构造类型正确的ConcurrentSkipListMap.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtil.html#newCopyOnWriteArrayList--">newCopyOnWriteArrayList()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtil.html" title="com.huazheng.tunny.tools.collection中的类">ListUtil</a></dt>
<dd>
<div class="block">根据等号左边的类型，构造类型正确的CopyOnWriteArrayList.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtil.html#newCopyOnWriteArrayList-T...-">newCopyOnWriteArrayList(T...)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtil.html" title="com.huazheng.tunny.tools.collection中的类">ListUtil</a></dt>
<dd>
<div class="block">根据等号左边的类型，构造类型转换的CopyOnWriteArrayList, 并初始化元素.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtil.html#newEnumMap-java.lang.Class-">newEnumMap(Class&lt;K&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtil.html" title="com.huazheng.tunny.tools.collection中的类">MapUtil</a></dt>
<dd>
<div class="block">相比HashMap，当key是枚举类时, 性能与空间占用俱佳.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtil.html#newHashMap--">newHashMap()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtil.html" title="com.huazheng.tunny.tools.collection中的类">MapUtil</a></dt>
<dd>
<div class="block">根据等号左边的类型, 构造类型正确的HashMap.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtil.html#newHashMap-K-V-">newHashMap(K, V)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtil.html" title="com.huazheng.tunny.tools.collection中的类">MapUtil</a></dt>
<dd>
<div class="block">根据等号左边的类型, 构造类型正确的HashMap.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtil.html#newHashMap-K:A-V:A-">newHashMap(K[], V[])</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtil.html" title="com.huazheng.tunny.tools.collection中的类">MapUtil</a></dt>
<dd>
<div class="block">根据等号左边的类型, 构造类型正确的HashMap.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtil.html#newHashMap-java.util.List-java.util.List-">newHashMap(List&lt;K&gt;, List&lt;V&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtil.html" title="com.huazheng.tunny.tools.collection中的类">MapUtil</a></dt>
<dd>
<div class="block">根据等号左边的类型, 构造类型正确的HashMap.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtil.html#newHashMapWithCapacity-int-float-">newHashMapWithCapacity(int, float)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtil.html" title="com.huazheng.tunny.tools.collection中的类">MapUtil</a></dt>
<dd>
<div class="block">根据等号左边的类型, 构造类型正确的HashMap.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/SetUtil.html#newHashSet--">newHashSet()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/SetUtil.html" title="com.huazheng.tunny.tools.collection中的类">SetUtil</a></dt>
<dd>
<div class="block">根据等号左边的类型，构造类型正确的HashSet.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/SetUtil.html#newHashSet-T...-">newHashSet(T...)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/SetUtil.html" title="com.huazheng.tunny.tools.collection中的类">SetUtil</a></dt>
<dd>
<div class="block">根据等号左边的类型，构造类型正确的HashSet, 并初始化元素.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/SetUtil.html#newHashSet-java.lang.Iterable-">newHashSet(Iterable&lt;? extends T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/SetUtil.html" title="com.huazheng.tunny.tools.collection中的类">SetUtil</a></dt>
<dd>
<div class="block">HashSet涉及HashMap大小，因此建议在构造时传入需要初始的集合，其他如TreeSet不需要.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/SetUtil.html#newHashSetWithCapacity-int-">newHashSetWithCapacity(int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/SetUtil.html" title="com.huazheng.tunny.tools.collection中的类">SetUtil</a></dt>
<dd>
<div class="block">创建HashSet并设置初始大小，因为HashSet内部是HashMap，会计算LoadFactor后的真实大小.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/QueueUtil.html#newLinkedBlockingQueue-int-">newLinkedBlockingQueue(int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/QueueUtil.html" title="com.huazheng.tunny.tools.collection中的类">QueueUtil</a></dt>
<dd>
<div class="block">创建并发阻塞情况下，长度受限，头队尾两把锁, 但使用更多内存的队列.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/QueueUtil.html#newLinkedDeque--">newLinkedDeque()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/QueueUtil.html" title="com.huazheng.tunny.tools.collection中的类">QueueUtil</a></dt>
<dd>
<div class="block">创建LinkedDeque (LinkedList实现了Deque接口)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtil.html#newLinkedList--">newLinkedList()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtil.html" title="com.huazheng.tunny.tools.collection中的类">ListUtil</a></dt>
<dd>
<div class="block">根据等号左边的类型，构造类型正确的LinkedList.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ListUtil.html#newLinkedList-java.lang.Iterable-">newLinkedList(Iterable&lt;? extends T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ListUtil.html" title="com.huazheng.tunny.tools.collection中的类">ListUtil</a></dt>
<dd>
<div class="block">根据等号左边的类型，构造类型正确的LinkedList.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/SetUtil.html#newSetFromMap-java.util.Map-">newSetFromMap(Map&lt;T, Boolean&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/SetUtil.html" title="com.huazheng.tunny.tools.collection中的类">SetUtil</a></dt>
<dd>
<div class="block">从Map构造Set的大杀器, 可以用来制造各种Set</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtil.html#newSortedMap--">newSortedMap()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtil.html" title="com.huazheng.tunny.tools.collection中的类">MapUtil</a></dt>
<dd>
<div class="block">根据等号左边的类型，构造类型正确的TreeMap.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtil.html#newSortedMap-java.util.Comparator-">newSortedMap(Comparator&lt;C&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtil.html" title="com.huazheng.tunny.tools.collection中的类">MapUtil</a></dt>
<dd>
<div class="block">根据等号左边的类型，构造类型正确的TreeMap.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/SetUtil.html#newSortedSet--">newSortedSet()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/SetUtil.html" title="com.huazheng.tunny.tools.collection中的类">SetUtil</a></dt>
<dd>
<div class="block">根据等号左边的类型，构造类型正确的TreeSet, 通过实现了Comparable的元素自身进行排序.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/SetUtil.html#newSortedSet-java.util.Comparator-">newSortedSet(Comparator&lt;? super T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/SetUtil.html" title="com.huazheng.tunny.tools.collection中的类">SetUtil</a></dt>
<dd>
<div class="block">根据等号左边的类型，构造类型正确的TreeSet, 并设置comparator.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtilTest.TestPropertiesListener.html#newValue">newValue</a></span> - 类 中的变量com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtilTest.TestPropertiesListener.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtilTest.TestPropertiesListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#nextDate-java.util.Date-">nextDate(Date)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">2016-11-10 07:33:23, 则返回2016-11-11 00:00:00</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#nextDouble--">nextDouble()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">返回0-之间的double, 使用ThreadLocalRandom</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#nextDouble-java.util.Random-">nextDouble(Random)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">返回0-Double.MAX之间的double</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#nextDouble-double-">nextDouble(double)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">返回0-max之间的double, 使用ThreadLocalRandom
 
 注意：与JDK默认返回0-1的行为不一致.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#nextDouble-java.util.Random-double-">nextDouble(Random, double)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">返回0-max之间的double</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#nextDouble-double-double-">nextDouble(double, double)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">返回min-max之间的double,ThreadLocalRandom</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#nextDouble-java.util.Random-double-double-">nextDouble(Random, double, double)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">返回min-max之间的double</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#nextHour-java.util.Date-">nextHour(Date)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">2016-12-10 07:33:23, 则返回2016-12-10 08:00:00</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#nextInt--">nextInt()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">返回0到Intger.MAX_VALUE的随机Int, 使用ThreadLocalRandom.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#nextInt-java.util.Random-">nextInt(Random)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">返回0到Intger.MAX_VALUE的随机Int, 可传入ThreadLocalRandom或SecureRandom</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#nextInt-int-">nextInt(int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">返回0到max的随机Int, 使用ThreadLocalRandom.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#nextInt-java.util.Random-int-">nextInt(Random, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">返回0到max的随机Int, 可传入SecureRandom或ThreadLocalRandom</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#nextInt-int-int-">nextInt(int, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">返回min到max的随机Int, 使用ThreadLocalRandom.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#nextInt-java.util.Random-int-int-">nextInt(Random, int, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">返回min到max的随机Int,可传入SecureRandom或ThreadLocalRandom.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#nextLong--">nextLong()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">返回0－Long.MAX_VALUE间的随机Long, 使用ThreadLocalRandom.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#nextLong-java.util.Random-">nextLong(Random)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">返回0－Long.MAX_VALUE间的随机Long, 可传入SecureRandom或ThreadLocalRandom</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#nextLong-long-">nextLong(long)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">返回0－max间的随机Long, 使用ThreadLocalRandom.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#nextLong-java.util.Random-long-">nextLong(Random, long)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">返回0-max间的随机Long, 可传入SecureRandom或ThreadLocalRandom</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#nextLong-long-long-">nextLong(long, long)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">返回min－max间的随机Long, 使用ThreadLocalRandom.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#nextLong-java.util.Random-long-long-">nextLong(Random, long, long)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">返回min-max间的随机Long,可传入SecureRandom或ThreadLocalRandom.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#nextMinute-java.util.Date-">nextMinute(Date)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">2016-12-10 07:33:23, 则返回2016-12-10 07:34:00</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#nextMonth-java.util.Date-">nextMonth(Date)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">2016-11-10 07:33:23, 则返回2016-12-1 00:00:00</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/MathUtil.html#nextPowerOfTwo-int-">nextPowerOfTwo(int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/MathUtil.html" title="com.huazheng.tunny.tools.number中的类">MathUtil</a></dt>
<dd>
<div class="block">往上找出最接近的2的倍数，比如15返回16， 17返回32.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/MathUtil.html#nextPowerOfTwo-long-">nextPowerOfTwo(long)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/MathUtil.html" title="com.huazheng.tunny.tools.number中的类">MathUtil</a></dt>
<dd>
<div class="block">往上找出最接近的2的倍数，比如15返回16， 17返回32.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#nextWeek-java.util.Date-">nextWeek(Date)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">2017-1-23 07:33:23, 则返回2017-1-22 00:00:00</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#nextYear-java.util.Date-">nextYear(Date)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">2016-11-10 07:33:23, 则返回2017-1-1 00:00:00</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/JsonMapper.html#nonEmptyMapper--">nonEmptyMapper()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/JsonMapper.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapper</a></dt>
<dd>
<div class="block">创建只输出非Null且非Empty(如List.isEmpty)的属性到Json字符串的Mapper.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/Concurrents.html#nonFairSemaphore-int-">nonFairSemaphore(int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/Concurrents.html" title="com.huazheng.tunny.tools.concurrent中的类">Concurrents</a></dt>
<dd>
<div class="block">返回默认的非公平信号量，先请求的线程不一定先拿到信号量</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/MoreValidate.html#nonNegative-java.lang.String-int-">nonNegative(String, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/MoreValidate.html" title="com.huazheng.tunny.tools.base中的类">MoreValidate</a></dt>
<dd>
<div class="block">校验为非负数则返回该数字，否则抛出异常.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/MoreValidate.html#nonNegative-java.lang.String-java.lang.Integer-">nonNegative(String, Integer)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/MoreValidate.html" title="com.huazheng.tunny.tools.base中的类">MoreValidate</a></dt>
<dd>
<div class="block">校验为非负数则返回该数字，否则抛出异常.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/MoreValidate.html#nonNegative-java.lang.String-long-">nonNegative(String, long)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/MoreValidate.html" title="com.huazheng.tunny.tools.base中的类">MoreValidate</a></dt>
<dd>
<div class="block">校验为非负数则返回该数字，否则抛出异常.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/MoreValidate.html#nonNegative-java.lang.String-java.lang.Long-">nonNegative(String, Long)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/MoreValidate.html" title="com.huazheng.tunny.tools.base中的类">MoreValidate</a></dt>
<dd>
<div class="block">校验为非负数则返回该数字，否则抛出异常.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/MoreValidate.html#nonNegative-java.lang.String-double-">nonNegative(String, double)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/MoreValidate.html" title="com.huazheng.tunny.tools.base中的类">MoreValidate</a></dt>
<dd>
<div class="block">校验为非负数则返回该数字，否则抛出异常.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/JsonMapper.html#nonNullMapper--">nonNullMapper()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/JsonMapper.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapper</a></dt>
<dd>
<div class="block">创建只输出非Null的属性到Json字符串的Mapper.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/vip/vjtools/test/log/LogbackListAppenderTest.html#normal--">normal()</a></span> - 类 中的方法com.vip.vjtools.test.log.<a href="../com/vip/vjtools/test/log/LogbackListAppenderTest.html" title="com.vip.vjtools.test.log中的类">LogbackListAppenderTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FilePathUtil.html#normalizePath-java.lang.String-">normalizePath(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FilePathUtil.html" title="com.huazheng.tunny.tools.io中的类">FilePathUtil</a></dt>
<dd>
<div class="block">在Windows环境里，兼容Windows上的路径分割符，将 '/' 转回 '\'</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/base/annotation/NotNull.html" title="com.huazheng.tunny.tools.base.annotation中的注释"><span class="typeNameLink">NotNull</span></a> - <a href="../com/huazheng/tunny/tools/base/annotation/package-summary.html">com.huazheng.tunny.tools.base.annotation</a>中的注释类型</dt>
<dd>
<div class="block">标注参数、属性、方法不可为 Null</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/base/annotation/Nullable.html" title="com.huazheng.tunny.tools.base.annotation中的注释"><span class="typeNameLink">Nullable</span></a> - <a href="../com/huazheng/tunny/tools/base/annotation/package-summary.html">com.huazheng.tunny.tools.base.annotation</a>中的注释类型</dt>
<dd>
<div class="block">标注参数、属性、方法可为 Null</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.html#nullAndEmpty--">nullAndEmpty()</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapperTest</a></dt>
<dd>
<div class="block">测试传入空对象,空字符串,Empty的集合,"null"字符串的结果.</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/number/NumberUtil.html" title="com.huazheng.tunny.tools.number中的类"><span class="typeNameLink">NumberUtil</span></a> - <a href="../com/huazheng/tunny/tools/number/package-summary.html">com.huazheng.tunny.tools.number</a>中的类</dt>
<dd>
<div class="block">数字的工具类.
 
 1.原始类型数字与byte[]的双向转换(via Guava)
 
 2.判断字符串是否数字, 是否16进制字符串(via Common Lang)
 
 3.10机制/16进制字符串 与 原始类型数字/数字对象 的双向转换(参考Common Lang自写)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtil.html#NumberUtil--">NumberUtil()</a></span> - 类 的构造器com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtil.html" title="com.huazheng.tunny.tools.number中的类">NumberUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/number/NumberUtilTest.html" title="com.huazheng.tunny.tools.number中的类"><span class="typeNameLink">NumberUtilTest</span></a> - <a href="../com/huazheng/tunny/tools/number/package-summary.html">com.huazheng.tunny.tools.number</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtilTest.html#NumberUtilTest--">NumberUtilTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtilTest.html" title="com.huazheng.tunny.tools.number中的类">NumberUtilTest</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-13.html">上一个字母</a></li>
<li><a href="index-15.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-14.html" target="_top">框架</a></li>
<li><a href="index-14.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
