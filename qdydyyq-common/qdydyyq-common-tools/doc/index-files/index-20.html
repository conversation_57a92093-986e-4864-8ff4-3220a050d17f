<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:36 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>T - 索引</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="T - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-19.html">上一个字母</a></li>
<li><a href="index-21.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-20.html" target="_top">框架</a></li>
<li><a href="index-20.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a name="I:T">
<!--   -->
</a>
<h2 class="title">T</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Teacher.html#Teacher--">Teacher()</a></span> - 类 的构造器com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Teacher.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.Teacher</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Teacher.html#Teacher-java.lang.String-">Teacher(String)</a></span> - 类 的构造器com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.Teacher.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.Teacher</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.TeacherVO.html#TeacherVO--">TeacherVO()</a></span> - 类 的构造器com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.TeacherVO.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.TeacherVO</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.TeacherVO.html#TeacherVO-java.lang.String-">TeacherVO(String)</a></span> - 类 的构造器com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapperTest.TeacherVO.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapperTest.TeacherVO</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/BeanUtilTest.html#test--">test()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/BeanUtilTest.html" title="com.huazheng.tunny.tools.base中的类">BeanUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/MoreValidateTest.html#test--">test()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/MoreValidateTest.html" title="com.huazheng.tunny.tools.base中的类">MoreValidateTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/CollectionUtilTest.html#test--">test()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/CollectionUtilTest.html" title="com.huazheng.tunny.tools.collection中的类">CollectionUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/BasicFutureTest.html#test--">test()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/BasicFutureTest.html" title="com.huazheng.tunny.tools.concurrent中的类">BasicFutureTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/SamplerTest.html#test--">test()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/SamplerTest.html" title="com.huazheng.tunny.tools.concurrent中的类">SamplerTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/ThreadDumpperTest.html#test--">test()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/ThreadDumpperTest.html" title="com.huazheng.tunny.tools.concurrent中的类">ThreadDumpperTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/ThreadLocalContextTest.html#test--">test()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/ThreadLocalContextTest.html" title="com.huazheng.tunny.tools.concurrent中的类">ThreadLocalContextTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPoolTest.html#test--">test()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPoolTest.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">QueuableCachedThreadPoolTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/ResourceUtilTest.html#test--">test()</a></span> - 类 中的方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/ResourceUtilTest.html" title="com.huazheng.tunny.tools.io中的类">ResourceUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassloaderUtilTest.html#test--">test()</a></span> - 类 中的方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassloaderUtilTest.html" title="com.huazheng.tunny.tools.reflect中的类">ClassloaderUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/StringBuilderHolderTest.html#test--">test()</a></span> - 类 中的方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/StringBuilderHolderTest.html" title="com.huazheng.tunny.tools.text中的类">StringBuilderHolderTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/CachingDatFormatterTest.html#test--">test()</a></span> - 类 中的方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/CachingDatFormatterTest.html" title="com.huazheng.tunny.tools.time中的类">CachingDatFormatterTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtilTest.html#test0--">test0()</a></span> - 类 中的方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtilTest.html" title="com.huazheng.tunny.tools.number中的类">RandomUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtilTest.html#testAsBufferedReader--">testAsBufferedReader()</a></span> - 类 中的方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtilTest.html" title="com.huazheng.tunny.tools.io中的类">FileUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtilTest.html#testAsBufferedWriter--">testAsBufferedWriter()</a></span> - 类 中的方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtilTest.html" title="com.huazheng.tunny.tools.io中的类">FileUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtilTest.html#testAsInputStream--">testAsInputStream()</a></span> - 类 中的方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtilTest.html" title="com.huazheng.tunny.tools.io中的类">FileUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtilTest.html#testAsOututStream--">testAsOututStream()</a></span> - 类 中的方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtilTest.html" title="com.huazheng.tunny.tools.io中的类">FileUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.TestBean.html#TestBean--">TestBean()</a></span> - 类 的构造器com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.TestBean.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapperTest.TestBean</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.TestBean.html#TestBean-java.lang.String-">TestBean(String)</a></span> - 类 的构造器com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.TestBean.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapperTest.TestBean</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.TestBean.html#TestBean--">TestBean()</a></span> - 类 的构造器com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.TestBean.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtilTest.TestBean</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.TestBean.html#TestBean--">TestBean()</a></span> - 类 的构造器com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.TestBean.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtilTest.TestBean</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.TestBean2.html#TestBean2--">TestBean2()</a></span> - 类 的构造器com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.TestBean2.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtilTest.TestBean2</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.TestBean2.html#TestBean2--">TestBean2()</a></span> - 类 的构造器com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.TestBean2.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtilTest.TestBean2</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.TestBean3.html#TestBean3--">TestBean3()</a></span> - 类 的构造器com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.TestBean3.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtilTest.TestBean3</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.TestBean3.html#TestBean3--">TestBean3()</a></span> - 类 的构造器com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.TestBean3.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtilTest.TestBean3</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.TestBean3.html#TestBean3-int-">TestBean3(int)</a></span> - 类 的构造器com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.TestBean3.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtilTest.TestBean3</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/EnumUtilTest.html#testBits--">testBits()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/EnumUtilTest.html" title="com.huazheng.tunny.tools.base中的类">EnumUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/ThreadUtilTest.html#testCaller--">testCaller()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/ThreadUtilTest.html" title="com.huazheng.tunny.tools.concurrent中的类">ThreadUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtilTest.html#testCopy--">testCopy()</a></span> - 类 中的方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtilTest.html" title="com.huazheng.tunny.tools.io中的类">FileUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/ClockUtilTest.html#testDummyClock--">testDummyClock()</a></span> - 类 中的方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/ClockUtilTest.html" title="com.huazheng.tunny.tools.time中的类">ClockUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtilTest.html#testGetFileExtension--">testGetFileExtension()</a></span> - 类 中的方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtilTest.html" title="com.huazheng.tunny.tools.io中的类">FileUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtilTest.html#testIsDirExists--">testIsDirExists()</a></span> - 类 中的方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtilTest.html" title="com.huazheng.tunny.tools.io中的类">FileUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtilTest.html#testIsFileExists--">testIsFileExists()</a></span> - 类 中的方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtilTest.html" title="com.huazheng.tunny.tools.io中的类">FileUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.html#testIsSubClassOrInterfaceOf--">testIsSubClassOrInterfaceOf()</a></span> - 类 中的方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtilTest</a></dt>
<dd>
<div class="block">Unit test case of <a href="../com/huazheng/tunny/tools/reflect/ClassUtil.html#isSubClassOrInterfaceOf-java.lang.Class-java.lang.Class-"><code>ClassUtil.isSubClassOrInterfaceOf(Class, Class)</code></a></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtilTest.html#testMakesureDirExists--">testMakesureDirExists()</a></span> - 类 中的方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtilTest.html" title="com.huazheng.tunny.tools.io中的类">FileUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/vip/vjtools/test/rule/TestProgress.html" title="com.vip.vjtools.test.rule中的类"><span class="typeNameLink">TestProgress</span></a> - <a href="../com/vip/vjtools/test/rule/package-summary.html">com.vip.vjtools.test.rule</a>中的类</dt>
<dd>
<div class="block">在Console里打印Case的开始与结束，更容易分清Console里的日志归属于哪个Case.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/vip/vjtools/test/rule/TestProgress.html#TestProgress--">TestProgress()</a></span> - 类 的构造器com.vip.vjtools.test.rule.<a href="../com/vip/vjtools/test/rule/TestProgress.html" title="com.vip.vjtools.test.rule中的类">TestProgress</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtilTest.TestPropertiesListener.html#TestPropertiesListener-java.lang.String-">TestPropertiesListener(String)</a></span> - 类 的构造器com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtilTest.TestPropertiesListener.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtilTest.TestPropertiesListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/RuntimeUtilTest.html#testRuntime--">testRuntime()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/RuntimeUtilTest.html" title="com.huazheng.tunny.tools.base中的类">RuntimeUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/EnumUtilTest.html#testString--">testString()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/EnumUtilTest.html" title="com.huazheng.tunny.tools.base中的类">EnumUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/TimeIntervalLimiterTest.html#testTryAcquire--">testTryAcquire()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/TimeIntervalLimiterTest.html" title="com.huazheng.tunny.tools.concurrent中的类">TimeIntervalLimiterTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/ValueValidatorTest.html#testValidator--">testValidator()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/ValueValidatorTest.html" title="com.huazheng.tunny.tools.base中的类">ValueValidatorTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/text/TextValidator.html" title="com.huazheng.tunny.tools.text中的类"><span class="typeNameLink">TextValidator</span></a> - <a href="../com/huazheng/tunny/tools/text/package-summary.html">com.huazheng.tunny.tools.text</a>中的类</dt>
<dd>
<div class="block">通过正则表达判断是否正确的URL， 邮箱，手机号，固定电话，身份证，邮箱等.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/TextValidator.html#TextValidator--">TextValidator()</a></span> - 类 的构造器com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/TextValidator.html" title="com.huazheng.tunny.tools.text中的类">TextValidator</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/text/TextValidatorTest.html" title="com.huazheng.tunny.tools.text中的类"><span class="typeNameLink">TextValidatorTest</span></a> - <a href="../com/huazheng/tunny/tools/text/package-summary.html">com.huazheng.tunny.tools.text</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/TextValidatorTest.html#TextValidatorTest--">TextValidatorTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/TextValidatorTest.html" title="com.huazheng.tunny.tools.text中的类">TextValidatorTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.AClass.html#tfield">tfield</a></span> - 类 中的变量com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.AClass.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtilTest.AClass</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/ThreadDumpper.html#threadDumpIfNeed--">threadDumpIfNeed()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/ThreadDumpper.html" title="com.huazheng.tunny.tools.concurrent中的类">ThreadDumpper</a></dt>
<dd>
<div class="block">符合条件则打印线程栈.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/ThreadDumpper.html#threadDumpIfNeed-java.lang.String-">threadDumpIfNeed(String)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/ThreadDumpper.html" title="com.huazheng.tunny.tools.concurrent中的类">ThreadDumpper</a></dt>
<dd>
<div class="block">符合条件则打印线程栈.</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/ThreadDumpper.html" title="com.huazheng.tunny.tools.concurrent中的类"><span class="typeNameLink">ThreadDumpper</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/package-summary.html">com.huazheng.tunny.tools.concurrent</a>中的类</dt>
<dd>
<div class="block">由程序触发的ThreadDump，打印到日志中.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/ThreadDumpper.html#ThreadDumpper--">ThreadDumpper()</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/ThreadDumpper.html" title="com.huazheng.tunny.tools.concurrent中的类">ThreadDumpper</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/ThreadDumpper.html#ThreadDumpper-long-int-">ThreadDumpper(long, int)</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/ThreadDumpper.html" title="com.huazheng.tunny.tools.concurrent中的类">ThreadDumpper</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/ThreadDumpperTest.html" title="com.huazheng.tunny.tools.concurrent中的类"><span class="typeNameLink">ThreadDumpperTest</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/package-summary.html">com.huazheng.tunny.tools.concurrent</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/ThreadDumpperTest.html#ThreadDumpperTest--">ThreadDumpperTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/ThreadDumpperTest.html" title="com.huazheng.tunny.tools.concurrent中的类">ThreadDumpperTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/ThreadDumpperTest.LongRunTask.html" title="com.huazheng.tunny.tools.concurrent中的类"><span class="typeNameLink">ThreadDumpperTest.LongRunTask</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/package-summary.html">com.huazheng.tunny.tools.concurrent</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/type/ThreadLocalContext.html" title="com.huazheng.tunny.tools.concurrent.type中的类"><span class="typeNameLink">ThreadLocalContext</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/type/package-summary.html">com.huazheng.tunny.tools.concurrent.type</a>中的类</dt>
<dd>
<div class="block">存储于ThreadLocal的Map, 用于存储上下文.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/type/ThreadLocalContext.html#ThreadLocalContext--">ThreadLocalContext()</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.type.<a href="../com/huazheng/tunny/tools/concurrent/type/ThreadLocalContext.html" title="com.huazheng.tunny.tools.concurrent.type中的类">ThreadLocalContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/ThreadLocalContextTest.html" title="com.huazheng.tunny.tools.concurrent中的类"><span class="typeNameLink">ThreadLocalContextTest</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/package-summary.html">com.huazheng.tunny.tools.concurrent</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/ThreadLocalContextTest.html#ThreadLocalContextTest--">ThreadLocalContextTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/ThreadLocalContextTest.html" title="com.huazheng.tunny.tools.concurrent中的类">ThreadLocalContextTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/RandomUtil.html#threadLocalRandom--">threadLocalRandom()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/RandomUtil.html" title="com.huazheng.tunny.tools.number中的类">RandomUtil</a></dt>
<dd>
<div class="block">返回无锁的ThreadLocalRandom</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><span class="typeNameLink">ThreadPoolBuilder</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/threadpool/package-summary.html">com.huazheng.tunny.tools.concurrent.threadpool</a>中的类</dt>
<dd>
<div class="block">ThreadPool创建的工具类.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.html#ThreadPoolBuilder--">ThreadPoolBuilder()</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><span class="typeNameLink">ThreadPoolBuilder.CachedThreadPoolBuilder</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/threadpool/package-summary.html">com.huazheng.tunny.tools.concurrent.threadpool</a>中的类</dt>
<dd>
<div class="block">创建CachedThreadPool, maxSize建议设置
 
 1.</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.FixedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><span class="typeNameLink">ThreadPoolBuilder.FixedThreadPoolBuilder</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/threadpool/package-summary.html">com.huazheng.tunny.tools.concurrent.threadpool</a>中的类</dt>
<dd>
<div class="block">创建FixedThreadPool.建议必须设置queueSize保证有界。</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.QueuableCachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><span class="typeNameLink">ThreadPoolBuilder.QueuableCachedThreadPoolBuilder</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/threadpool/package-summary.html">com.huazheng.tunny.tools.concurrent.threadpool</a>中的类</dt>
<dd>
<div class="block">从Tomcat移植过来的可扩展可用Queue缓存任务的ThreadPool</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.ScheduledThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><span class="typeNameLink">ThreadPoolBuilder.ScheduledThreadPoolBuilder</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/threadpool/package-summary.html">com.huazheng.tunny.tools.concurrent.threadpool</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilderTest.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><span class="typeNameLink">ThreadPoolBuilderTest</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/threadpool/package-summary.html">com.huazheng.tunny.tools.concurrent.threadpool</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilderTest.html#ThreadPoolBuilderTest--">ThreadPoolBuilderTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilderTest.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilderTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtil.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><span class="typeNameLink">ThreadPoolUtil</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/threadpool/package-summary.html">com.huazheng.tunny.tools.concurrent.threadpool</a>中的类</dt>
<dd>
<div class="block">线程池工具集
 
 1.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtil.html#ThreadPoolUtil--">ThreadPoolUtil()</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtil.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtilTest.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><span class="typeNameLink">ThreadPoolUtilTest</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/threadpool/package-summary.html">com.huazheng.tunny.tools.concurrent.threadpool</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtilTest.html#ThreadPoolUtilTest--">ThreadPoolUtilTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtilTest.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/ThreadUtil.html" title="com.huazheng.tunny.tools.concurrent中的类"><span class="typeNameLink">ThreadUtil</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/package-summary.html">com.huazheng.tunny.tools.concurrent</a>中的类</dt>
<dd>
<div class="block">线程相关工具类.
 
 1.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/ThreadUtil.html#ThreadUtil--">ThreadUtil()</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/ThreadUtil.html" title="com.huazheng.tunny.tools.concurrent中的类">ThreadUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/ThreadUtilTest.html" title="com.huazheng.tunny.tools.concurrent中的类"><span class="typeNameLink">ThreadUtilTest</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/package-summary.html">com.huazheng.tunny.tools.concurrent</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/ThreadUtilTest.html#ThreadUtilTest--">ThreadUtilTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/ThreadUtilTest.html" title="com.huazheng.tunny.tools.concurrent中的类">ThreadUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/ThreadUtilTest.MyClass.html" title="com.huazheng.tunny.tools.concurrent中的类"><span class="typeNameLink">ThreadUtilTest.MyClass</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/package-summary.html">com.huazheng.tunny.tools.concurrent</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.html#threeTypeMappers--">threeTypeMappers()</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapperTest</a></dt>
<dd>
<div class="block">测试三种不同的Mapper.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/Concurrents.html#timeIntervalLimiter-long-java.util.concurrent.TimeUnit-">timeIntervalLimiter(long, TimeUnit)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/Concurrents.html" title="com.huazheng.tunny.tools.concurrent中的类">Concurrents</a></dt>
<dd>
<div class="block">返回时间间隔限制器.</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/TimeIntervalLimiter.html" title="com.huazheng.tunny.tools.concurrent中的类"><span class="typeNameLink">TimeIntervalLimiter</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/package-summary.html">com.huazheng.tunny.tools.concurrent</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/TimeIntervalLimiter.html#TimeIntervalLimiter-long-java.util.concurrent.TimeUnit-">TimeIntervalLimiter(long, TimeUnit)</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/TimeIntervalLimiter.html" title="com.huazheng.tunny.tools.concurrent中的类">TimeIntervalLimiter</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/TimeIntervalLimiterTest.html" title="com.huazheng.tunny.tools.concurrent中的类"><span class="typeNameLink">TimeIntervalLimiterTest</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/package-summary.html">com.huazheng.tunny.tools.concurrent</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/TimeIntervalLimiterTest.html#TimeIntervalLimiterTest--">TimeIntervalLimiterTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/TimeIntervalLimiterTest.html" title="com.huazheng.tunny.tools.concurrent中的类">TimeIntervalLimiterTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/Platforms.html#TMP_DIR">TMP_DIR</a></span> - 类 中的静态变量com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/Platforms.html" title="com.huazheng.tunny.tools.base中的类">Platforms</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtil.html#to2DigitString-double-">to2DigitString(double)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtil.html" title="com.huazheng.tunny.tools.number中的类">NumberUtil</a></dt>
<dd>
<div class="block">输出格式化为小数后两位的double字符串</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ArrayUtil.html#toArray-java.util.Collection-java.lang.Class-">toArray(Collection&lt;T&gt;, Class&lt;T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ArrayUtil.html" title="com.huazheng.tunny.tools.collection中的类">ArrayUtil</a></dt>
<dd>
<div class="block">从collection转为Array, 以 list.toArray(new String[0]); 最快 不需要创建list.size()的数组.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html#toArray--">toArray()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.<a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="com.huazheng.tunny.tools.collection.type中的类">ConcurrentHashSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html#toArray-T:A-">toArray(T[])</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.<a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="com.huazheng.tunny.tools.collection.type中的类">ConcurrentHashSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/BooleanUtil.html#toBoolean-java.lang.String-">toBoolean(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/BooleanUtil.html" title="com.huazheng.tunny.tools.base中的类">BooleanUtil</a></dt>
<dd>
<div class="block">使用标准JDK，只分析是否忽略大小写的"true", str为空时返回false</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/BooleanUtil.html#toBooleanObject-java.lang.String-">toBooleanObject(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/BooleanUtil.html" title="com.huazheng.tunny.tools.base中的类">BooleanUtil</a></dt>
<dd>
<div class="block">使用标准JDK，只分析是否忽略大小写的"true", str为空时返回null</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/BooleanUtil.html#toBooleanObject-java.lang.String-java.lang.Boolean-">toBooleanObject(String, Boolean)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/BooleanUtil.html" title="com.huazheng.tunny.tools.base中的类">BooleanUtil</a></dt>
<dd>
<div class="block">使用标准JDK，只分析是否忽略大小写的"true", str为空时返回defaultValue</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/IOUtil.html#toBufferedReader-java.io.Reader-">toBufferedReader(Reader)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/IOUtil.html" title="com.huazheng.tunny.tools.io中的类">IOUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#toByteArray-java.io.File-">toByteArray(File)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">读取文件到byte[].</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtil.html#toBytes-int-">toBytes(int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtil.html" title="com.huazheng.tunny.tools.number中的类">NumberUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtil.html#toBytes-long-">toBytes(long)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtil.html" title="com.huazheng.tunny.tools.number中的类">NumberUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtil.html#toBytes-double-">toBytes(double)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtil.html" title="com.huazheng.tunny.tools.number中的类">NumberUtil</a></dt>
<dd>
<div class="block">copy from ElasticSearch Numbers</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtilTest.html#toBytes--">toBytes()</a></span> - 类 中的方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtilTest.html" title="com.huazheng.tunny.tools.number中的类">NumberUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/SizeUnit.html#toBytes-long-">toBytes(long)</a></span> - 枚举 中的方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/SizeUnit.html" title="com.huazheng.tunny.tools.number中的枚举">SizeUnit</a></dt>
<dd>
<div class="block">Returns the number of bytes corresponding to the provided input for a particular unit of memory.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/UnitConverter.html#toBytes-java.lang.String-">toBytes(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/UnitConverter.html" title="com.huazheng.tunny.tools.number中的类">UnitConverter</a></dt>
<dd>
<div class="block">将带单位的大小字符串转化为字节数.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/CsvUtil.html#toCsvString-java.lang.Object...-">toCsvString(Object...)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/CsvUtil.html" title="com.huazheng.tunny.tools.text中的类">CsvUtil</a></dt>
<dd>
<div class="block">Parse fields as csv string,</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/CsvUtilTest.html#toCsvString--">toCsvString()</a></span> - 类 中的方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/CsvUtilTest.html" title="com.huazheng.tunny.tools.text中的类">CsvUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtil.html#toDouble-byte:A-">toDouble(byte[])</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtil.html" title="com.huazheng.tunny.tools.number中的类">NumberUtil</a></dt>
<dd>
<div class="block">copy from ElasticSearch Numbers</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtil.html#toDouble-java.lang.String-">toDouble(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtil.html" title="com.huazheng.tunny.tools.number中的类">NumberUtil</a></dt>
<dd>
<div class="block">将10进制的String安全的转化为double.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtil.html#toDouble-java.lang.String-double-">toDouble(String, double)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtil.html" title="com.huazheng.tunny.tools.number中的类">NumberUtil</a></dt>
<dd>
<div class="block">将10进制的String安全的转化为double.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtil.html#toDoubleObject-java.lang.String-">toDoubleObject(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtil.html" title="com.huazheng.tunny.tools.number中的类">NumberUtil</a></dt>
<dd>
<div class="block">将10进制的String安全的转化为Double.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtil.html#toDoubleObject-java.lang.String-java.lang.Double-">toDoubleObject(String, Double)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtil.html" title="com.huazheng.tunny.tools.number中的类">NumberUtil</a></dt>
<dd>
<div class="block">将10进制的String安全的转化为Long.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/UnitConverter.html#toDurationMillis-java.lang.String-">toDurationMillis(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/UnitConverter.html" title="com.huazheng.tunny.tools.number中的类">UnitConverter</a></dt>
<dd>
<div class="block">将带单位的时间字符串转化为毫秒数.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/SizeUnit.html#toGigaBytes-long-">toGigaBytes(long)</a></span> - 枚举 中的方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/SizeUnit.html" title="com.huazheng.tunny.tools.number中的枚举">SizeUnit</a></dt>
<dd>
<div class="block">Returns the number of gigabytes corresponding to the provided input for a particular unit of memory.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/net/IPUtil.html#toInt-java.net.InetAddress-">toInt(InetAddress)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.net.<a href="../com/huazheng/tunny/tools/net/IPUtil.html" title="com.huazheng.tunny.tools.net中的类">IPUtil</a></dt>
<dd>
<div class="block">从InetAddress转化到int, 传输和存储时, 用int代表InetAddress是最小的开销.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtil.html#toInt-byte:A-">toInt(byte[])</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtil.html" title="com.huazheng.tunny.tools.number中的类">NumberUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtil.html#toInt-java.lang.String-">toInt(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtil.html" title="com.huazheng.tunny.tools.number中的类">NumberUtil</a></dt>
<dd>
<div class="block">将10进制的String转化为int.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtil.html#toInt-java.lang.String-int-">toInt(String, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtil.html" title="com.huazheng.tunny.tools.number中的类">NumberUtil</a></dt>
<dd>
<div class="block">将10进制的String安全的转化为int.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtil.html#toInt32-long-">toInt32(long)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtil.html" title="com.huazheng.tunny.tools.number中的类">NumberUtil</a></dt>
<dd>
<div class="block">安全的将小于Integer.MAX的long转为int，否则抛出IllegalArgumentException异常</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtil.html#toIntObject-java.lang.String-">toIntObject(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtil.html" title="com.huazheng.tunny.tools.number中的类">NumberUtil</a></dt>
<dd>
<div class="block">将10进制的String安全的转化为Integer.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtil.html#toIntObject-java.lang.String-java.lang.Integer-">toIntObject(String, Integer)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtil.html" title="com.huazheng.tunny.tools.number中的类">NumberUtil</a></dt>
<dd>
<div class="block">将10进制的String安全的转化为Integer.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/net/IPUtil.html#toIpString-java.net.InetAddress-">toIpString(InetAddress)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.net.<a href="../com/huazheng/tunny/tools/net/IPUtil.html" title="com.huazheng.tunny.tools.net中的类">IPUtil</a></dt>
<dd>
<div class="block">InetAddress转换为String.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/JsonMapper.html#toJson-java.lang.Object-">toJson(Object)</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/JsonMapper.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapper</a></dt>
<dd>
<div class="block">Object可以是POJO，也可以是Collection或数组。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.html#toJson--">toJson()</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapperTest</a></dt>
<dd>
<div class="block">序列化对象/集合到Json字符串.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/JsonMapper.html#toJsonP-java.lang.String-java.lang.Object-">toJsonP(String, Object)</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/JsonMapper.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapper</a></dt>
<dd>
<div class="block">輸出JSONP格式數據.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/SizeUnit.html#toKiloBytes-long-">toKiloBytes(long)</a></span> - 枚举 中的方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/SizeUnit.html" title="com.huazheng.tunny.tools.number中的枚举">SizeUnit</a></dt>
<dd>
<div class="block">Returns the number of kilobytes corresponding to the provided input for a particular unit of memory.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#toLines-java.io.File-">toLines(File)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">读取文件的每行内容到List<String>.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/IOUtil.html#toLines-java.io.InputStream-">toLines(InputStream)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/IOUtil.html" title="com.huazheng.tunny.tools.io中的类">IOUtil</a></dt>
<dd>
<div class="block">简单读取Reader的每行内容到List<String></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/IOUtil.html#toLines-java.io.Reader-">toLines(Reader)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/IOUtil.html" title="com.huazheng.tunny.tools.io中的类">IOUtil</a></dt>
<dd>
<div class="block">简单读取Reader的每行内容到List<String></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/ResourceUtil.html#toLines-java.lang.String-">toLines(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/ResourceUtil.html" title="com.huazheng.tunny.tools.io中的类">ResourceUtil</a></dt>
<dd>
<div class="block">读取文件的每一行，读取规则见本类注释.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/ResourceUtil.html#toLines-java.lang.Class-java.lang.String-">toLines(Class&lt;?&gt;, String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/ResourceUtil.html" title="com.huazheng.tunny.tools.io中的类">ResourceUtil</a></dt>
<dd>
<div class="block">读取文件的每一行，读取规则见本类注释.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtil.html#toLong-byte:A-">toLong(byte[])</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtil.html" title="com.huazheng.tunny.tools.number中的类">NumberUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtil.html#toLong-java.lang.String-">toLong(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtil.html" title="com.huazheng.tunny.tools.number中的类">NumberUtil</a></dt>
<dd>
<div class="block">将10进制的String安全的转化为long.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtil.html#toLong-java.lang.String-long-">toLong(String, long)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtil.html" title="com.huazheng.tunny.tools.number中的类">NumberUtil</a></dt>
<dd>
<div class="block">将10进制的String安全的转化为long.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtil.html#toLongObject-java.lang.String-">toLongObject(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtil.html" title="com.huazheng.tunny.tools.number中的类">NumberUtil</a></dt>
<dd>
<div class="block">将10进制的String安全的转化为Long.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtil.html#toLongObject-java.lang.String-java.lang.Long-">toLongObject(String, Long)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtil.html" title="com.huazheng.tunny.tools.number中的类">NumberUtil</a></dt>
<dd>
<div class="block">将10进制的String安全的转化为Long.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/SizeUnit.html#toMegaBytes-long-">toMegaBytes(long)</a></span> - 枚举 中的方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/SizeUnit.html" title="com.huazheng.tunny.tools.number中的枚举">SizeUnit</a></dt>
<dd>
<div class="block">Returns the number of megabytes corresponding to the provided input for a particular unit of memory.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtilTest.html#toNumber--">toNumber()</a></span> - 类 中的方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtilTest.html" title="com.huazheng.tunny.tools.number中的类">NumberUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/CollectionUtil.html#topN-java.lang.Iterable-int-">topN(Iterable&lt;T&gt;, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/CollectionUtil.html" title="com.huazheng.tunny.tools.collection中的类">CollectionUtil</a></dt>
<dd>
<div class="block">返回Iterable中最大的N个对象, back by guava.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/CollectionUtil.html#topN-java.lang.Iterable-int-java.util.Comparator-">topN(Iterable&lt;T&gt;, int, Comparator&lt;? super T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/CollectionUtil.html" title="com.huazheng.tunny.tools.collection中的类">CollectionUtil</a></dt>
<dd>
<div class="block">返回Iterable中最大的N个对象, back by guava.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/CollectionUtilTest.html#topNAndBottomN--">topNAndBottomN()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/CollectionUtilTest.html" title="com.huazheng.tunny.tools.collection中的类">CollectionUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtil.html#topNByValue-java.util.Map-boolean-int-">topNByValue(Map&lt;K, V&gt;, boolean, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtil.html" title="com.huazheng.tunny.tools.collection中的类">MapUtil</a></dt>
<dd>
<div class="block">对一个Map按Value进行排序，返回排序LinkedHashMap，最多只返回n条，多用于Value是Counter的情况.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtil.html#topNByValue-java.util.Map-java.util.Comparator-int-">topNByValue(Map&lt;K, V&gt;, Comparator&lt;? super V&gt;, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtil.html" title="com.huazheng.tunny.tools.collection中的类">MapUtil</a></dt>
<dd>
<div class="block">对一个Map按Value进行排序，返回排序LinkedHashMap, 最多只返回n条，多用于Value是Counter的情况.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/ObjectUtil.html#toPrettyString-java.lang.Object-">toPrettyString(Object)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/ObjectUtil.html" title="com.huazheng.tunny.tools.base中的类">ObjectUtil</a></dt>
<dd>
<div class="block">对象的toString(), 处理了对象为数组的情况，JDK的默认toString()只打数组的地址如 "[Ljava.lang.Integer;@490d6c15.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/ObjectUtilTest.html#toPrettyString--">toPrettyString()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/ObjectUtilTest.html" title="com.huazheng.tunny.tools.base中的类">ObjectUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/UnitConverter.html#toSizeUnit-java.lang.Long-int-">toSizeUnit(Long, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/UnitConverter.html" title="com.huazheng.tunny.tools.number中的类">UnitConverter</a></dt>
<dd>
<div class="block">从bytes转换为带单位的字符串, 单位最大只支持到G级别，四舍五入</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/EnumUtil.html#toString-java.lang.Enum-">toString(Enum)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/EnumUtil.html" title="com.huazheng.tunny.tools.base中的类">EnumUtil</a></dt>
<dd>
<div class="block">Enum转换为String</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/type/Pair.html#toString--">toString()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.type.<a href="../com/huazheng/tunny/tools/base/type/Pair.html" title="com.huazheng.tunny.tools.base.type中的类">Pair</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/type/Triple.html#toString--">toString()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.type.<a href="../com/huazheng/tunny/tools/base/type/Triple.html" title="com.huazheng.tunny.tools.base.type中的类">Triple</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html#toString--">toString()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.<a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="com.huazheng.tunny.tools.collection.type中的类">ConcurrentHashSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html#toString--">toString()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">IntObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#toString--">toString()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">LongObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#toString--">toString()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.jsr166e.<a href="../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html" title="com.huazheng.tunny.tools.concurrent.jsr166e中的类">LongAdder</a></dt>
<dd>
<div class="block">Returns the String representation of the <a href="../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#sum--"><code>LongAdder.sum()</code></a>.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#toString-java.io.File-">toString(File)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">读取文件到String.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/IOUtil.html#toString-java.io.InputStream-">toString(InputStream)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/IOUtil.html" title="com.huazheng.tunny.tools.io中的类">IOUtil</a></dt>
<dd>
<div class="block">简单读取InputStream到String.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/IOUtil.html#toString-java.io.Reader-">toString(Reader)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/IOUtil.html" title="com.huazheng.tunny.tools.io中的类">IOUtil</a></dt>
<dd>
<div class="block">简单读取Reader到String</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/ResourceUtil.html#toString-java.lang.String-">toString(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/ResourceUtil.html" title="com.huazheng.tunny.tools.io中的类">ResourceUtil</a></dt>
<dd>
<div class="block">读取文件的每一行，读取规则见本类注释.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/ResourceUtil.html#toString-java.lang.Class-java.lang.String-">toString(Class&lt;?&gt;, String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/ResourceUtil.html" title="com.huazheng.tunny.tools.io中的类">ResourceUtil</a></dt>
<dd>
<div class="block">读取文件的每一行，读取规则见本类注释.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html#toString--">toString()</a></span> - 类 中的方法com.huazheng.tunny.tools.io.type.<a href="../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html" title="com.huazheng.tunny.tools.io.type中的类">StringBuilderWriter</a></dt>
<dd>
<div class="block">Returns <code>StringBuilder.toString()</code>.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.TestBean.html#toString--">toString()</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.TestBean.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapperTest.TestBean</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtil.html#toString-int-">toString(int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtil.html" title="com.huazheng.tunny.tools.number中的类">NumberUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtil.html#toString-java.lang.Integer-">toString(Integer)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtil.html" title="com.huazheng.tunny.tools.number中的类">NumberUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtil.html#toString-long-">toString(long)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtil.html" title="com.huazheng.tunny.tools.number中的类">NumberUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtil.html#toString-java.lang.Long-">toString(Long)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtil.html" title="com.huazheng.tunny.tools.number中的类">NumberUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtil.html#toString-double-">toString(double)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtil.html" title="com.huazheng.tunny.tools.number中的类">NumberUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtil.html#toString-java.lang.Double-">toString(Double)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtil.html" title="com.huazheng.tunny.tools.number中的类">NumberUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/NumberUtilTest.html#toStringTest--">toStringTest()</a></span> - 类 中的方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/NumberUtilTest.html" title="com.huazheng.tunny.tools.number中的类">NumberUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/ExceptionUtil.html#toStringWithRootCause-java.lang.Throwable-">toStringWithRootCause(Throwable)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/ExceptionUtil.html" title="com.huazheng.tunny.tools.base中的类">ExceptionUtil</a></dt>
<dd>
<div class="block">拼装 短异常类名: 异常信息 <-- RootCause的短异常类名: 异常信息</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/ExceptionUtil.html#toStringWithShortName-java.lang.Throwable-">toStringWithShortName(Throwable)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/ExceptionUtil.html" title="com.huazheng.tunny.tools.base中的类">ExceptionUtil</a></dt>
<dd>
<div class="block">拼装 短异常类名: 异常信息.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/UnitConverter.html#toTimeUnit-long-int-">toTimeUnit(long, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/UnitConverter.html" title="com.huazheng.tunny.tools.number中的类">UnitConverter</a></dt>
<dd>
<div class="block">转换毫秒为带时间单位的字符串，单位最大到day级别，四舍五入</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/UnitConverter.html#toTimeWithMinorUnit-long-">toTimeWithMinorUnit(long)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/UnitConverter.html" title="com.huazheng.tunny.tools.number中的类">UnitConverter</a></dt>
<dd>
<div class="block">转换毫秒为带时间单位的字符串，会同时带下一级的单位，四舍五入</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#touch-java.lang.String-">touch(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">创建文件或更新时间戳.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#touch-java.io.File-">touch(File)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">创建文件或更新时间戳.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/URLResourceUtil.html#toURI-java.lang.String-">toURI(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/URLResourceUtil.html" title="com.huazheng.tunny.tools.io中的类">URLResourceUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/XmlMapper.html#toXml-java.lang.Object-">toXml(Object)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/XmlMapper.html" title="com.huazheng.tunny.tools.mapper中的类">XmlMapper</a></dt>
<dd>
<div class="block">Java Object->Xml without encoding.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/XmlMapper.html#toXml-java.lang.Object-java.lang.String-">toXml(Object, String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/XmlMapper.html" title="com.huazheng.tunny.tools.mapper中的类">XmlMapper</a></dt>
<dd>
<div class="block">Java Object->Xml with encoding.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/XmlMapper.html#toXml-java.lang.Object-java.lang.Class-java.lang.String-">toXml(Object, Class, String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/XmlMapper.html" title="com.huazheng.tunny.tools.mapper中的类">XmlMapper</a></dt>
<dd>
<div class="block">Java Object->Xml with encoding.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/XmlMapper.html#toXml-java.util.Collection-java.lang.String-java.lang.Class-">toXml(Collection&lt;?&gt;, String, Class)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/XmlMapper.html" title="com.huazheng.tunny.tools.mapper中的类">XmlMapper</a></dt>
<dd>
<div class="block">Java Collection->Xml without encoding, 特别支持Root Element是Collection的情形.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/XmlMapper.html#toXml-java.util.Collection-java.lang.String-java.lang.Class-java.lang.String-">toXml(Collection&lt;?&gt;, String, Class, String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/XmlMapper.html" title="com.huazheng.tunny.tools.mapper中的类">XmlMapper</a></dt>
<dd>
<div class="block">Java Collection->Xml with encoding, 特别支持Root Element是Collection的情形.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/XmlMapperTest.html#toXmlWithListAsRoot--">toXmlWithListAsRoot()</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/XmlMapperTest.html" title="com.huazheng.tunny.tools.mapper中的类">XmlMapperTest</a></dt>
<dd>
<div class="block">测试以List对象作为根节点时的XML输出</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/base/type/Triple.html" title="com.huazheng.tunny.tools.base.type中的类"><span class="typeNameLink">Triple</span></a>&lt;<a href="../com/huazheng/tunny/tools/base/type/Triple.html" title="Triple中的类型参数">L</a>,<a href="../com/huazheng/tunny/tools/base/type/Triple.html" title="Triple中的类型参数">M</a>,<a href="../com/huazheng/tunny/tools/base/type/Triple.html" title="Triple中的类型参数">R</a>&gt; - <a href="../com/huazheng/tunny/tools/base/type/package-summary.html">com.huazheng.tunny.tools.base.type</a>中的类</dt>
<dd>
<div class="block">引入一个简简单单的Triple, 用于返回值返回三个元素.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/type/Triple.html#Triple-L-M-R-">Triple(L, M, R)</a></span> - 类 的构造器com.huazheng.tunny.tools.base.type.<a href="../com/huazheng/tunny/tools/base/type/Triple.html" title="com.huazheng.tunny.tools.base.type中的类">Triple</a></dt>
<dd>
<div class="block">Creates a new Triple.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/PairTest.html#tripleTest--">tripleTest()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/PairTest.html" title="com.huazheng.tunny.tools.base中的类">PairTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtilTest.html#truncateAndCelling--">truncateAndCelling()</a></span> - 类 中的方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtilTest.html" title="com.huazheng.tunny.tools.time中的类">DateUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/TimeIntervalLimiter.html#tryAcquire--">tryAcquire()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/TimeIntervalLimiter.html" title="com.huazheng.tunny.tools.concurrent中的类">TimeIntervalLimiter</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-19.html">上一个字母</a></li>
<li><a href="index-21.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-20.html" target="_top">框架</a></li>
<li><a href="index-20.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
