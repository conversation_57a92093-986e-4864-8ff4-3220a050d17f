<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:36 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>F - 索引</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="F - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-5.html">上一个字母</a></li>
<li><a href="index-7.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-6.html" target="_top">框架</a></li>
<li><a href="index-6.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a name="I:F">
<!--   -->
</a>
<h2 class="title">F</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html#failed-java.lang.Exception-">failed(Exception)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.type.<a href="../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html" title="com.huazheng.tunny.tools.concurrent.type中的类">BasicFuture</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/Concurrents.html#fairSemaphore-int-">fairSemaphore(int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/Concurrents.html" title="com.huazheng.tunny.tools.concurrent中的类">Concurrents</a></dt>
<dd>
<div class="block">返回公平的信号量，先请求的线程先拿到信号量</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/CsvUtil.html#FIELD_QUOTE">FIELD_QUOTE</a></span> - 类 中的静态变量com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/CsvUtil.html" title="com.huazheng.tunny.tools.text中的类">CsvUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/CsvUtil.html#FIELD_SEPARATOR">FIELD_SEPARATOR</a></span> - 类 中的静态变量com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/CsvUtil.html" title="com.huazheng.tunny.tools.text中的类">CsvUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/URLResourceTest.html#file--">file()</a></span> - 类 中的方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/URLResourceTest.html" title="com.huazheng.tunny.tools.io中的类">URLResourceTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/Platforms.html#FILE_PATH_SEPARATOR">FILE_PATH_SEPARATOR</a></span> - 类 中的静态变量com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/Platforms.html" title="com.huazheng.tunny.tools.base中的类">Platforms</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/Platforms.html#FILE_PATH_SEPARATOR_CHAR">FILE_PATH_SEPARATOR_CHAR</a></span> - 类 中的静态变量com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/Platforms.html" title="com.huazheng.tunny.tools.base中的类">Platforms</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtilTest.html#fileExist--">fileExist()</a></span> - 类 中的方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtilTest.html" title="com.huazheng.tunny.tools.io中的类">FileUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/io/FilePathUtil.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">FilePathUtil</span></a> - <a href="../com/huazheng/tunny/tools/io/package-summary.html">com.huazheng.tunny.tools.io</a>中的类</dt>
<dd>
<div class="block">关于文件路径的工具集.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FilePathUtil.html#FilePathUtil--">FilePathUtil()</a></span> - 类 的构造器com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FilePathUtil.html" title="com.huazheng.tunny.tools.io中的类">FilePathUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/io/FilePathUtilTest.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">FilePathUtilTest</span></a> - <a href="../com/huazheng/tunny/tools/io/package-summary.html">com.huazheng.tunny.tools.io</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FilePathUtilTest.html#FilePathUtilTest--">FilePathUtilTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FilePathUtilTest.html" title="com.huazheng.tunny.tools.io中的类">FilePathUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileTreeWalker.html#fileTreeTraverser--">fileTreeTraverser()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileTreeWalker.html" title="com.huazheng.tunny.tools.io中的类">FileTreeWalker</a></dt>
<dd>
<div class="block">直接使用Guava的TreeTraverser，获得更大的灵活度, 比如加入各类filter，前序/后序的选择，一边遍历一边操作
 
 
 FileUtil.fileTreeTraverser().preOrderTraversal(root).iterator();
 </div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/io/FileTreeWalker.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">FileTreeWalker</span></a> - <a href="../com/huazheng/tunny/tools/io/package-summary.html">com.huazheng.tunny.tools.io</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileTreeWalker.html#FileTreeWalker--">FileTreeWalker()</a></span> - 类 的构造器com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileTreeWalker.html" title="com.huazheng.tunny.tools.io中的类">FileTreeWalker</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/io/FileTreeWalker.AntPathFilter.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">FileTreeWalker.AntPathFilter</span></a> - <a href="../com/huazheng/tunny/tools/io/package-summary.html">com.huazheng.tunny.tools.io</a>中的类</dt>
<dd>
<div class="block">以ant风格的path为filter，配合fileTreeTraverser使用.</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/io/FileTreeWalker.FileExtensionFilter.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">FileTreeWalker.FileExtensionFilter</span></a> - <a href="../com/huazheng/tunny/tools/io/package-summary.html">com.huazheng.tunny.tools.io</a>中的类</dt>
<dd>
<div class="block">以文件名后缀做filter，配合fileTreeTraverser使用</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/io/FileTreeWalker.RegexFileNameFilter.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">FileTreeWalker.RegexFileNameFilter</span></a> - <a href="../com/huazheng/tunny/tools/io/package-summary.html">com.huazheng.tunny.tools.io</a>中的类</dt>
<dd>
<div class="block">以文件名正则表达式为filter，配合fileTreeTraverser使用</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/io/FileTreeWalker.WildcardFileNameFilter.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">FileTreeWalker.WildcardFileNameFilter</span></a> - <a href="../com/huazheng/tunny/tools/io/package-summary.html">com.huazheng.tunny.tools.io</a>中的类</dt>
<dd>
<div class="block">以文件名通配符为filter，配合fileTreeTraverser使用.</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/io/FileTreeWalkerTest.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">FileTreeWalkerTest</span></a> - <a href="../com/huazheng/tunny/tools/io/package-summary.html">com.huazheng.tunny.tools.io</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileTreeWalkerTest.html#FileTreeWalkerTest--">FileTreeWalkerTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileTreeWalkerTest.html" title="com.huazheng.tunny.tools.io中的类">FileTreeWalkerTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">FileUtil</span></a> - <a href="../com/huazheng/tunny/tools/io/package-summary.html">com.huazheng.tunny.tools.io</a>中的类</dt>
<dd>
<div class="block">关于文件的工具集.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#FileUtil--">FileUtil()</a></span> - 类 的构造器com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/io/FileUtilTest.html" title="com.huazheng.tunny.tools.io中的类"><span class="typeNameLink">FileUtilTest</span></a> - <a href="../com/huazheng/tunny/tools/io/package-summary.html">com.huazheng.tunny.tools.io</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtilTest.html#FileUtilTest--">FileUtilTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtilTest.html" title="com.huazheng.tunny.tools.io中的类">FileUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/net/NetUtil.html#findAvailablePortFrom-int-">findAvailablePortFrom(int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.net.<a href="../com/huazheng/tunny/tools/net/NetUtil.html" title="com.huazheng.tunny.tools.net中的类">NetUtil</a></dt>
<dd>
<div class="block">从某个端口开始，递增直到65535，找一个空闲端口.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/ExceptionUtil.html#findCause-java.lang.Throwable-java.lang.Class-">findCause(Throwable, Class&lt;T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/ExceptionUtil.html" title="com.huazheng.tunny.tools.base中的类">ExceptionUtil</a></dt>
<dd>
<div class="block">获取某种类型的cause，如果没有则返回空
 
 copy from Jodd ExceptionUtil</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/SortedArrayList.html#findInsertionPoint-E-">findInsertionPoint(E)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.<a href="../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="com.huazheng.tunny.tools.collection.type中的类">SortedArrayList</a></dt>
<dd>
<div class="block">Finds the index at which object should be inserted.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/SortedArrayList.html#findInsertionPoint-E-int-int-">findInsertionPoint(E, int, int)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.<a href="../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="com.huazheng.tunny.tools.collection.type中的类">SortedArrayList</a></dt>
<dd>
<div class="block">Conducts a binary search to find the index where Object
 should be inserted.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/net/NetUtil.html#findRandomAvailablePort--">findRandomAvailablePort()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.net.<a href="../com/huazheng/tunny/tools/net/NetUtil.html" title="com.huazheng.tunny.tools.net中的类">NetUtil</a></dt>
<dd>
<div class="block">从1024到65535， 随机找一个空闲端口 from Spring SocketUtils</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/net/NetUtil.html#findRandomAvailablePort-int-int-">findRandomAvailablePort(int, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.net.<a href="../com/huazheng/tunny/tools/net/NetUtil.html" title="com.huazheng.tunny.tools.net中的类">NetUtil</a></dt>
<dd>
<div class="block">在范围里随机找一个空闲端口,from Spring SocketUtils.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/vip/vjtools/test/rule/TestProgress.html#finished-org.junit.runner.Description-">finished(Description)</a></span> - 类 中的方法com.vip.vjtools.test.rule.<a href="../com/vip/vjtools/test/rule/TestProgress.html" title="com.vip.vjtools.test.rule中的类">TestProgress</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.html#fixedPool--">fixedPool()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.FixedThreadPoolBuilder.html#FixedThreadPoolBuilder--">FixedThreadPoolBuilder()</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.FixedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.FixedThreadPoolBuilder</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilderTest.html#fixPool--">fixPool()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilderTest.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilderTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#floatValue--">floatValue()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.jsr166e.<a href="../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html" title="com.huazheng.tunny.tools.concurrent.jsr166e中的类">LongAdder</a></dt>
<dd>
<div class="block">Returns the <a href="../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#sum--"><code>LongAdder.sum()</code></a> as a <code>float</code>
 after a widening primitive conversion.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html#flush--">flush()</a></span> - 类 中的方法com.huazheng.tunny.tools.io.type.<a href="../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html" title="com.huazheng.tunny.tools.io.type中的类">StringBuilderWriter</a></dt>
<dd>
<div class="block">Flushing this writer has no effect.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.ControllableQueue.html#force-java.lang.Runnable-">force(Runnable)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.ControllableQueue.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">QueuableCachedThreadPool.ControllableQueue</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.ControllableQueue.html#force-java.lang.Runnable-long-java.util.concurrent.TimeUnit-">force(Runnable, long, TimeUnit)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.ControllableQueue.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">QueuableCachedThreadPool.ControllableQueue</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/CachingDateFormatter.html#format-long-">format(long)</a></span> - 类 中的方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/CachingDateFormatter.html" title="com.huazheng.tunny.tools.time中的类">CachingDateFormatter</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html#formatDate-java.lang.String-java.util.Date-">formatDate(String, Date)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html" title="com.huazheng.tunny.tools.time中的类">DateFormatUtil</a></dt>
<dd>
<div class="block">格式化日期, 仅用于pattern不固定的情况.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html#formatDate-java.lang.String-long-">formatDate(String, long)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html" title="com.huazheng.tunny.tools.time中的类">DateFormatUtil</a></dt>
<dd>
<div class="block">格式化日期, 仅用于不固定pattern不固定的情况.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html#formatDuration-java.util.Date-java.util.Date-">formatDuration(Date, Date)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html" title="com.huazheng.tunny.tools.time中的类">DateFormatUtil</a></dt>
<dd>
<div class="block">按HH:mm:ss.SSS格式，格式化时间间隔.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html#formatDuration-long-">formatDuration(long)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html" title="com.huazheng.tunny.tools.time中的类">DateFormatUtil</a></dt>
<dd>
<div class="block">按HH:mm:ss.SSS格式，格式化时间间隔
 
 单位为毫秒，必须大于0，可大于1天</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateFormatUtilTest.html#formatDuration--">formatDuration()</a></span> - 类 中的方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateFormatUtilTest.html" title="com.huazheng.tunny.tools.time中的类">DateFormatUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html#formatDurationOnSecond-java.util.Date-java.util.Date-">formatDurationOnSecond(Date, Date)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html" title="com.huazheng.tunny.tools.time中的类">DateFormatUtil</a></dt>
<dd>
<div class="block">按HH:mm:ss格式，格式化时间间隔
 
 endDate必须大于startDate，间隔可大于1天</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html#formatDurationOnSecond-long-">formatDurationOnSecond(long)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html" title="com.huazheng.tunny.tools.time中的类">DateFormatUtil</a></dt>
<dd>
<div class="block">按HH:mm:ss格式，格式化时间间隔
 
 单位为毫秒，必须大于0，可大于1天</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html#formatFriendlyTimeSpanByNow-java.util.Date-">formatFriendlyTimeSpanByNow(Date)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html" title="com.huazheng.tunny.tools.time中的类">DateFormatUtil</a></dt>
<dd>
<div class="block">打印用户友好的，与当前时间相比的时间差，如刚刚，5分钟前，今天XXX，昨天XXX
 
 copy from AndroidUtilCode</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html#formatFriendlyTimeSpanByNow-long-">formatFriendlyTimeSpanByNow(long)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html" title="com.huazheng.tunny.tools.time中的类">DateFormatUtil</a></dt>
<dd>
<div class="block">打印用户友好的，与当前时间相比的时间差，如刚刚，5分钟前，今天XXX，昨天XXX
 
 copy from AndroidUtilCode</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateFormatUtilTest.html#formatFriendlyTimeSpanByNow--">formatFriendlyTimeSpanByNow()</a></span> - 类 中的方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateFormatUtilTest.html" title="com.huazheng.tunny.tools.time中的类">DateFormatUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateFormatUtilTest.html#formatWithPattern--">formatWithPattern()</a></span> - 类 中的方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateFormatUtilTest.html" title="com.huazheng.tunny.tools.time中的类">DateFormatUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/CsvUtil.html#fromCsvString-java.lang.String-">fromCsvString(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/CsvUtil.html" title="com.huazheng.tunny.tools.text中的类">CsvUtil</a></dt>
<dd>
<div class="block">Converts CSV line to string array.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/CsvUtilTest.html#fromCsvString--">fromCsvString()</a></span> - 类 中的方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/CsvUtilTest.html" title="com.huazheng.tunny.tools.text中的类">CsvUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/net/IPUtil.html#fromInt-int-">fromInt(int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.net.<a href="../com/huazheng/tunny/tools/net/IPUtil.html" title="com.huazheng.tunny.tools.net中的类">IPUtil</a></dt>
<dd>
<div class="block">从int转换为Inet4Address(仅支持IPV4)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/net/IPUtil.html#fromIpString-java.lang.String-">fromIpString(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.net.<a href="../com/huazheng/tunny/tools/net/IPUtil.html" title="com.huazheng.tunny.tools.net中的类">IPUtil</a></dt>
<dd>
<div class="block">从String转换为InetAddress.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/net/IPUtil.html#fromIpv4String-java.lang.String-">fromIpv4String(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.net.<a href="../com/huazheng/tunny/tools/net/IPUtil.html" title="com.huazheng.tunny.tools.net中的类">IPUtil</a></dt>
<dd>
<div class="block">从IPv4String转换为InetAddress.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/JsonMapper.html#fromJson-java.lang.String-java.lang.Class-">fromJson(String, Class&lt;T&gt;)</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/JsonMapper.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapper</a></dt>
<dd>
<div class="block">反序列化POJO或简单Collection如List<String>.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/JsonMapper.html#fromJson-java.lang.String-com.fasterxml.jackson.databind.JavaType-">fromJson(String, JavaType)</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/JsonMapper.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapper</a></dt>
<dd>
<div class="block">反序列化复杂Collection如List<Bean>, contructCollectionType()或contructMapType()构造类型, 然后调用本函数.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.html#fromJson--">fromJson()</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/JsonMapperTest.html" title="com.huazheng.tunny.tools.mapper中的类">JsonMapperTest</a></dt>
<dd>
<div class="block">从Json字符串反序列化对象/集合.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/EnumUtil.html#fromString-java.lang.Class-java.lang.String-">fromString(Class&lt;T&gt;, String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/EnumUtil.html" title="com.huazheng.tunny.tools.base中的类">EnumUtil</a></dt>
<dd>
<div class="block">String转换为Enum</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/XmlMapper.html#fromXml-java.lang.String-java.lang.Class-">fromXml(String, Class&lt;T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/XmlMapper.html" title="com.huazheng.tunny.tools.mapper中的类">XmlMapper</a></dt>
<dd>
<div class="block">Xml->Java Object.</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-5.html">上一个字母</a></li>
<li><a href="index-7.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-6.html" target="_top">框架</a></li>
<li><a href="index-6.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
