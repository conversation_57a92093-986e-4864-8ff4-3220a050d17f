<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:35 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>A - 索引</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="A - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个字母</li>
<li><a href="index-2.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-1.html" target="_top">框架</a></li>
<li><a href="index-1.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a name="I:A">
<!--   -->
</a>
<h2 class="title">A</h2>
<dl>
<dt><a href="../com/huazheng/tunny/tools/concurrent/threadpool/AbortPolicyWithReport.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><span class="typeNameLink">AbortPolicyWithReport</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/threadpool/package-summary.html">com.huazheng.tunny.tools.concurrent.threadpool</a>中的类</dt>
<dd>
<div class="block">移植至Dubbo.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/AbortPolicyWithReport.html#AbortPolicyWithReport-java.lang.String-">AbortPolicyWithReport(String)</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/AbortPolicyWithReport.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">AbortPolicyWithReport</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/threadpool/AbortPolicyWithReportTest.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><span class="typeNameLink">AbortPolicyWithReportTest</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/threadpool/package-summary.html">com.huazheng.tunny.tools.concurrent.threadpool</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/AbortPolicyWithReportTest.html#AbortPolicyWithReportTest--">AbortPolicyWithReportTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/AbortPolicyWithReportTest.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">AbortPolicyWithReportTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.AClass.html#AClass--">AClass()</a></span> - 类 的构造器com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.AClass.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtilTest.AClass</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html#add-E-">add(E)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.<a href="../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="com.huazheng.tunny.tools.collection.type中的类">ConcurrentHashSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/SortedArrayList.html#add-E-">add(E)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.<a href="../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="com.huazheng.tunny.tools.collection.type中的类">SortedArrayList</a></dt>
<dd>
<div class="block">Adds an Object to sorted list.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/SortedArrayList.html#add-int-E-">add(int, E)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.<a href="../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="com.huazheng.tunny.tools.collection.type中的类">SortedArrayList</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#add-long-">add(long)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.jsr166e.<a href="../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html" title="com.huazheng.tunny.tools.concurrent.jsr166e中的类">LongAdder</a></dt>
<dd>
<div class="block">Adds the given value.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/SortedArrayList.html#addAll-java.util.Collection-">addAll(Collection&lt;? extends E&gt;)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.<a href="../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="com.huazheng.tunny.tools.collection.type中的类">SortedArrayList</a></dt>
<dd>
<div class="block">Add all of the elements in the given collection to this list.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/SortedArrayList.html#addAll-int-java.util.Collection-">addAll(int, Collection&lt;? extends E&gt;)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.<a href="../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="com.huazheng.tunny.tools.collection.type中的类">SortedArrayList</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">已过时。</span></div>
</dd>
<dt><span class="memberNameLink"><a href="../com/vip/vjtools/test/log/LogbackListAppenderTest.html#addAndRemoveAppender--">addAndRemoveAppender()</a></span> - 类 中的方法com.vip.vjtools.test.log.<a href="../com/vip/vjtools/test/log/LogbackListAppenderTest.html" title="com.vip.vjtools.test.log中的类">LogbackListAppenderTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#addDays-java.util.Date-int-">addDays(Date, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">加一天</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#addHours-java.util.Date-int-">addHours(Date, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">加一小时</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#addMinutes-java.util.Date-int-">addMinutes(Date, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">加一分钟</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#addMonths-java.util.Date-int-">addMonths(Date, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">加一月</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#addSeconds-java.util.Date-int-">addSeconds(Date, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">终于到了，续一秒.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/RuntimeUtil.html#addShutdownHook-java.lang.Runnable-">addShutdownHook(Runnable)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/RuntimeUtil.html" title="com.huazheng.tunny.tools.base中的类">RuntimeUtil</a></dt>
<dd>
<div class="block">注册JVM关闭时的钩子程序</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/vip/vjtools/test/log/LogbackListAppender.html#addToLogger-java.lang.String-">addToLogger(String)</a></span> - 类 中的方法com.vip.vjtools.test.log.<a href="../com/vip/vjtools/test/log/LogbackListAppender.html" title="com.vip.vjtools.test.log中的类">LogbackListAppender</a></dt>
<dd>
<div class="block">将此appender添加到logger中.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/vip/vjtools/test/log/LogbackListAppender.html#addToLogger-java.lang.Class-">addToLogger(Class&lt;?&gt;)</a></span> - 类 中的方法com.vip.vjtools.test.log.<a href="../com/vip/vjtools/test/log/LogbackListAppender.html" title="com.vip.vjtools.test.log中的类">LogbackListAppender</a></dt>
<dd>
<div class="block">将此appender添加到logger中.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/vip/vjtools/test/log/LogbackListAppender.html#addToRootLogger--">addToRootLogger()</a></span> - 类 中的方法com.vip.vjtools.test.log.<a href="../com/vip/vjtools/test/log/LogbackListAppender.html" title="com.vip.vjtools.test.log中的类">LogbackListAppender</a></dt>
<dd>
<div class="block">将此appender添加到root logger中.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#addWeeks-java.util.Date-int-">addWeeks(Date, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>
<div class="block">加一周</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/security/CryptoUtilTest.html#aes--">aes()</a></span> - 类 中的方法com.huazheng.tunny.tools.security.<a href="../com/huazheng/tunny/tools/security/CryptoUtilTest.html" title="com.huazheng.tunny.tools.security中的类">CryptoUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/security/CryptoUtil.html#aesDecrypt-byte:A-byte:A-">aesDecrypt(byte[], byte[])</a></span> - 类 中的静态方法com.huazheng.tunny.tools.security.<a href="../com/huazheng/tunny/tools/security/CryptoUtil.html" title="com.huazheng.tunny.tools.security中的类">CryptoUtil</a></dt>
<dd>
<div class="block">使用AES解密字符串, 返回原始字符串.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/security/CryptoUtil.html#aesDecrypt-byte:A-byte:A-byte:A-">aesDecrypt(byte[], byte[], byte[])</a></span> - 类 中的静态方法com.huazheng.tunny.tools.security.<a href="../com/huazheng/tunny/tools/security/CryptoUtil.html" title="com.huazheng.tunny.tools.security中的类">CryptoUtil</a></dt>
<dd>
<div class="block">使用AES解密字符串, 返回原始字符串.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/security/CryptoUtil.html#aesEncrypt-byte:A-byte:A-">aesEncrypt(byte[], byte[])</a></span> - 类 中的静态方法com.huazheng.tunny.tools.security.<a href="../com/huazheng/tunny/tools/security/CryptoUtil.html" title="com.huazheng.tunny.tools.security中的类">CryptoUtil</a></dt>
<dd>
<div class="block">使用AES加密原始字符串.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/security/CryptoUtil.html#aesEncrypt-byte:A-byte:A-byte:A-">aesEncrypt(byte[], byte[], byte[])</a></span> - 类 中的静态方法com.huazheng.tunny.tools.security.<a href="../com/huazheng/tunny/tools/security/CryptoUtil.html" title="com.huazheng.tunny.tools.security中的类">CryptoUtil</a></dt>
<dd>
<div class="block">使用AES加密原始字符串.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/security/CryptoUtilTest.html#aesWithIV--">aesWithIV()</a></span> - 类 中的方法com.huazheng.tunny.tools.security.<a href="../com/huazheng/tunny/tools/security/CryptoUtilTest.html" title="com.huazheng.tunny.tools.security中的类">CryptoUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.html#afterExecute-java.lang.Runnable-java.lang.Throwable-">afterExecute(Runnable, Throwable)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">QueuableCachedThreadPool</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/SamplerTest.html#always--">always()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/SamplerTest.html" title="com.huazheng.tunny.tools.concurrent中的类">SamplerTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/Sampler.AlwaysSampler.html#AlwaysSampler--">AlwaysSampler()</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/Sampler.AlwaysSampler.html" title="com.huazheng.tunny.tools.concurrent中的类">Sampler.AlwaysSampler</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/BooleanUtil.html#and-boolean...-">and(boolean...)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/BooleanUtil.html" title="com.huazheng.tunny.tools.base中的类">BooleanUtil</a></dt>
<dd>
<div class="block">多个值的and</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/reflect/AnnotationUtil.html" title="com.huazheng.tunny.tools.reflect中的类"><span class="typeNameLink">AnnotationUtil</span></a> - <a href="../com/huazheng/tunny/tools/reflect/package-summary.html">com.huazheng.tunny.tools.reflect</a>中的类</dt>
<dd>
<div class="block">Annotation的工具类
 
 1.获得类的全部Annotation
 
 2.获取类的标注了annotation的所有属性和方法</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/AnnotationUtil.html#AnnotationUtil--">AnnotationUtil()</a></span> - 类 的构造器com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/AnnotationUtil.html" title="com.huazheng.tunny.tools.reflect中的类">AnnotationUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#append-java.lang.CharSequence-java.io.File-">append(CharSequence, File)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">追加String到File.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html#append-char-">append(char)</a></span> - 类 中的方法com.huazheng.tunny.tools.io.type.<a href="../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html" title="com.huazheng.tunny.tools.io.type中的类">StringBuilderWriter</a></dt>
<dd>
<div class="block">Appends a single character to this Writer.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html#append-java.lang.CharSequence-">append(CharSequence)</a></span> - 类 中的方法com.huazheng.tunny.tools.io.type.<a href="../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html" title="com.huazheng.tunny.tools.io.type中的类">StringBuilderWriter</a></dt>
<dd>
<div class="block">Appends a character sequence to this Writer.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html#append-java.lang.CharSequence-int-int-">append(CharSequence, int, int)</a></span> - 类 中的方法com.huazheng.tunny.tools.io.type.<a href="../com/huazheng/tunny/tools/io/type/StringBuilderWriter.html" title="com.huazheng.tunny.tools.io.type中的类">StringBuilderWriter</a></dt>
<dd>
<div class="block">Appends a portion of a character sequence to the <code>StringBuilder</code>.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/vip/vjtools/test/log/LogbackListAppender.html#append-ch.qos.logback.classic.spi.ILoggingEvent-">append(ILoggingEvent)</a></span> - 类 中的方法com.vip.vjtools.test.log.<a href="../com/vip/vjtools/test/log/LogbackListAppender.html" title="com.vip.vjtools.test.log中的类">LogbackListAppender</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileTreeWalker.AntPathFilter.html#apply-java.io.File-">apply(File)</a></span> - 类 中的方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileTreeWalker.AntPathFilter.html" title="com.huazheng.tunny.tools.io中的类">FileTreeWalker.AntPathFilter</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileTreeWalker.FileExtensionFilter.html#apply-java.io.File-">apply(File)</a></span> - 类 中的方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileTreeWalker.FileExtensionFilter.html" title="com.huazheng.tunny.tools.io中的类">FileTreeWalker.FileExtensionFilter</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileTreeWalker.RegexFileNameFilter.html#apply-java.io.File-">apply(File)</a></span> - 类 中的方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileTreeWalker.RegexFileNameFilter.html" title="com.huazheng.tunny.tools.io中的类">FileTreeWalker.RegexFileNameFilter</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileTreeWalker.WildcardFileNameFilter.html#apply-java.io.File-">apply(File)</a></span> - 类 中的方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileTreeWalker.WildcardFileNameFilter.html" title="com.huazheng.tunny.tools.io中的类">FileTreeWalker.WildcardFileNameFilter</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/collection/ArrayUtil.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">ArrayUtil</span></a> - <a href="../com/huazheng/tunny/tools/collection/package-summary.html">com.huazheng.tunny.tools.collection</a>中的类</dt>
<dd>
<div class="block">数组工具类.
 
 1.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ArrayUtil.html#ArrayUtil--">ArrayUtil()</a></span> - 类 的构造器com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ArrayUtil.html" title="com.huazheng.tunny.tools.collection中的类">ArrayUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/collection/ArrayUtilTest.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">ArrayUtilTest</span></a> - <a href="../com/huazheng/tunny/tools/collection/package-summary.html">com.huazheng.tunny.tools.collection</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ArrayUtilTest.html#ArrayUtilTest--">ArrayUtilTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ArrayUtilTest.html" title="com.huazheng.tunny.tools.collection中的类">ArrayUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#asBufferedReader-java.lang.String-">asBufferedReader(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">获取File的BufferedReader.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#asBufferedReader-java.nio.file.Path-">asBufferedReader(Path)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#asBufferedWriter-java.lang.String-">asBufferedWriter(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">获取File的BufferedWriter.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#asBufferedWriter-java.nio.file.Path-">asBufferedWriter(Path)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">获取File的BufferedWriter.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/Charsets.html#ASCII_NAME">ASCII_NAME</a></span> - 类 中的静态变量com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/Charsets.html" title="com.huazheng.tunny.tools.text中的类">Charsets</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/URLResourceUtil.html#asFile-java.lang.String-">asFile(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/URLResourceUtil.html" title="com.huazheng.tunny.tools.io中的类">URLResourceUtil</a></dt>
<dd>
<div class="block">兼容无前缀, classpath:, file:// 的情况获取文件
 
 如果以classpath: 定义的文件不存在会抛出IllegalArgumentException异常，以file://定义的则不会</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#asInputStream-java.lang.String-">asInputStream(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">打开文件为InputStream.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#asInputStream-java.io.File-">asInputStream(File)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">打开文件为InputStream.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#asInputStream-java.nio.file.Path-">asInputStream(Path)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">打开文件为InputStream.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ArrayUtil.html#asList-T...-">asList(T...)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ArrayUtil.html" title="com.huazheng.tunny.tools.collection中的类">ArrayUtil</a></dt>
<dd>
<div class="block">原版将数组转换为List.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/ArrayUtilTest.html#asList--">asList()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/ArrayUtilTest.html" title="com.huazheng.tunny.tools.collection中的类">ArrayUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#asOututStream-java.lang.String-">asOututStream(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">打开文件为OutputStream.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#asOututStream-java.io.File-">asOututStream(File)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">打开文件为OutputStream.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#asOututStream-java.nio.file.Path-">asOututStream(Path)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">打开文件为OutputStream.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/ResourceUtil.html#asStream-java.lang.String-">asStream(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/ResourceUtil.html" title="com.huazheng.tunny.tools.io中的类">ResourceUtil</a></dt>
<dd>
<div class="block">读取规则见本类注释.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/ResourceUtil.html#asStream-java.lang.Class-java.lang.String-">asStream(Class&lt;?&gt;, String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/ResourceUtil.html" title="com.huazheng.tunny.tools.io中的类">ResourceUtil</a></dt>
<dd>
<div class="block">读取文件的每一行，读取规则见本类注释.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/URLResourceUtil.html#asStream-java.lang.String-">asStream(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/URLResourceUtil.html" title="com.huazheng.tunny.tools.io中的类">URLResourceUtil</a></dt>
<dd>
<div class="block">兼容无前缀, classpath:, file:// 的情况打开文件成Stream</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/ResourceUtil.html#asUrl-java.lang.String-">asUrl(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/ResourceUtil.html" title="com.huazheng.tunny.tools.io中的类">ResourceUtil</a></dt>
<dd>
<div class="block">读取规则见本类注释.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/ResourceUtil.html#asUrl-java.lang.Class-java.lang.String-">asUrl(Class&lt;?&gt;, String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/ResourceUtil.html" title="com.huazheng.tunny.tools.io中的类">ResourceUtil</a></dt>
<dd>
<div class="block">读取规则见本类注释.</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个字母</li>
<li><a href="index-2.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-1.html" target="_top">框架</a></li>
<li><a href="index-1.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
