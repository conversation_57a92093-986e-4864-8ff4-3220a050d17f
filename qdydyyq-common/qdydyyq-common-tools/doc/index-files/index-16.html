<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:36 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>P - 索引</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="P - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-15.html">上一个字母</a></li>
<li><a href="index-17.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-16.html" target="_top">框架</a></li>
<li><a href="index-16.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a name="I:P">
<!--   -->
</a>
<h2 class="title">P</h2>
<dl>
<dt><a href="../com/huazheng/tunny/tools/base/type/Pair.html" title="com.huazheng.tunny.tools.base.type中的类"><span class="typeNameLink">Pair</span></a>&lt;<a href="../com/huazheng/tunny/tools/base/type/Pair.html" title="Pair中的类型参数">L</a>,<a href="../com/huazheng/tunny/tools/base/type/Pair.html" title="Pair中的类型参数">R</a>&gt; - <a href="../com/huazheng/tunny/tools/base/type/package-summary.html">com.huazheng.tunny.tools.base.type</a>中的类</dt>
<dd>
<div class="block">引入一个简简单单的Pair, 用于返回值返回两个元素.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/type/Pair.html#Pair-L-R-">Pair(L, R)</a></span> - 类 的构造器com.huazheng.tunny.tools.base.type.<a href="../com/huazheng/tunny/tools/base/type/Pair.html" title="com.huazheng.tunny.tools.base.type中的类">Pair</a></dt>
<dd>
<div class="block">Creates a new pair.</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/base/PairTest.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">PairTest</span></a> - <a href="../com/huazheng/tunny/tools/base/package-summary.html">com.huazheng.tunny.tools.base</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/PairTest.html#PairTest--">PairTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/PairTest.html" title="com.huazheng.tunny.tools.base中的类">PairTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/PairTest.html#pairTest--">pairTest()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/PairTest.html" title="com.huazheng.tunny.tools.base中的类">PairTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.ParentBean.html#ParentBean--">ParentBean()</a></span> - 类 的构造器com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ClassUtilTest.ParentBean.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtilTest.ParentBean</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.ParentBean.html#ParentBean--">ParentBean()</a></span> - 类 的构造器com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.ParentBean.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtilTest.ParentBean</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html#parseDate-java.lang.String-java.lang.String-">parseDate(String, String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html" title="com.huazheng.tunny.tools.time中的类">DateFormatUtil</a></dt>
<dd>
<div class="block">分析日期字符串, 仅用于pattern不固定的情况.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/BooleanUtil.html#parseGeneralString-java.lang.String-">parseGeneralString(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/BooleanUtil.html" title="com.huazheng.tunny.tools.base中的类">BooleanUtil</a></dt>
<dd>
<div class="block">支持true/false, on/off, y/n, yes/no的转换, str为空或无法分析时返回null</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/BooleanUtil.html#parseGeneralString-java.lang.String-java.lang.Boolean-">parseGeneralString(String, Boolean)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/BooleanUtil.html" title="com.huazheng.tunny.tools.base中的类">BooleanUtil</a></dt>
<dd>
<div class="block">支持true/false,on/off, y/n, yes/no的转换, str为空或无法分析时返回defaultValue</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateFormatUtilTest.html#parseWithPattern--">parseWithPattern()</a></span> - 类 中的方法com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateFormatUtilTest.html" title="com.huazheng.tunny.tools.time中的类">DateFormatUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/WildcardMatcher.html#PATH_MATCH">PATH_MATCH</a></span> - 类 中的静态变量com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/WildcardMatcher.html" title="com.huazheng.tunny.tools.text中的类">WildcardMatcher</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/WildcardMatcher.html#PATH_SPLITTER">PATH_SPLITTER</a></span> - 类 中的静态变量com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/WildcardMatcher.html" title="com.huazheng.tunny.tools.text中的类">WildcardMatcher</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FilePathUtilTest.html#pathName--">pathName()</a></span> - 类 中的方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FilePathUtilTest.html" title="com.huazheng.tunny.tools.io中的类">FilePathUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html#PATTERN_DEFAULT">PATTERN_DEFAULT</a></span> - 类 中的静态变量com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html" title="com.huazheng.tunny.tools.time中的类">DateFormatUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html#PATTERN_DEFAULT_ON_SECOND">PATTERN_DEFAULT_ON_SECOND</a></span> - 类 中的静态变量com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html" title="com.huazheng.tunny.tools.time中的类">DateFormatUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html#PATTERN_ISO">PATTERN_ISO</a></span> - 类 中的静态变量com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html" title="com.huazheng.tunny.tools.time中的类">DateFormatUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html#PATTERN_ISO_ON_DATE">PATTERN_ISO_ON_DATE</a></span> - 类 中的静态变量com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html" title="com.huazheng.tunny.tools.time中的类">DateFormatUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html#PATTERN_ISO_ON_SECOND">PATTERN_ISO_ON_SECOND</a></span> - 类 中的静态变量com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateFormatUtil.html" title="com.huazheng.tunny.tools.time中的类">DateFormatUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/base/Platforms.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">Platforms</span></a> - <a href="../com/huazheng/tunny/tools/base/package-summary.html">com.huazheng.tunny.tools.base</a>中的类</dt>
<dd>
<div class="block">关于系统设定，平台信息的变量(via Common Lang SystemUtils)</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/Platforms.html#Platforms--">Platforms()</a></span> - 类 的构造器com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/Platforms.html" title="com.huazheng.tunny.tools.base中的类">Platforms</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/base/PlatformsTest.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">PlatformsTest</span></a> - <a href="../com/huazheng/tunny/tools/base/package-summary.html">com.huazheng.tunny.tools.base</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/PlatformsTest.html#PlatformsTest--">PlatformsTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/PlatformsTest.html" title="com.huazheng.tunny.tools.base中的类">PlatformsTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/PlatformsTest.html#PlatformTest--">PlatformTest()</a></span> - 类 中的方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/PlatformsTest.html" title="com.huazheng.tunny.tools.base中的类">PlatformsTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/net/NetUtil.html#PORT_RANGE_MAX">PORT_RANGE_MAX</a></span> - 类 中的静态变量com.huazheng.tunny.tools.net.<a href="../com/huazheng/tunny/tools/net/NetUtil.html" title="com.huazheng.tunny.tools.net中的类">NetUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/net/NetUtil.html#PORT_RANGE_MIN">PORT_RANGE_MIN</a></span> - 类 中的静态变量com.huazheng.tunny.tools.net.<a href="../com/huazheng/tunny/tools/net/NetUtil.html" title="com.huazheng.tunny.tools.net中的类">NetUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/net/NetUtilTest.html#portDetect--">portDetect()</a></span> - 类 中的方法com.huazheng.tunny.tools.net.<a href="../com/huazheng/tunny/tools/net/NetUtilTest.html" title="com.huazheng.tunny.tools.net中的类">NetUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/MoreValidate.html#positive-java.lang.String-int-">positive(String, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/MoreValidate.html" title="com.huazheng.tunny.tools.base中的类">MoreValidate</a></dt>
<dd>
<div class="block">校验为正数则返回该数字，否则抛出异常.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/MoreValidate.html#positive-java.lang.String-java.lang.Integer-">positive(String, Integer)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/MoreValidate.html" title="com.huazheng.tunny.tools.base中的类">MoreValidate</a></dt>
<dd>
<div class="block">校验为正数则返回该数字，否则抛出异常.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/MoreValidate.html#positive-java.lang.String-long-">positive(String, long)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/MoreValidate.html" title="com.huazheng.tunny.tools.base中的类">MoreValidate</a></dt>
<dd>
<div class="block">校验为正数则返回该数字，否则抛出异常.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/MoreValidate.html#positive-java.lang.String-java.lang.Long-">positive(String, Long)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/MoreValidate.html" title="com.huazheng.tunny.tools.base中的类">MoreValidate</a></dt>
<dd>
<div class="block">校验为正数则返回该数字，否则抛出异常.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/MoreValidate.html#positive-java.lang.String-double-">positive(String, double)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/MoreValidate.html" title="com.huazheng.tunny.tools.base中的类">MoreValidate</a></dt>
<dd>
<div class="block">校验为正数则返回该数字，否则抛出异常.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/MathUtil.html#pow-int-int-">pow(int, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/MathUtil.html" title="com.huazheng.tunny.tools.number中的类">MathUtil</a></dt>
<dd>
<div class="block">平方</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/MathUtil.html#pow-long-int-">pow(long, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/MathUtil.html" title="com.huazheng.tunny.tools.number中的类">MathUtil</a></dt>
<dd>
<div class="block">平方</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/MathUtilTest.html#power2--">power2()</a></span> - 类 中的方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/MathUtilTest.html" title="com.huazheng.tunny.tools.number中的类">MathUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/MathUtil.html#previousPowerOfTwo-int-">previousPowerOfTwo(int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/MathUtil.html" title="com.huazheng.tunny.tools.number中的类">MathUtil</a></dt>
<dd>
<div class="block">往下找出最接近2的倍数，比如15返回8， 17返回16.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/MathUtil.html#previousPowerOfTwo-long-">previousPowerOfTwo(long)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/MathUtil.html" title="com.huazheng.tunny.tools.number中的类">MathUtil</a></dt>
<dd>
<div class="block">往下找出最接近2的倍数，比如15返回8， 17返回16.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/EnumUtil.html#processBits-java.lang.Class-long-">processBits(Class&lt;E&gt;, long)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/EnumUtil.html" title="com.huazheng.tunny.tools.base中的类">EnumUtil</a></dt>
<dd>
<div class="block">long重新解析为若干个枚举值，用于使用long保存多个选项的情况.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.PropertiesListener.html#PropertiesListener-java.lang.String-">PropertiesListener(String)</a></span> - 类 的构造器com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.PropertiesListener.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtil.PropertiesListener</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/base/PropertiesUtil.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">PropertiesUtil</span></a> - <a href="../com/huazheng/tunny/tools/base/package-summary.html">com.huazheng.tunny.tools.base</a>中的类</dt>
<dd>
<div class="block">关于Properties的工具类
 
 1.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/PropertiesUtil.html#PropertiesUtil--">PropertiesUtil()</a></span> - 类 的构造器com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/PropertiesUtil.html" title="com.huazheng.tunny.tools.base中的类">PropertiesUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/base/PropertiesUtilTest.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">PropertiesUtilTest</span></a> - <a href="../com/huazheng/tunny/tools/base/package-summary.html">com.huazheng.tunny.tools.base</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/PropertiesUtilTest.html#PropertiesUtilTest--">PropertiesUtilTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/PropertiesUtilTest.html" title="com.huazheng.tunny.tools.base中的类">PropertiesUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.PropertiesListener.html#propertyName">propertyName</a></span> - 类 中的变量com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.PropertiesListener.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtil.PropertiesListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html#put-int-V-">put(int, V)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">IntObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html#put-java.lang.Integer-V-">put(Integer, V)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">IntObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectMap.html#put-int-V-">put(int, V)</a></span> - 接口 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口">IntObjectMap</a></dt>
<dd>
<div class="block">Puts the given entry into the map.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#put-long-V-">put(long, V)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">LongObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#put-java.lang.Long-V-">put(Long, V)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">LongObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html#put-long-V-">put(long, V)</a></span> - 接口 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口">LongObjectMap</a></dt>
<dd>
<div class="block">Puts the given entry into the map.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/type/ThreadLocalContext.html#put-java.lang.String-java.lang.Object-">put(String, Object)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.concurrent.type.<a href="../com/huazheng/tunny/tools/concurrent/type/ThreadLocalContext.html" title="com.huazheng.tunny.tools.concurrent.type中的类">ThreadLocalContext</a></dt>
<dd>
<div class="block">放入ThreadLocal的上下文信息.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html#putAll-java.util.Map-">putAll(Map&lt;? extends Integer, ? extends V&gt;)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">IntObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#putAll-java.util.Map-">putAll(Map&lt;? extends Long, ? extends V&gt;)</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.type.primitive.<a href="../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">LongObjectHashMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtil.html#putIfAbsentReturnLast-java.util.concurrent.ConcurrentMap-K-V-">putIfAbsentReturnLast(ConcurrentMap&lt;K, V&gt;, K, V)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtil.html" title="com.huazheng.tunny.tools.collection中的类">MapUtil</a></dt>
<dd>
<div class="block">ConcurrentMap的putIfAbsent()返回之前的Value，此函数封装返回最终存储在Map中的Value</div>
</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-15.html">上一个字母</a></li>
<li><a href="index-17.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-16.html" target="_top">框架</a></li>
<li><a href="index-16.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
