<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:36 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>M - 索引</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="M - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-12.html">上一个字母</a></li>
<li><a href="index-14.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-13.html" target="_top">框架</a></li>
<li><a href="index-13.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a name="I:M">
<!--   -->
</a>
<h2 class="title">M</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/security/CryptoUtilTest.html#mac--">mac()</a></span> - 类 中的方法com.huazheng.tunny.tools.security.<a href="../com/huazheng/tunny/tools/security/CryptoUtilTest.html" title="com.huazheng.tunny.tools.security中的类">CryptoUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#makeAccessible-java.lang.reflect.Method-">makeAccessible(Method)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtil</a></dt>
<dd>
<div class="block">改变private/protected的方法为可访问，尽量不进行改变，避免JDK的SecurityManager抱怨。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#makeAccessible-java.lang.reflect.Field-">makeAccessible(Field)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.reflect.<a href="../com/huazheng/tunny/tools/reflect/ReflectionUtil.html" title="com.huazheng.tunny.tools.reflect中的类">ReflectionUtil</a></dt>
<dd>
<div class="block">改变private/protected的成员变量为可访问，尽量不进行改变，避免JDK的SecurityManager抱怨。</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#makesureDirExists-java.lang.String-">makesureDirExists(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">确保目录存在, 如不存在则创建</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#makesureDirExists-java.io.File-">makesureDirExists(File)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">确保目录存在, 如不存在则创建</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#makesureDirExists-java.nio.file.Path-">makesureDirExists(Path)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">确保目录存在, 如不存在则创建.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#makesureParentDirExists-java.io.File-">makesureParentDirExists(File)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">确保父目录及其父目录直到根目录都已经创建.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapper.html#map-S-java.lang.Class-">map(S, Class&lt;D&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapper.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapper</a></dt>
<dd>
<div class="block">简单的复制出新类型对象.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapper.html#mapArray-S:A-java.lang.Class-">mapArray(S[], Class&lt;D&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapper.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapper</a></dt>
<dd>
<div class="block">简单复制出新对象数组</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/BeanMapper.html#mapList-java.lang.Iterable-java.lang.Class-">mapList(Iterable&lt;S&gt;, Class&lt;D&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/BeanMapper.html" title="com.huazheng.tunny.tools.mapper中的类">BeanMapper</a></dt>
<dd>
<div class="block">简单的复制出新对象ArrayList</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/collection/MapUtil.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">MapUtil</span></a> - <a href="../com/huazheng/tunny/tools/collection/package-summary.html">com.huazheng.tunny.tools.collection</a>中的类</dt>
<dd>
<div class="block">关于Map的工具集合，
 
 1.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtil.html#MapUtil--">MapUtil()</a></span> - 类 的构造器com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtil.html" title="com.huazheng.tunny.tools.collection中的类">MapUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/collection/MapUtil.ValueCreator.html" title="com.huazheng.tunny.tools.collection中的接口"><span class="typeNameLink">MapUtil.ValueCreator</span></a>&lt;<a href="../com/huazheng/tunny/tools/collection/MapUtil.ValueCreator.html" title="MapUtil.ValueCreator中的类型参数">T</a>&gt; - <a href="../com/huazheng/tunny/tools/collection/package-summary.html">com.huazheng.tunny.tools.collection</a>中的接口</dt>
<dd>
<div class="block">Lazy创建Value值的回调类</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/collection/MapUtilTest.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">MapUtilTest</span></a> - <a href="../com/huazheng/tunny/tools/collection/package-summary.html">com.huazheng.tunny.tools.collection</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtilTest.html#MapUtilTest--">MapUtilTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtilTest.html" title="com.huazheng.tunny.tools.collection中的类">MapUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/collection/MapUtilTest.EnumA.html" title="com.huazheng.tunny.tools.collection中的枚举"><span class="typeNameLink">MapUtilTest.EnumA</span></a> - <a href="../com/huazheng/tunny/tools/collection/package-summary.html">com.huazheng.tunny.tools.collection</a>中的枚举</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/collection/MapUtilTest.MyBean.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">MapUtilTest.MyBean</span></a> - <a href="../com/huazheng/tunny/tools/collection/package-summary.html">com.huazheng.tunny.tools.collection</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/WildcardMatcher.html#match-java.lang.CharSequence-java.lang.CharSequence-">match(CharSequence, CharSequence)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/WildcardMatcher.html" title="com.huazheng.tunny.tools.text中的类">WildcardMatcher</a></dt>
<dd>
<div class="block">Checks whether a string matches a given wildcard pattern.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/WildcardMatcher.html#matchOne-java.lang.String-java.lang.String...-">matchOne(String, String...)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/WildcardMatcher.html" title="com.huazheng.tunny.tools.text中的类">WildcardMatcher</a></dt>
<dd>
<div class="block">Matches string to at least one pattern.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/WildcardMatcher.html#matchPath-java.lang.String-java.lang.String-">matchPath(String, String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/WildcardMatcher.html" title="com.huazheng.tunny.tools.text中的类">WildcardMatcher</a></dt>
<dd>
<div class="block">Matches path against pattern using *, ?</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/WildcardMatcherTest.html#matchPath--">matchPath()</a></span> - 类 中的方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/WildcardMatcherTest.html" title="com.huazheng.tunny.tools.text中的类">WildcardMatcherTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/WildcardMatcher.html#matchPathOne-java.lang.String-java.lang.String...-">matchPathOne(String, String...)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/WildcardMatcher.html" title="com.huazheng.tunny.tools.text中的类">WildcardMatcher</a></dt>
<dd>
<div class="block">Matches path to at least one pattern.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/WildcardMatcherTest.html#matchString--">matchString()</a></span> - 类 中的方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/WildcardMatcherTest.html" title="com.huazheng.tunny.tools.text中的类">WildcardMatcherTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/WildcardMatcher.html#matchTokens-java.lang.String:A-java.lang.String:A-">matchTokens(String[], String[])</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/WildcardMatcher.html" title="com.huazheng.tunny.tools.text中的类">WildcardMatcher</a></dt>
<dd>
<div class="block">Match tokenized string and pattern.</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/number/MathUtil.html" title="com.huazheng.tunny.tools.number中的类"><span class="typeNameLink">MathUtil</span></a> - <a href="../com/huazheng/tunny/tools/number/package-summary.html">com.huazheng.tunny.tools.number</a>中的类</dt>
<dd>
<div class="block">数学相关工具类.包括
 
 1. 2的倍数的计算
 
 2.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/MathUtil.html#MathUtil--">MathUtil()</a></span> - 类 的构造器com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/MathUtil.html" title="com.huazheng.tunny.tools.number中的类">MathUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/number/MathUtilTest.html" title="com.huazheng.tunny.tools.number中的类"><span class="typeNameLink">MathUtilTest</span></a> - <a href="../com/huazheng/tunny/tools/number/package-summary.html">com.huazheng.tunny.tools.number</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/MathUtilTest.html#MathUtilTest--">MathUtilTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/MathUtilTest.html" title="com.huazheng.tunny.tools.number中的类">MathUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/CollectionUtil.html#max-java.util.Collection-">max(Collection&lt;? extends T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/CollectionUtil.html" title="com.huazheng.tunny.tools.collection中的类">CollectionUtil</a></dt>
<dd>
<div class="block">返回无序集合中的最大值，使用元素默认排序</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/CollectionUtil.html#max-java.util.Collection-java.util.Comparator-">max(Collection&lt;? extends T&gt;, Comparator&lt;? super T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/CollectionUtil.html" title="com.huazheng.tunny.tools.collection中的类">CollectionUtil</a></dt>
<dd>
<div class="block">返回无序集合中的最大值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/HashUtil.html#md5File-java.io.InputStream-">md5File(InputStream)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/HashUtil.html" title="com.huazheng.tunny.tools.text中的类">HashUtil</a></dt>
<dd>
<div class="block">对文件进行md5散列，被破解后MD5已较少人用.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/type/CloneableException.html#message">message</a></span> - 异常错误 中的变量com.huazheng.tunny.tools.base.type.<a href="../com/huazheng/tunny/tools/base/type/CloneableException.html" title="com.huazheng.tunny.tools.base.type中的类">CloneableException</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html#message">message</a></span> - 异常错误 中的变量com.huazheng.tunny.tools.base.type.<a href="../com/huazheng/tunny/tools/base/type/CloneableRuntimeException.html" title="com.huazheng.tunny.tools.base.type中的类">CloneableRuntimeException</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#MILLIS_PER_DAY">MILLIS_PER_DAY</a></span> - 类 中的静态变量com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#MILLIS_PER_HOUR">MILLIS_PER_HOUR</a></span> - 类 中的静态变量com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#MILLIS_PER_MINUTE">MILLIS_PER_MINUTE</a></span> - 类 中的静态变量com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/time/DateUtil.html#MILLIS_PER_SECOND">MILLIS_PER_SECOND</a></span> - 类 中的静态变量com.huazheng.tunny.tools.time.<a href="../com/huazheng/tunny/tools/time/DateUtil.html" title="com.huazheng.tunny.tools.time中的类">DateUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/CollectionUtil.html#min-java.util.Collection-">min(Collection&lt;? extends T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/CollectionUtil.html" title="com.huazheng.tunny.tools.collection中的类">CollectionUtil</a></dt>
<dd>
<div class="block">返回无序集合中的最小值，使用元素默认排序</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/CollectionUtil.html#min-java.util.Collection-java.util.Comparator-">min(Collection&lt;? extends T&gt;, Comparator&lt;? super T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/CollectionUtil.html" title="com.huazheng.tunny.tools.collection中的类">CollectionUtil</a></dt>
<dd>
<div class="block">返回无序集合中的最小值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/CollectionUtil.html#minAndMax-java.util.Collection-">minAndMax(Collection&lt;? extends T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/CollectionUtil.html" title="com.huazheng.tunny.tools.collection中的类">CollectionUtil</a></dt>
<dd>
<div class="block">同时返回无序集合中的最小值和最大值，使用元素默认排序
 
 在返回的Pair中，第一个为最小值，第二个为最大值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/CollectionUtil.html#minAndMax-java.util.Collection-java.util.Comparator-">minAndMax(Collection&lt;? extends T&gt;, Comparator&lt;? super T&gt;)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/CollectionUtil.html" title="com.huazheng.tunny.tools.collection中的类">CollectionUtil</a></dt>
<dd>
<div class="block">返回无序集合中的最小值和最大值
 
 在返回的Pair中，第一个为最小值，第二个为最大值</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/CollectionUtilTest.html#minAndMax--">minAndMax()</a></span> - 类 中的方法com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/CollectionUtilTest.html" title="com.huazheng.tunny.tools.collection中的类">CollectionUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/MathUtil.html#mod-int-int-">mod(int, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/MathUtil.html" title="com.huazheng.tunny.tools.number中的类">MathUtil</a></dt>
<dd>
<div class="block">保证结果为正数的取模.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/MathUtil.html#mod-long-long-">mod(long, long)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/MathUtil.html" title="com.huazheng.tunny.tools.number中的类">MathUtil</a></dt>
<dd>
<div class="block">保证结果为正数的取模.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/MathUtil.html#mod-long-int-">mod(long, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/MathUtil.html" title="com.huazheng.tunny.tools.number中的类">MathUtil</a></dt>
<dd>
<div class="block">保证结果为正数的取模</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/number/MathUtil.html#modByPowerOfTwo-int-int-">modByPowerOfTwo(int, int)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.number.<a href="../com/huazheng/tunny/tools/number/MathUtil.html" title="com.huazheng.tunny.tools.number中的类">MathUtil</a></dt>
<dd>
<div class="block">当模为2的倍数时，用比取模块更快的方式计算.</div>
</dd>
<dt><a href="../com/huazheng/tunny/tools/collection/MoreLists.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">MoreLists</span></a> - <a href="../com/huazheng/tunny/tools/collection/package-summary.html">com.huazheng.tunny.tools.collection</a>中的类</dt>
<dd>
<div class="block">特殊的List类型</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MoreLists.html#MoreLists--">MoreLists()</a></span> - 类 的构造器com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MoreLists.html" title="com.huazheng.tunny.tools.collection中的类">MoreLists</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/collection/MoreMaps.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">MoreMaps</span></a> - <a href="../com/huazheng/tunny/tools/collection/package-summary.html">com.huazheng.tunny.tools.collection</a>中的类</dt>
<dd>
<div class="block">来自Guava，Netty等的特殊Map类型</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MoreMaps.html#MoreMaps--">MoreMaps()</a></span> - 类 的构造器com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MoreMaps.html" title="com.huazheng.tunny.tools.collection中的类">MoreMaps</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/collection/MoreQueues.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">MoreQueues</span></a> - <a href="../com/huazheng/tunny/tools/collection/package-summary.html">com.huazheng.tunny.tools.collection</a>中的类</dt>
<dd>
<div class="block">特殊类型Queue:LIFO的Stack, LRU的Queue</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MoreQueues.html#MoreQueues--">MoreQueues()</a></span> - 类 的构造器com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MoreQueues.html" title="com.huazheng.tunny.tools.collection中的类">MoreQueues</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/text/MoreStringUtil.html" title="com.huazheng.tunny.tools.text中的类"><span class="typeNameLink">MoreStringUtil</span></a> - <a href="../com/huazheng/tunny/tools/text/package-summary.html">com.huazheng.tunny.tools.text</a>中的类</dt>
<dd>
<div class="block">尽量使用Common Lang StringUtils, 基本覆盖了所有类库的StringUtils
 
 本类仅补充少量额外方法, 尤其是针对char的运算
 
 1. split char/chars
 
 2.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/MoreStringUtil.html#MoreStringUtil--">MoreStringUtil()</a></span> - 类 的构造器com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/MoreStringUtil.html" title="com.huazheng.tunny.tools.text中的类">MoreStringUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/text/MoreStringUtilTest.html" title="com.huazheng.tunny.tools.text中的类"><span class="typeNameLink">MoreStringUtilTest</span></a> - <a href="../com/huazheng/tunny/tools/text/package-summary.html">com.huazheng.tunny.tools.text</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/MoreStringUtilTest.html#MoreStringUtilTest--">MoreStringUtilTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/MoreStringUtilTest.html" title="com.huazheng.tunny.tools.text中的类">MoreStringUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/base/MoreValidate.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">MoreValidate</span></a> - <a href="../com/huazheng/tunny/tools/base/package-summary.html">com.huazheng.tunny.tools.base</a>中的类</dt>
<dd>
<div class="block">参数校验统一使用Apache Common Lange Validate, 补充一些缺少的.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/MoreValidate.html#MoreValidate--">MoreValidate()</a></span> - 类 的构造器com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/MoreValidate.html" title="com.huazheng.tunny.tools.base中的类">MoreValidate</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/base/MoreValidateTest.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">MoreValidateTest</span></a> - <a href="../com/huazheng/tunny/tools/base/package-summary.html">com.huazheng.tunny.tools.base</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/MoreValidateTest.html#MoreValidateTest--">MoreValidateTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/MoreValidateTest.html" title="com.huazheng.tunny.tools.base中的类">MoreValidateTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#moveDir-java.io.File-java.io.File-">moveDir(File, File)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">目录移动/重命名</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#moveFile-java.io.File-java.io.File-">moveFile(File, File)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">文件移动/重命名.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtil.html#moveFile-java.nio.file.Path-java.nio.file.Path-">moveFile(Path, Path)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtil.html" title="com.huazheng.tunny.tools.io中的类">FileUtil</a></dt>
<dd>
<div class="block">文件移动/重命名.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/HashUtil.html#murmur128AsLong-byte:A-">murmur128AsLong(byte[])</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/HashUtil.html" title="com.huazheng.tunny.tools.text中的类">HashUtil</a></dt>
<dd>
<div class="block">对输入字符串进行murmur128散列, 返回值可能是负数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/HashUtil.html#murmur128AsLong-java.lang.String-">murmur128AsLong(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/HashUtil.html" title="com.huazheng.tunny.tools.text中的类">HashUtil</a></dt>
<dd>
<div class="block">对输入字符串进行murmur128散列, 返回值可能是负数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/HashUtil.html#murmur32AsInt-byte:A-">murmur32AsInt(byte[])</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/HashUtil.html" title="com.huazheng.tunny.tools.text中的类">HashUtil</a></dt>
<dd>
<div class="block">对输入字符串进行murmur32散列, 返回值可能是负数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/HashUtil.html#murmur32AsInt-java.lang.String-">murmur32AsInt(String)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/HashUtil.html" title="com.huazheng.tunny.tools.text中的类">HashUtil</a></dt>
<dd>
<div class="block">对输入字符串进行murmur32散列, 返回值可能是负数</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/HashUtil.html#MURMUR_SEED">MURMUR_SEED</a></span> - 类 中的静态变量com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/HashUtil.html" title="com.huazheng.tunny.tools.text中的类">HashUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/HashUtilTest.html#murmurhash--">murmurhash()</a></span> - 类 中的方法com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/HashUtilTest.html" title="com.huazheng.tunny.tools.text中的类">HashUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/MapUtilTest.MyBean.html#MyBean-java.lang.String-">MyBean(String)</a></span> - 类 的构造器com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/MapUtilTest.MyBean.html" title="com.huazheng.tunny.tools.collection中的类">MapUtilTest.MyBean</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/ThreadUtilTest.MyClass.html#MyClass--">MyClass()</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/ThreadUtilTest.MyClass.html" title="com.huazheng.tunny.tools.concurrent中的类">ThreadUtilTest.MyClass</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/BasicFutureTest.MyFuture.html#MyFuture--">MyFuture()</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/BasicFutureTest.MyFuture.html" title="com.huazheng.tunny.tools.concurrent中的类">BasicFutureTest.MyFuture</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-12.html">上一个字母</a></li>
<li><a href="index-14.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-13.html" target="_top">框架</a></li>
<li><a href="index-13.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
