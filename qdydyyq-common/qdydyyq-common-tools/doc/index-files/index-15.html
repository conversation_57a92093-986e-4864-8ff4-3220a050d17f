<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:36 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>O - 索引</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="O - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-14.html">上一个字母</a></li>
<li><a href="index-16.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-15.html" target="_top">框架</a></li>
<li><a href="index-15.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a name="I:O">
<!--   -->
</a>
<h2 class="title">O</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/mapper/XmlMapperTest.html#objectToXml--">objectToXml()</a></span> - 类 中的方法com.huazheng.tunny.tools.mapper.<a href="../com/huazheng/tunny/tools/mapper/XmlMapperTest.html" title="com.huazheng.tunny.tools.mapper中的类">XmlMapperTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/base/ObjectUtil.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">ObjectUtil</span></a> - <a href="../com/huazheng/tunny/tools/base/package-summary.html">com.huazheng.tunny.tools.base</a>中的类</dt>
<dd>
<div class="block">1.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/ObjectUtil.html#ObjectUtil--">ObjectUtil()</a></span> - 类 的构造器com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/ObjectUtil.html" title="com.huazheng.tunny.tools.base中的类">ObjectUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/base/ObjectUtilTest.html" title="com.huazheng.tunny.tools.base中的类"><span class="typeNameLink">ObjectUtilTest</span></a> - <a href="../com/huazheng/tunny/tools/base/package-summary.html">com.huazheng.tunny.tools.base</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/ObjectUtilTest.html#ObjectUtilTest--">ObjectUtilTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/ObjectUtilTest.html" title="com.huazheng.tunny.tools.base中的类">ObjectUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/type/Pair.html#of-L-R-">of(L, R)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.type.<a href="../com/huazheng/tunny/tools/base/type/Pair.html" title="com.huazheng.tunny.tools.base.type中的类">Pair</a></dt>
<dd>
<div class="block">根据等号左边的泛型，自动构造合适的Pair</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/type/Triple.html#of-L-M-R-">of(L, M, R)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.type.<a href="../com/huazheng/tunny/tools/base/type/Triple.html" title="com.huazheng.tunny.tools.base.type中的类">Triple</a></dt>
<dd>
<div class="block">根据等号左边的泛型，自动构造合适的Triple</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.ControllableQueue.html#offer-java.lang.Runnable-">offer(Runnable)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.ControllableQueue.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">QueuableCachedThreadPool.ControllableQueue</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/BasicFutureTest.MyFuture.html#onCancelled--">onCancelled()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/BasicFutureTest.MyFuture.html" title="com.huazheng.tunny.tools.concurrent中的类">BasicFutureTest.MyFuture</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html#onCancelled--">onCancelled()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.type.<a href="../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html" title="com.huazheng.tunny.tools.concurrent.type中的类">BasicFuture</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.PropertiesListener.html#onChange-java.lang.String-java.lang.String-">onChange(String, String)</a></span> - 类 中的方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtil.PropertiesListener.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtil.PropertiesListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtilTest.TestPropertiesListener.html#onChange-java.lang.String-java.lang.String-">onChange(String, String)</a></span> - 类 中的方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/SystemPropertiesUtilTest.TestPropertiesListener.html" title="com.huazheng.tunny.tools.base中的类">SystemPropertiesUtilTest.TestPropertiesListener</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/BasicFutureTest.MyFuture.html#onCompleted-T-">onCompleted(T)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/BasicFutureTest.MyFuture.html" title="com.huazheng.tunny.tools.concurrent中的类">BasicFutureTest.MyFuture</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html#onCompleted-T-">onCompleted(T)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.type.<a href="../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html" title="com.huazheng.tunny.tools.concurrent.type中的类">BasicFuture</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/BasicFutureTest.MyFuture.html#onFailed-java.lang.Exception-">onFailed(Exception)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.<a href="../com/huazheng/tunny/tools/concurrent/BasicFutureTest.MyFuture.html" title="com.huazheng.tunny.tools.concurrent中的类">BasicFutureTest.MyFuture</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html#onFailed-java.lang.Exception-">onFailed(Exception)</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.type.<a href="../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html" title="com.huazheng.tunny.tools.concurrent.type中的类">BasicFuture</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtilTest.html#opDir--">opDir()</a></span> - 类 中的方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtilTest.html" title="com.huazheng.tunny.tools.io中的类">FileUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/io/FileUtilTest.html#opFiles--">opFiles()</a></span> - 类 中的方法com.huazheng.tunny.tools.io.<a href="../com/huazheng/tunny/tools/io/FileUtilTest.html" title="com.huazheng.tunny.tools.io中的类">FileUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/BooleanUtil.html#or-boolean...-">or(boolean...)</a></span> - 类 中的静态方法com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/BooleanUtil.html" title="com.huazheng.tunny.tools.base中的类">BooleanUtil</a></dt>
<dd>
<div class="block">多个值的or</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/Platforms.html#OS_ARCH">OS_ARCH</a></span> - 类 中的静态变量com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/Platforms.html" title="com.huazheng.tunny.tools.base中的类">Platforms</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/Platforms.html#OS_NAME">OS_NAME</a></span> - 类 中的静态变量com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/Platforms.html" title="com.huazheng.tunny.tools.base中的类">Platforms</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/base/Platforms.html#OS_VERSION">OS_VERSION</a></span> - 类 中的静态变量com.huazheng.tunny.tools.base.<a href="../com/huazheng/tunny/tools/base/Platforms.html" title="com.huazheng.tunny.tools.base中的类">Platforms</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-14.html">上一个字母</a></li>
<li><a href="index-16.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-15.html" target="_top">框架</a></li>
<li><a href="index-15.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
