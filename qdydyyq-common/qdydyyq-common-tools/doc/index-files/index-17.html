<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:36 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Q - 索引</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript" src="../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Q - \u7D22\u5F15";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-16.html">上一个字母</a></li>
<li><a href="index-18.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-17.html" target="_top">框架</a></li>
<li><a href="index-17.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;<a name="I:Q">
<!--   -->
</a>
<h2 class="title">Q</h2>
<dl>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilderTest.html#quequablePool--">quequablePool()</a></span> - 类 中的方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilderTest.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilderTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.html#queuableCachedPool--">queuableCachedPool()</a></span> - 类 中的静态方法com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><span class="typeNameLink">QueuableCachedThreadPool</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/threadpool/package-summary.html">com.huazheng.tunny.tools.concurrent.threadpool</a>中的类</dt>
<dd>
<div class="block">From Tomcat 8.5.6, 传统的FixedThreadPool有Queue但线程数量不变，而CachedThreadPool线程数可变但没有Queue
 
 Tomcat的线程池，通过控制TaskQueue，线程数，但线程数到达最大时会进入Queue中.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.html#QueuableCachedThreadPool-int-int-long-java.util.concurrent.TimeUnit-com.huazheng.tunny.tools.concurrent.threadpool.QueuableCachedThreadPool.ControllableQueue-java.util.concurrent.ThreadFactory-java.util.concurrent.RejectedExecutionHandler-">QueuableCachedThreadPool(int, int, long, TimeUnit, QueuableCachedThreadPool.ControllableQueue, ThreadFactory, RejectedExecutionHandler)</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">QueuableCachedThreadPool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.ControllableQueue.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><span class="typeNameLink">QueuableCachedThreadPool.ControllableQueue</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/threadpool/package-summary.html">com.huazheng.tunny.tools.concurrent.threadpool</a>中的类</dt>
<dd>
<div class="block">https://github.com/apache/tomcat/blob/trunk/java/org/apache/tomcat/util/threads/TaskQueue.java</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.QueuableCachedThreadPoolBuilder.html#QueuableCachedThreadPoolBuilder--">QueuableCachedThreadPoolBuilder()</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.QueuableCachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.QueuableCachedThreadPoolBuilder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPoolTest.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><span class="typeNameLink">QueuableCachedThreadPoolTest</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/threadpool/package-summary.html">com.huazheng.tunny.tools.concurrent.threadpool</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPoolTest.html#QueuableCachedThreadPoolTest--">QueuableCachedThreadPoolTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.concurrent.threadpool.<a href="../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPoolTest.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">QueuableCachedThreadPoolTest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPoolTest.LongRunTask.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><span class="typeNameLink">QueuableCachedThreadPoolTest.LongRunTask</span></a> - <a href="../com/huazheng/tunny/tools/concurrent/threadpool/package-summary.html">com.huazheng.tunny.tools.concurrent.threadpool</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/collection/QueueUtil.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">QueueUtil</span></a> - <a href="../com/huazheng/tunny/tools/collection/package-summary.html">com.huazheng.tunny.tools.collection</a>中的类</dt>
<dd>
<div class="block">Queue工具集.</div>
</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/QueueUtil.html#QueueUtil--">QueueUtil()</a></span> - 类 的构造器com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/QueueUtil.html" title="com.huazheng.tunny.tools.collection中的类">QueueUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="../com/huazheng/tunny/tools/collection/QueueUtilTest.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">QueueUtilTest</span></a> - <a href="../com/huazheng/tunny/tools/collection/package-summary.html">com.huazheng.tunny.tools.collection</a>中的类</dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/collection/QueueUtilTest.html#QueueUtilTest--">QueueUtilTest()</a></span> - 类 的构造器com.huazheng.tunny.tools.collection.<a href="../com/huazheng/tunny/tools/collection/QueueUtilTest.html" title="com.huazheng.tunny.tools.collection中的类">QueueUtilTest</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="../com/huazheng/tunny/tools/text/CsvUtil.html#QUOTE">QUOTE</a></span> - 类 中的静态变量com.huazheng.tunny.tools.text.<a href="../com/huazheng/tunny/tools/text/CsvUtil.html" title="com.huazheng.tunny.tools.text中的类">CsvUtil</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="index-1.html">A</a>&nbsp;<a href="index-2.html">B</a>&nbsp;<a href="index-3.html">C</a>&nbsp;<a href="index-4.html">D</a>&nbsp;<a href="index-5.html">E</a>&nbsp;<a href="index-6.html">F</a>&nbsp;<a href="index-7.html">G</a>&nbsp;<a href="index-8.html">H</a>&nbsp;<a href="index-9.html">I</a>&nbsp;<a href="index-10.html">J</a>&nbsp;<a href="index-11.html">K</a>&nbsp;<a href="index-12.html">L</a>&nbsp;<a href="index-13.html">M</a>&nbsp;<a href="index-14.html">N</a>&nbsp;<a href="index-15.html">O</a>&nbsp;<a href="index-16.html">P</a>&nbsp;<a href="index-17.html">Q</a>&nbsp;<a href="index-18.html">R</a>&nbsp;<a href="index-19.html">S</a>&nbsp;<a href="index-20.html">T</a>&nbsp;<a href="index-21.html">U</a>&nbsp;<a href="index-22.html">V</a>&nbsp;<a href="index-23.html">W</a>&nbsp;<a href="index-24.html">X</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../overview-summary.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li><a href="../overview-tree.html">树</a></li>
<li><a href="../deprecated-list.html">已过时</a></li>
<li class="navBarCell1Rev">索引</li>
<li><a href="../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="index-16.html">上一个字母</a></li>
<li><a href="index-18.html">下一个字母</a></li>
</ul>
<ul class="navList">
<li><a href="../index.html?index-files/index-17.html" target="_top">框架</a></li>
<li><a href="index-17.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
