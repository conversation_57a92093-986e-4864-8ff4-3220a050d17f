<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:36 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>概览</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="\u6982\u89C8";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li class="navBarCell1Rev">概览</li>
<li>程序包</li>
<li>类</li>
<li><a href="overview-tree.html">树</a></li>
<li><a href="deprecated-list.html">已过时</a></li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="index.html?overview-summary.html" target="_top">框架</a></li>
<li><a href="overview-summary.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer">
<table class="overviewSummary" border="0" cellpadding="3" cellspacing="0" summary="程序包表, 列表程序包和解释">
<caption><span>程序包</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">程序包</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="com/huazheng/tunny/tools/base/package-summary.html">com.huazheng.tunny.tools.base</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/huazheng/tunny/tools/base/annotation/package-summary.html">com.huazheng.tunny.tools.base.annotation</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/huazheng/tunny/tools/base/type/package-summary.html">com.huazheng.tunny.tools.base.type</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/huazheng/tunny/tools/collection/package-summary.html">com.huazheng.tunny.tools.collection</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/huazheng/tunny/tools/collection/type/package-summary.html">com.huazheng.tunny.tools.collection.type</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/huazheng/tunny/tools/collection/type/primitive/package-summary.html">com.huazheng.tunny.tools.collection.type.primitive</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/huazheng/tunny/tools/concurrent/package-summary.html">com.huazheng.tunny.tools.concurrent</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/huazheng/tunny/tools/concurrent/jsr166e/package-summary.html">com.huazheng.tunny.tools.concurrent.jsr166e</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/huazheng/tunny/tools/concurrent/threadpool/package-summary.html">com.huazheng.tunny.tools.concurrent.threadpool</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/huazheng/tunny/tools/concurrent/type/package-summary.html">com.huazheng.tunny.tools.concurrent.type</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/huazheng/tunny/tools/io/package-summary.html">com.huazheng.tunny.tools.io</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/huazheng/tunny/tools/io/type/package-summary.html">com.huazheng.tunny.tools.io.type</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/huazheng/tunny/tools/mapper/package-summary.html">com.huazheng.tunny.tools.mapper</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/huazheng/tunny/tools/net/package-summary.html">com.huazheng.tunny.tools.net</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/huazheng/tunny/tools/number/package-summary.html">com.huazheng.tunny.tools.number</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/huazheng/tunny/tools/reflect/package-summary.html">com.huazheng.tunny.tools.reflect</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/huazheng/tunny/tools/security/package-summary.html">com.huazheng.tunny.tools.security</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/huazheng/tunny/tools/text/package-summary.html">com.huazheng.tunny.tools.text</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/huazheng/tunny/tools/time/package-summary.html">com.huazheng.tunny.tools.time</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/vip/vjtools/test/data/package-summary.html">com.vip.vjtools.test.data</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="com/vip/vjtools/test/log/package-summary.html">com.vip.vjtools.test.log</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="com/vip/vjtools/test/rule/package-summary.html">com.vip.vjtools.test.rule</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li class="navBarCell1Rev">概览</li>
<li>程序包</li>
<li>类</li>
<li><a href="overview-tree.html">树</a></li>
<li><a href="deprecated-list.html">已过时</a></li>
<li><a href="index-files/index-1.html">索引</a></li>
<li><a href="help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个</li>
<li>下一个</li>
</ul>
<ul class="navList">
<li><a href="index.html?overview-summary.html" target="_top">框架</a></li>
<li><a href="overview-summary.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
