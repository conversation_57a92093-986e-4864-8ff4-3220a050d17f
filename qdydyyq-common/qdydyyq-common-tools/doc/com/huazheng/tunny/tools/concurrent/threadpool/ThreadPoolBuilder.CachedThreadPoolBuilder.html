<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:34 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ThreadPoolBuilder.CachedThreadPoolBuilder</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ThreadPoolBuilder.CachedThreadPoolBuilder";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.FixedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" target="_top">框架</a></li>
<li><a href="ThreadPoolBuilder.CachedThreadPoolBuilder.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.concurrent.threadpool</div>
<h2 title="类 ThreadPoolBuilder.CachedThreadPoolBuilder" class="title">类 ThreadPoolBuilder.CachedThreadPoolBuilder</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.concurrent.threadpool.ThreadPoolBuilder.CachedThreadPoolBuilder</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>封闭类:</dt>
<dd><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">ThreadPoolBuilder.CachedThreadPoolBuilder</span>
extends java.lang.Object</pre>
<div class="block">创建CachedThreadPool, maxSize建议设置
 
 1. 任务提交时, 如果线程数还没达到minSize即创建新线程并绑定任务(即minSize次提交后线程总数必达到minSize, 不会重用之前的线程)
 
 minSize默认为0, 可设置保证有基本的线程处理请求不被回收.
 
 2. 第minSize次任务提交后, 新增任务提交进SynchronousQueue后，如果没有空闲线程立刻处理，则会创建新的线程, 直到总线程数达到上限.
 
 maxSize默认为Integer.Max, 可以进行设置.
 
 如果设置了maxSize, 当总线程数达到上限, 会调用RejectHandler进行处理, 默认为AbortPolicy, 抛出RejectedExecutionException异常.
 其他可选的Policy包括静默放弃当前任务(Discard)，或由主线程来直接执行(CallerRuns).
 
 3. minSize以上, maxSize以下的线程, 如果在keepAliveTime中都poll不到任务执行将会被结束掉, keeAliveTimeJDK默认为10秒.
 JDK默认值60秒太高，如高达1000线程时，要低于16QPS时才会开始回收线程, 因此改为默认10秒.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html#CachedThreadPoolBuilder--">CachedThreadPoolBuilder</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.util.concurrent.ThreadPoolExecutor</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html#build--">build</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.CachedThreadPoolBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html#setDaemon-java.lang.Boolean-">setDaemon</a></span>(java.lang.Boolean&nbsp;daemon)</code>
<div class="block">与threadFactory互斥, 优先使用ThreadFactory
 
 默认为NULL，不进行设置，使用JDK的默认值.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.CachedThreadPoolBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html#setKeepAliveSecs-int-">setKeepAliveSecs</a></span>(int&nbsp;keepAliveSecs)</code>
<div class="block">JDK默认值60秒太高，如高达1000线程时，要低于16QPS时才会开始回收线程, 因此改为默认10秒.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.CachedThreadPoolBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html#setMaxSize-int-">setMaxSize</a></span>(int&nbsp;maxSize)</code>
<div class="block">Max默认Integer.MAX_VALUE的，建议设置</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.CachedThreadPoolBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html#setMinSize-int-">setMinSize</a></span>(int&nbsp;minSize)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.CachedThreadPoolBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html#setRejectHanlder-java.util.concurrent.RejectedExecutionHandler-">setRejectHanlder</a></span>(java.util.concurrent.RejectedExecutionHandler&nbsp;rejectHandler)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.CachedThreadPoolBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html#setThreadFactory-java.util.concurrent.ThreadFactory-">setThreadFactory</a></span>(java.util.concurrent.ThreadFactory&nbsp;threadFactory)</code>
<div class="block">与threadNamePrefix互斥, 优先使用ThreadFactory</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.CachedThreadPoolBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html#setThreadNamePrefix-java.lang.String-">setThreadNamePrefix</a></span>(java.lang.String&nbsp;threadNamePrefix)</code>
<div class="block">与threadFactory互斥, 优先使用ThreadFactory</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="CachedThreadPoolBuilder--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CachedThreadPoolBuilder</h4>
<pre>public&nbsp;CachedThreadPoolBuilder()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="setMinSize-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMinSize</h4>
<pre>public&nbsp;<a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.CachedThreadPoolBuilder</a>&nbsp;setMinSize(int&nbsp;minSize)</pre>
</li>
</ul>
<a name="setMaxSize-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaxSize</h4>
<pre>public&nbsp;<a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.CachedThreadPoolBuilder</a>&nbsp;setMaxSize(int&nbsp;maxSize)</pre>
<div class="block">Max默认Integer.MAX_VALUE的，建议设置</div>
</li>
</ul>
<a name="setKeepAliveSecs-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setKeepAliveSecs</h4>
<pre>public&nbsp;<a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.CachedThreadPoolBuilder</a>&nbsp;setKeepAliveSecs(int&nbsp;keepAliveSecs)</pre>
<div class="block">JDK默认值60秒太高，如高达1000线程时，要低于16QPS时才会开始回收线程, 因此改为默认10秒.</div>
</li>
</ul>
<a name="setThreadFactory-java.util.concurrent.ThreadFactory-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setThreadFactory</h4>
<pre>public&nbsp;<a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.CachedThreadPoolBuilder</a>&nbsp;setThreadFactory(java.util.concurrent.ThreadFactory&nbsp;threadFactory)</pre>
<div class="block">与threadNamePrefix互斥, 优先使用ThreadFactory</div>
</li>
</ul>
<a name="setThreadNamePrefix-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setThreadNamePrefix</h4>
<pre>public&nbsp;<a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.CachedThreadPoolBuilder</a>&nbsp;setThreadNamePrefix(java.lang.String&nbsp;threadNamePrefix)</pre>
<div class="block">与threadFactory互斥, 优先使用ThreadFactory</div>
</li>
</ul>
<a name="setDaemon-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDaemon</h4>
<pre>public&nbsp;<a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.CachedThreadPoolBuilder</a>&nbsp;setDaemon(java.lang.Boolean&nbsp;daemon)</pre>
<div class="block">与threadFactory互斥, 优先使用ThreadFactory
 
 默认为NULL，不进行设置，使用JDK的默认值.</div>
</li>
</ul>
<a name="setRejectHanlder-java.util.concurrent.RejectedExecutionHandler-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRejectHanlder</h4>
<pre>public&nbsp;<a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.CachedThreadPoolBuilder</a>&nbsp;setRejectHanlder(java.util.concurrent.RejectedExecutionHandler&nbsp;rejectHandler)</pre>
</li>
</ul>
<a name="build--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>build</h4>
<pre>public&nbsp;java.util.concurrent.ThreadPoolExecutor&nbsp;build()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.FixedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" target="_top">框架</a></li>
<li><a href="ThreadPoolBuilder.CachedThreadPoolBuilder.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
