<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:32 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>LongAdder</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="LongAdder";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/Striped64.html" title="com.huazheng.tunny.tools.concurrent.jsr166e中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html" target="_top">框架</a></li>
<li><a href="LongAdder.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.concurrent.jsr166e</div>
<h2 title="类 LongAdder" class="title">类 LongAdder</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.lang.Number</li>
<li>
<ul class="inheritance">
<li><a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/Striped64.html" title="com.huazheng.tunny.tools.concurrent.jsr166e中的类">com.huazheng.tunny.tools.concurrent.jsr166e.Striped64</a></li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.concurrent.jsr166e.LongAdder</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>java.io.Serializable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">LongAdder</span>
extends <a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/Striped64.html" title="com.huazheng.tunny.tools.concurrent.jsr166e中的类">Striped64</a>
implements java.io.Serializable</pre>
<div class="block">移植
 http://gee.cs.oswego.edu/cgi-bin/viewcvs.cgi/jsr166/src/jsr166e/LongAdder.java Revision.1.17
 
 One or more variables that together maintain an initially zero
 <code>long</code> sum.  When updates (method <a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#add-long-"><code>add(long)</code></a>) are contended
 across threads, the set of variables may grow dynamically to reduce
 contention. Method <a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#sum--"><code>sum()</code></a> (or, equivalently, <a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#longValue--"><code>longValue()</code></a>) returns the current total combined across the
 variables maintaining the sum.

 <p>This class is usually preferable to <code>AtomicLong</code> when
 multiple threads update a common sum that is used for purposes such
 as collecting statistics, not for fine-grained synchronization
 control.  Under low update contention, the two classes have similar
 characteristics. But under high contention, expected throughput of
 this class is significantly higher, at the expense of higher space
 consumption.

 <p>This class extends <code>Number</code>, but does <em>not</em> define
 methods such as <code>equals</code>, <code>hashCode</code> and <code>compareTo</code> because instances are expected to be mutated, and so are
 not useful as collection keys.

 <p><em>jsr166e note: This class is targeted to be placed in
 java.util.concurrent.atomic.</em></div>
<dl>
<dt><span class="simpleTagLabel">从以下版本开始:</span></dt>
<dd>1.8</dd>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../../serialized-form.html#com.huazheng.tunny.tools.concurrent.jsr166e.LongAdder">序列化表格</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#LongAdder--">LongAdder</a></span>()</code>
<div class="block">Creates a new adder with initial sum of zero.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#add-long-">add</a></span>(long&nbsp;x)</code>
<div class="block">Adds the given value.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#decrement--">decrement</a></span>()</code>
<div class="block">Equivalent to <code>add(-1)</code>.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#doubleValue--">doubleValue</a></span>()</code>
<div class="block">Returns the <a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#sum--"><code>sum()</code></a> as a <code>double</code> after a widening
 primitive conversion.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#floatValue--">floatValue</a></span>()</code>
<div class="block">Returns the <a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#sum--"><code>sum()</code></a> as a <code>float</code>
 after a widening primitive conversion.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#increment--">increment</a></span>()</code>
<div class="block">Equivalent to <code>add(1)</code>.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#intValue--">intValue</a></span>()</code>
<div class="block">Returns the <a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#sum--"><code>sum()</code></a> as an <code>int</code> after a narrowing
 primitive conversion.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#longValue--">longValue</a></span>()</code>
<div class="block">Equivalent to <a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#sum--"><code>sum()</code></a>.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#reset--">reset</a></span>()</code>
<div class="block">Resets variables maintaining the sum to zero.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#sum--">sum</a></span>()</code>
<div class="block">Returns the current sum.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#sumThenReset--">sumThenReset</a></span>()</code>
<div class="block">Equivalent in effect to <a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#sum--"><code>sum()</code></a> followed by <a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#reset--"><code>reset()</code></a>.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#toString--">toString</a></span>()</code>
<div class="block">Returns the String representation of the <a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#sum--"><code>sum()</code></a>.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Number">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Number</h3>
<code>byteValue, shortValue</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="LongAdder--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>LongAdder</h4>
<pre>public&nbsp;LongAdder()</pre>
<div class="block">Creates a new adder with initial sum of zero.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="add-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>add</h4>
<pre>public&nbsp;void&nbsp;add(long&nbsp;x)</pre>
<div class="block">Adds the given value.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>x</code> - the value to add</dd>
</dl>
</li>
</ul>
<a name="increment--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>increment</h4>
<pre>public&nbsp;void&nbsp;increment()</pre>
<div class="block">Equivalent to <code>add(1)</code>.</div>
</li>
</ul>
<a name="decrement--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>decrement</h4>
<pre>public&nbsp;void&nbsp;decrement()</pre>
<div class="block">Equivalent to <code>add(-1)</code>.</div>
</li>
</ul>
<a name="sum--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sum</h4>
<pre>public&nbsp;long&nbsp;sum()</pre>
<div class="block">Returns the current sum.  The returned value is <em>NOT</em> an
 atomic snapshot; invocation in the absence of concurrent
 updates returns an accurate result, but concurrent updates that
 occur while the sum is being calculated might not be
 incorporated.</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>the sum</dd>
</dl>
</li>
</ul>
<a name="reset--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>reset</h4>
<pre>public&nbsp;void&nbsp;reset()</pre>
<div class="block">Resets variables maintaining the sum to zero.  This method may
 be a useful alternative to creating a new adder, but is only
 effective if there are no concurrent updates.  Because this
 method is intrinsically racy, it should only be used when it is
 known that no threads are concurrently updating.</div>
</li>
</ul>
<a name="sumThenReset--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sumThenReset</h4>
<pre>public&nbsp;long&nbsp;sumThenReset()</pre>
<div class="block">Equivalent in effect to <a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#sum--"><code>sum()</code></a> followed by <a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#reset--"><code>reset()</code></a>. This method may apply for example during quiescent
 points between multithreaded computations.  If there are
 updates concurrent with this method, the returned value is
 <em>not</em> guaranteed to be the final value occurring before
 the reset.</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd>the sum</dd>
</dl>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;java.lang.String&nbsp;toString()</pre>
<div class="block">Returns the String representation of the <a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#sum--"><code>sum()</code></a>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>toString</code>&nbsp;在类中&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>the String representation of the <a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#sum--"><code>sum()</code></a></dd>
</dl>
</li>
</ul>
<a name="longValue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>longValue</h4>
<pre>public&nbsp;long&nbsp;longValue()</pre>
<div class="block">Equivalent to <a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#sum--"><code>sum()</code></a>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>longValue</code>&nbsp;在类中&nbsp;<code>java.lang.Number</code></dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>the sum</dd>
</dl>
</li>
</ul>
<a name="intValue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>intValue</h4>
<pre>public&nbsp;int&nbsp;intValue()</pre>
<div class="block">Returns the <a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#sum--"><code>sum()</code></a> as an <code>int</code> after a narrowing
 primitive conversion.</div>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>intValue</code>&nbsp;在类中&nbsp;<code>java.lang.Number</code></dd>
</dl>
</li>
</ul>
<a name="floatValue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>floatValue</h4>
<pre>public&nbsp;float&nbsp;floatValue()</pre>
<div class="block">Returns the <a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#sum--"><code>sum()</code></a> as a <code>float</code>
 after a widening primitive conversion.</div>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>floatValue</code>&nbsp;在类中&nbsp;<code>java.lang.Number</code></dd>
</dl>
</li>
</ul>
<a name="doubleValue--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>doubleValue</h4>
<pre>public&nbsp;double&nbsp;doubleValue()</pre>
<div class="block">Returns the <a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html#sum--"><code>sum()</code></a> as a <code>double</code> after a widening
 primitive conversion.</div>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>doubleValue</code>&nbsp;在类中&nbsp;<code>java.lang.Number</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/Striped64.html" title="com.huazheng.tunny.tools.concurrent.jsr166e中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html" target="_top">框架</a></li>
<li><a href="LongAdder.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
