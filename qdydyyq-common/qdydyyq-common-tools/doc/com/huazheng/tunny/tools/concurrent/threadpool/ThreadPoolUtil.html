<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:34 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ThreadPoolUtil</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ThreadPoolUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilderTest.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtilTest.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtil.html" target="_top">框架</a></li>
<li><a href="ThreadPoolUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.concurrent.threadpool</div>
<h2 title="类 ThreadPoolUtil" class="title">类 ThreadPoolUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.concurrent.threadpool.ThreadPoolUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ThreadPoolUtil</span>
extends java.lang.Object</pre>
<div class="block">线程池工具集
 
 1. 优雅关闭线程池的(via guava)
 
 2. 创建可自定义线程名的ThreadFactory(via guava)
 
 3. 防止第三方Runnable未捕捉异常导致线程跑飞</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtil.html#ThreadPoolUtil--">ThreadPoolUtil</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static java.util.concurrent.ThreadFactory</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtil.html#buildThreadFactory-java.lang.String-">buildThreadFactory</a></span>(java.lang.String&nbsp;threadNamePrefix)</code>
<div class="block">创建ThreadFactory，使得创建的线程有自己的名字而不是默认的"pool-x-thread-y"
 
 使用了Guava的工具类</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static java.util.concurrent.ThreadFactory</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtil.html#buildThreadFactory-java.lang.String-boolean-">buildThreadFactory</a></span>(java.lang.String&nbsp;threadNamePrefix,
                  boolean&nbsp;daemon)</code>
<div class="block">可设定是否daemon, daemon线程在主线程已执行完毕时, 不会阻塞应用不退出, 而非daemon线程则会阻塞.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtil.html#gracefulShutdown-java.util.concurrent.ExecutorService-int-">gracefulShutdown</a></span>(java.util.concurrent.ExecutorService&nbsp;threadPool,
                int&nbsp;shutdownTimeoutMills)</code>
<div class="block">按照ExecutorService JavaDoc示例代码编写的Graceful Shutdown方法.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtil.html#gracefulShutdown-java.util.concurrent.ExecutorService-int-java.util.concurrent.TimeUnit-">gracefulShutdown</a></span>(java.util.concurrent.ExecutorService&nbsp;threadPool,
                int&nbsp;shutdownTimeout,
                java.util.concurrent.TimeUnit&nbsp;timeUnit)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static java.lang.Runnable</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtil.html#safeRunnable-java.lang.Runnable-">safeRunnable</a></span>(java.lang.Runnable&nbsp;runnable)</code>
<div class="block">防止用户没有捕捉异常导致中断了线程池中的线程, 使得SchedulerService无法继续执行.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="ThreadPoolUtil--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ThreadPoolUtil</h4>
<pre>public&nbsp;ThreadPoolUtil()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="gracefulShutdown-java.util.concurrent.ExecutorService-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>gracefulShutdown</h4>
<pre>public static&nbsp;boolean&nbsp;gracefulShutdown(java.util.concurrent.ExecutorService&nbsp;threadPool,
                                       int&nbsp;shutdownTimeoutMills)</pre>
<div class="block">按照ExecutorService JavaDoc示例代码编写的Graceful Shutdown方法.
 
 先使用shutdown, 停止接收新任务并尝试完成所有已存在任务.
 
 如果1/2超时时间后, 则调用shutdownNow,取消在workQueue中Pending的任务,并中断所有阻塞函数.
 
 如果1/2超时仍然超時，則強制退出.
 
 另对在shutdown时线程本身被调用中断做了处理.
 
 返回线程最后是否被中断.
 
 使用了Guava的工具类</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>MoreExecutors.shutdownAndAwaitTermination(ExecutorService, long, TimeUnit)</code></dd>
</dl>
</li>
</ul>
<a name="gracefulShutdown-java.util.concurrent.ExecutorService-int-java.util.concurrent.TimeUnit-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>gracefulShutdown</h4>
<pre>public static&nbsp;boolean&nbsp;gracefulShutdown(java.util.concurrent.ExecutorService&nbsp;threadPool,
                                       int&nbsp;shutdownTimeout,
                                       java.util.concurrent.TimeUnit&nbsp;timeUnit)</pre>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>gracefulShutdown</code></dd>
</dl>
</li>
</ul>
<a name="buildThreadFactory-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>buildThreadFactory</h4>
<pre>public static&nbsp;java.util.concurrent.ThreadFactory&nbsp;buildThreadFactory(java.lang.String&nbsp;threadNamePrefix)</pre>
<div class="block">创建ThreadFactory，使得创建的线程有自己的名字而不是默认的"pool-x-thread-y"
 
 使用了Guava的工具类</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>ThreadFactoryBuilder.build()</code></dd>
</dl>
</li>
</ul>
<a name="buildThreadFactory-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>buildThreadFactory</h4>
<pre>public static&nbsp;java.util.concurrent.ThreadFactory&nbsp;buildThreadFactory(java.lang.String&nbsp;threadNamePrefix,
                                                                    boolean&nbsp;daemon)</pre>
<div class="block">可设定是否daemon, daemon线程在主线程已执行完毕时, 不会阻塞应用不退出, 而非daemon线程则会阻塞.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>buildThreadFactory</code></dd>
</dl>
</li>
</ul>
<a name="safeRunnable-java.lang.Runnable-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>safeRunnable</h4>
<pre>public static&nbsp;java.lang.Runnable&nbsp;safeRunnable(java.lang.Runnable&nbsp;runnable)</pre>
<div class="block">防止用户没有捕捉异常导致中断了线程池中的线程, 使得SchedulerService无法继续执行.
 
 在无法控制第三方包的Runnable实现时，调用本函数进行包裹.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilderTest.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtilTest.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtil.html" target="_top">框架</a></li>
<li><a href="ThreadPoolUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
