<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:33 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>BasicFuture</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BasicFuture";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":6,"i8":6,"i9":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../../com/huazheng/tunny/tools/concurrent/type/ThreadLocalContext.html" title="com.huazheng.tunny.tools.concurrent.type中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/huazheng/tunny/tools/concurrent/type/BasicFuture.html" target="_top">框架</a></li>
<li><a href="BasicFuture.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.concurrent.type</div>
<h2 title="类 BasicFuture" class="title">类 BasicFuture&lt;T&gt;</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.concurrent.type.BasicFuture&lt;T&gt;</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>java.util.concurrent.Future&lt;T&gt;</dd>
</dl>
<dl>
<dt>直接已知子类:</dt>
<dd><a href="../../../../../../com/huazheng/tunny/tools/concurrent/BasicFutureTest.MyFuture.html" title="com.huazheng.tunny.tools.concurrent中的类">BasicFutureTest.MyFuture</a></dd>
</dl>
<hr>
<br>
<pre>public abstract class <span class="typeNameLabel">BasicFuture&lt;T&gt;</span>
extends java.lang.Object
implements java.util.concurrent.Future&lt;T&gt;</pre>
<div class="block">从Apache HttpClient 移植(2017.4)，一个Future实现类的基本框架.
 
 https://github.com/apache/httpcomponents-core/blob/master/httpcore5/src/main/java/org/apache/hc/core5/concurrent/BasicFuture.java
 
 不过HC用的是callback，这里用的是继承</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html#BasicFuture--">BasicFuture</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html#cancel-boolean-">cancel</a></span>(boolean&nbsp;mayInterruptIfRunning)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html#completed-T-">completed</a></span>(<a href="../../../../../../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html" title="BasicFuture中的类型参数">T</a>&nbsp;result)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html#failed-java.lang.Exception-">failed</a></span>(java.lang.Exception&nbsp;exception)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html" title="BasicFuture中的类型参数">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html#get--">get</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html" title="BasicFuture中的类型参数">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html#get-long-java.util.concurrent.TimeUnit-">get</a></span>(long&nbsp;timeout,
   java.util.concurrent.TimeUnit&nbsp;unit)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html#isCancelled--">isCancelled</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html#isDone--">isDone</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>protected abstract void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html#onCancelled--">onCancelled</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>protected abstract void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html#onCompleted-T-">onCompleted</a></span>(<a href="../../../../../../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html" title="BasicFuture中的类型参数">T</a>&nbsp;result)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>protected abstract void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html#onFailed-java.lang.Exception-">onFailed</a></span>(java.lang.Exception&nbsp;ex)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="BasicFuture--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>BasicFuture</h4>
<pre>public&nbsp;BasicFuture()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="isCancelled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isCancelled</h4>
<pre>public&nbsp;boolean&nbsp;isCancelled()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>isCancelled</code>&nbsp;在接口中&nbsp;<code>java.util.concurrent.Future&lt;<a href="../../../../../../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html" title="BasicFuture中的类型参数">T</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="isDone--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDone</h4>
<pre>public&nbsp;boolean&nbsp;isDone()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>isDone</code>&nbsp;在接口中&nbsp;<code>java.util.concurrent.Future&lt;<a href="../../../../../../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html" title="BasicFuture中的类型参数">T</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="get--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get</h4>
<pre>public&nbsp;<a href="../../../../../../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html" title="BasicFuture中的类型参数">T</a>&nbsp;get()
      throws java.lang.InterruptedException,
             java.util.concurrent.ExecutionException</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>get</code>&nbsp;在接口中&nbsp;<code>java.util.concurrent.Future&lt;<a href="../../../../../../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html" title="BasicFuture中的类型参数">T</a>&gt;</code></dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.lang.InterruptedException</code></dd>
<dd><code>java.util.concurrent.ExecutionException</code></dd>
</dl>
</li>
</ul>
<a name="get-long-java.util.concurrent.TimeUnit-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get</h4>
<pre>public&nbsp;<a href="../../../../../../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html" title="BasicFuture中的类型参数">T</a>&nbsp;get(long&nbsp;timeout,
             java.util.concurrent.TimeUnit&nbsp;unit)
      throws java.lang.InterruptedException,
             java.util.concurrent.ExecutionException,
             java.util.concurrent.TimeoutException</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>get</code>&nbsp;在接口中&nbsp;<code>java.util.concurrent.Future&lt;<a href="../../../../../../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html" title="BasicFuture中的类型参数">T</a>&gt;</code></dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.lang.InterruptedException</code></dd>
<dd><code>java.util.concurrent.ExecutionException</code></dd>
<dd><code>java.util.concurrent.TimeoutException</code></dd>
</dl>
</li>
</ul>
<a name="completed-java.lang.Object-">
<!--   -->
</a><a name="completed-T-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>completed</h4>
<pre>public&nbsp;boolean&nbsp;completed(<a href="../../../../../../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html" title="BasicFuture中的类型参数">T</a>&nbsp;result)</pre>
</li>
</ul>
<a name="failed-java.lang.Exception-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>failed</h4>
<pre>public&nbsp;boolean&nbsp;failed(java.lang.Exception&nbsp;exception)</pre>
</li>
</ul>
<a name="cancel-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cancel</h4>
<pre>public&nbsp;boolean&nbsp;cancel(boolean&nbsp;mayInterruptIfRunning)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>cancel</code>&nbsp;在接口中&nbsp;<code>java.util.concurrent.Future&lt;<a href="../../../../../../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html" title="BasicFuture中的类型参数">T</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="onCompleted-java.lang.Object-">
<!--   -->
</a><a name="onCompleted-T-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onCompleted</h4>
<pre>protected abstract&nbsp;void&nbsp;onCompleted(<a href="../../../../../../com/huazheng/tunny/tools/concurrent/type/BasicFuture.html" title="BasicFuture中的类型参数">T</a>&nbsp;result)</pre>
</li>
</ul>
<a name="onFailed-java.lang.Exception-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onFailed</h4>
<pre>protected abstract&nbsp;void&nbsp;onFailed(java.lang.Exception&nbsp;ex)</pre>
</li>
</ul>
<a name="onCancelled--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>onCancelled</h4>
<pre>protected abstract&nbsp;void&nbsp;onCancelled()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../../com/huazheng/tunny/tools/concurrent/type/ThreadLocalContext.html" title="com.huazheng.tunny.tools.concurrent.type中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/huazheng/tunny/tools/concurrent/type/BasicFuture.html" target="_top">框架</a></li>
<li><a href="BasicFuture.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
