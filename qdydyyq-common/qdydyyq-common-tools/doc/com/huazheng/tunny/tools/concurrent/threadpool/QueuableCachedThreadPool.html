<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:34 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>QueuableCachedThreadPool</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="QueuableCachedThreadPool";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/AbortPolicyWithReportTest.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.ControllableQueue.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.html" target="_top">框架</a></li>
<li><a href="QueuableCachedThreadPool.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.concurrent.threadpool</div>
<h2 title="类 QueuableCachedThreadPool" class="title">类 QueuableCachedThreadPool</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.util.concurrent.AbstractExecutorService</li>
<li>
<ul class="inheritance">
<li>java.util.concurrent.ThreadPoolExecutor</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.concurrent.threadpool.QueuableCachedThreadPool</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>java.util.concurrent.Executor, java.util.concurrent.ExecutorService</dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">QueuableCachedThreadPool</span>
extends java.util.concurrent.ThreadPoolExecutor</pre>
<div class="block">From Tomcat 8.5.6, 传统的FixedThreadPool有Queue但线程数量不变，而CachedThreadPool线程数可变但没有Queue
 
 Tomcat的线程池，通过控制TaskQueue，线程数，但线程数到达最大时会进入Queue中.
 
 代码从Tomcat复制，主要修改包括：
 
 1. 删除定期重启线程避免内存泄漏的功能，
 
 2. TaskQueue中可能3次有锁的读取线程数量，改为只读取1次，这把锁也是这个实现里的唯一遗憾了。
 
 https://github.com/apache/tomcat/blob/trunk/java/org/apache/tomcat/util/threads/ThreadPoolExecutor.java</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>嵌套类概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="嵌套类概要表, 列表嵌套类和解释">
<caption><span>嵌套类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">类和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.ControllableQueue.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">QueuableCachedThreadPool.ControllableQueue</a></span></code>
<div class="block">https://github.com/apache/tomcat/blob/trunk/java/org/apache/tomcat/util/threads/TaskQueue.java</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.java.util.concurrent.ThreadPoolExecutor">
<!--   -->
</a>
<h3>从类继承的嵌套类/接口&nbsp;java.util.concurrent.ThreadPoolExecutor</h3>
<code>java.util.concurrent.ThreadPoolExecutor.AbortPolicy, java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy, java.util.concurrent.ThreadPoolExecutor.DiscardOldestPolicy, java.util.concurrent.ThreadPoolExecutor.DiscardPolicy</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.html#QueuableCachedThreadPool-int-int-long-java.util.concurrent.TimeUnit-com.huazheng.tunny.tools.concurrent.threadpool.QueuableCachedThreadPool.ControllableQueue-java.util.concurrent.ThreadFactory-java.util.concurrent.RejectedExecutionHandler-">QueuableCachedThreadPool</a></span>(int&nbsp;corePoolSize,
                        int&nbsp;maximumPoolSize,
                        long&nbsp;keepAliveTime,
                        java.util.concurrent.TimeUnit&nbsp;unit,
                        <a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.ControllableQueue.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">QueuableCachedThreadPool.ControllableQueue</a>&nbsp;workQueue,
                        java.util.concurrent.ThreadFactory&nbsp;threadFactory,
                        java.util.concurrent.RejectedExecutionHandler&nbsp;handler)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.html#afterExecute-java.lang.Runnable-java.lang.Throwable-">afterExecute</a></span>(java.lang.Runnable&nbsp;r,
            java.lang.Throwable&nbsp;t)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.html#execute-java.lang.Runnable-">execute</a></span>(java.lang.Runnable&nbsp;command)</code></td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.html#execute-java.lang.Runnable-long-java.util.concurrent.TimeUnit-">execute</a></span>(java.lang.Runnable&nbsp;command,
       long&nbsp;timeout,
       java.util.concurrent.TimeUnit&nbsp;unit)</code>
<div class="block">Executes the given command at some time in the future.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.html#getSubmittedCount--">getSubmittedCount</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.concurrent.ThreadPoolExecutor">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.util.concurrent.ThreadPoolExecutor</h3>
<code>allowCoreThreadTimeOut, allowsCoreThreadTimeOut, awaitTermination, beforeExecute, finalize, getActiveCount, getCompletedTaskCount, getCorePoolSize, getKeepAliveTime, getLargestPoolSize, getMaximumPoolSize, getPoolSize, getQueue, getRejectedExecutionHandler, getTaskCount, getThreadFactory, isShutdown, isTerminated, isTerminating, prestartAllCoreThreads, prestartCoreThread, purge, remove, setCorePoolSize, setKeepAliveTime, setMaximumPoolSize, setRejectedExecutionHandler, setThreadFactory, shutdown, shutdownNow, terminated, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.concurrent.AbstractExecutorService">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.util.concurrent.AbstractExecutorService</h3>
<code>invokeAll, invokeAll, invokeAny, invokeAny, newTaskFor, newTaskFor, submit, submit, submit</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="QueuableCachedThreadPool-int-int-long-java.util.concurrent.TimeUnit-com.huazheng.tunny.tools.concurrent.threadpool.QueuableCachedThreadPool.ControllableQueue-java.util.concurrent.ThreadFactory-java.util.concurrent.RejectedExecutionHandler-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>QueuableCachedThreadPool</h4>
<pre>public&nbsp;QueuableCachedThreadPool(int&nbsp;corePoolSize,
                                int&nbsp;maximumPoolSize,
                                long&nbsp;keepAliveTime,
                                java.util.concurrent.TimeUnit&nbsp;unit,
                                <a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.ControllableQueue.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">QueuableCachedThreadPool.ControllableQueue</a>&nbsp;workQueue,
                                java.util.concurrent.ThreadFactory&nbsp;threadFactory,
                                java.util.concurrent.RejectedExecutionHandler&nbsp;handler)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="afterExecute-java.lang.Runnable-java.lang.Throwable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>afterExecute</h4>
<pre>protected&nbsp;void&nbsp;afterExecute(java.lang.Runnable&nbsp;r,
                            java.lang.Throwable&nbsp;t)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>afterExecute</code>&nbsp;在类中&nbsp;<code>java.util.concurrent.ThreadPoolExecutor</code></dd>
</dl>
</li>
</ul>
<a name="getSubmittedCount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSubmittedCount</h4>
<pre>public&nbsp;int&nbsp;getSubmittedCount()</pre>
</li>
</ul>
<a name="execute-java.lang.Runnable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>execute</h4>
<pre>public&nbsp;void&nbsp;execute(java.lang.Runnable&nbsp;command)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>execute</code>&nbsp;在接口中&nbsp;<code>java.util.concurrent.Executor</code></dd>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>execute</code>&nbsp;在类中&nbsp;<code>java.util.concurrent.ThreadPoolExecutor</code></dd>
</dl>
</li>
</ul>
<a name="execute-java.lang.Runnable-long-java.util.concurrent.TimeUnit-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>execute</h4>
<pre>public&nbsp;void&nbsp;execute(java.lang.Runnable&nbsp;command,
                    long&nbsp;timeout,
                    java.util.concurrent.TimeUnit&nbsp;unit)</pre>
<div class="block">Executes the given command at some time in the future. The command may execute in a new thread, in a pooled
 thread, or in the calling thread, at the discretion of the <tt>Executor</tt> implementation. If no threads are
 available, it will be added to the work queue. If the work queue is full, the system will wait for the specified
 time and it throw a RejectedExecutionException if the queue is still full after that.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>command</code> - the runnable task</dd>
<dd><code>timeout</code> - A timeout for the completion of the task</dd>
<dd><code>unit</code> - The timeout time unit</dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.util.concurrent.RejectedExecutionException</code> - if this task cannot be accepted for execution - the queue is full</dd>
<dd><code>java.lang.NullPointerException</code> - if command or unit is null</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/AbortPolicyWithReportTest.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.ControllableQueue.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.html" target="_top">框架</a></li>
<li><a href="QueuableCachedThreadPool.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
