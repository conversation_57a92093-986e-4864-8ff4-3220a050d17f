<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:32 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ThreadDumpper</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ThreadDumpper";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/concurrent/SamplerTest.html" title="com.huazheng.tunny.tools.concurrent中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/concurrent/ThreadDumpperTest.html" title="com.huazheng.tunny.tools.concurrent中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/concurrent/ThreadDumpper.html" target="_top">框架</a></li>
<li><a href="ThreadDumpper.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.concurrent</div>
<h2 title="类 ThreadDumpper" class="title">类 ThreadDumpper</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.concurrent.ThreadDumpper</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ThreadDumpper</span>
extends java.lang.Object</pre>
<div class="block">由程序触发的ThreadDump，打印到日志中.
 
 因为ThreadDump本身会造成JVM停顿，所以加上了开关和最少间隔时间的选项(默认不限制)
 
 因为ThreadInfo的toString()最多只会打印8层的StackTrace，所以加上了最大打印层数的选项.(默认为8)</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/concurrent/ThreadDumpper.html#ThreadDumpper--">ThreadDumpper</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/concurrent/ThreadDumpper.html#ThreadDumpper-long-int-">ThreadDumpper</a></span>(long&nbsp;leastIntervalMills,
             int&nbsp;maxStackLevel)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/concurrent/ThreadDumpper.html#setEnable-boolean-">setEnable</a></span>(boolean&nbsp;enable)</code>
<div class="block">快速关闭打印</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/concurrent/ThreadDumpper.html#setLeastInterval-int-">setLeastInterval</a></span>(int&nbsp;leastIntervalSeconds)</code>
<div class="block">打印ThreadDump的最小时间间隔，单位为秒，默认为0不限制.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/concurrent/ThreadDumpper.html#setMaxStackLevel-int-">setMaxStackLevel</a></span>(int&nbsp;maxStackLevel)</code>
<div class="block">打印StackTrace的最大深度, 默认为8</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/concurrent/ThreadDumpper.html#threadDumpIfNeed--">threadDumpIfNeed</a></span>()</code>
<div class="block">符合条件则打印线程栈.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/concurrent/ThreadDumpper.html#threadDumpIfNeed-java.lang.String-">threadDumpIfNeed</a></span>(java.lang.String&nbsp;reasonMsg)</code>
<div class="block">符合条件则打印线程栈.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="ThreadDumpper--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ThreadDumpper</h4>
<pre>public&nbsp;ThreadDumpper()</pre>
</li>
</ul>
<a name="ThreadDumpper-long-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ThreadDumpper</h4>
<pre>public&nbsp;ThreadDumpper(long&nbsp;leastIntervalMills,
                     int&nbsp;maxStackLevel)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="threadDumpIfNeed--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>threadDumpIfNeed</h4>
<pre>public&nbsp;void&nbsp;threadDumpIfNeed()</pre>
<div class="block">符合条件则打印线程栈.</div>
</li>
</ul>
<a name="threadDumpIfNeed-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>threadDumpIfNeed</h4>
<pre>public&nbsp;void&nbsp;threadDumpIfNeed(java.lang.String&nbsp;reasonMsg)</pre>
<div class="block">符合条件则打印线程栈.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>reasonMsg</code> - 发生ThreadDump的原因</dd>
</dl>
</li>
</ul>
<a name="setEnable-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnable</h4>
<pre>public&nbsp;void&nbsp;setEnable(boolean&nbsp;enable)</pre>
<div class="block">快速关闭打印</div>
</li>
</ul>
<a name="setLeastInterval-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLeastInterval</h4>
<pre>public&nbsp;void&nbsp;setLeastInterval(int&nbsp;leastIntervalSeconds)</pre>
<div class="block">打印ThreadDump的最小时间间隔，单位为秒，默认为0不限制.</div>
</li>
</ul>
<a name="setMaxStackLevel-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setMaxStackLevel</h4>
<pre>public&nbsp;void&nbsp;setMaxStackLevel(int&nbsp;maxStackLevel)</pre>
<div class="block">打印StackTrace的最大深度, 默认为8</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/concurrent/SamplerTest.html" title="com.huazheng.tunny.tools.concurrent中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/concurrent/ThreadDumpperTest.html" title="com.huazheng.tunny.tools.concurrent中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/concurrent/ThreadDumpper.html" target="_top">框架</a></li>
<li><a href="ThreadDumpper.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
