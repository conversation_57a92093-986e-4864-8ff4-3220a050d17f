<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:34 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ThreadPoolBuilder</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ThreadPoolBuilder";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPoolTest.LongRunTask.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.html" target="_top">框架</a></li>
<li><a href="ThreadPoolBuilder.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.concurrent.threadpool</div>
<h2 title="类 ThreadPoolBuilder" class="title">类 ThreadPoolBuilder</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.concurrent.threadpool.ThreadPoolBuilder</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ThreadPoolBuilder</span>
extends java.lang.Object</pre>
<div class="block">ThreadPool创建的工具类.
 
 对比JDK Executors中的newFixedThreadPool(), newCachedThreadPool(),newScheduledThreadPool, 提供更多有用的配置项.
 
 另包含了移植自Tomcat的QueuableCachedPool.
 
 使用示例如下：
 
 <pre>
 ExecutorService ExecutorService = new FixedThreadPoolBuilder().setPoolSize(10).build();
 </pre>
 
 参考文章 《Java ThreadPool的正确打开方式》http://calvin1978.blogcn.com/articles/java-threadpool.html</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>嵌套类概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="嵌套类概要表, 列表嵌套类和解释">
<caption><span>嵌套类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">类和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.CachedThreadPoolBuilder</a></span></code>
<div class="block">创建CachedThreadPool, maxSize建议设置
 
 1.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.FixedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.FixedThreadPoolBuilder</a></span></code>
<div class="block">创建FixedThreadPool.建议必须设置queueSize保证有界。</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.QueuableCachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.QueuableCachedThreadPoolBuilder</a></span></code>
<div class="block">从Tomcat移植过来的可扩展可用Queue缓存任务的ThreadPool</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.ScheduledThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.ScheduledThreadPoolBuilder</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.html#ThreadPoolBuilder--">ThreadPoolBuilder</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.CachedThreadPoolBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.html#cachedPool--">cachedPool</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.FixedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.FixedThreadPoolBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.html#fixedPool--">fixedPool</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.QueuableCachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.QueuableCachedThreadPoolBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.html#queuableCachedPool--">queuableCachedPool</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.ScheduledThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.ScheduledThreadPoolBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.html#scheduledPool--">scheduledPool</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="ThreadPoolBuilder--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ThreadPoolBuilder</h4>
<pre>public&nbsp;ThreadPoolBuilder()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="fixedPool--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fixedPool</h4>
<pre>public static&nbsp;<a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.FixedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.FixedThreadPoolBuilder</a>&nbsp;fixedPool()</pre>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.FixedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><code>ThreadPoolBuilder.FixedThreadPoolBuilder</code></a></dd>
</dl>
</li>
</ul>
<a name="cachedPool--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cachedPool</h4>
<pre>public static&nbsp;<a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.CachedThreadPoolBuilder</a>&nbsp;cachedPool()</pre>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><code>ThreadPoolBuilder.CachedThreadPoolBuilder</code></a></dd>
</dl>
</li>
</ul>
<a name="scheduledPool--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>scheduledPool</h4>
<pre>public static&nbsp;<a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.ScheduledThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.ScheduledThreadPoolBuilder</a>&nbsp;scheduledPool()</pre>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.ScheduledThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><code>ThreadPoolBuilder.ScheduledThreadPoolBuilder</code></a></dd>
</dl>
</li>
</ul>
<a name="queuableCachedPool--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>queuableCachedPool</h4>
<pre>public static&nbsp;<a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.QueuableCachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.QueuableCachedThreadPoolBuilder</a>&nbsp;queuableCachedPool()</pre>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.QueuableCachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><code>ThreadPoolBuilder.QueuableCachedThreadPoolBuilder</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPoolTest.LongRunTask.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.html" target="_top">框架</a></li>
<li><a href="ThreadPoolBuilder.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
