<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:35 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>com.huazheng.tunny.tools.concurrent.threadpool</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.huazheng.tunny.tools.concurrent.threadpool";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li class="navBarCell1Rev">程序包</li>
<li>类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/package-summary.html">上一个程序包</a></li>
<li><a href="../../../../../../com/huazheng/tunny/tools/concurrent/type/package-summary.html">下一个程序包</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/huazheng/tunny/tools/concurrent/threadpool/package-summary.html" target="_top">框架</a></li>
<li><a href="package-summary.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="程序包" class="title">程序包&nbsp;com.huazheng.tunny.tools.concurrent.threadpool</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="类概要表, 列表类和解释">
<caption><span>类概要</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">类</th>
<th class="colLast" scope="col">说明</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/AbortPolicyWithReport.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">AbortPolicyWithReport</a></td>
<td class="colLast">
<div class="block">移植至Dubbo.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/AbortPolicyWithReportTest.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">AbortPolicyWithReportTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">QueuableCachedThreadPool</a></td>
<td class="colLast">
<div class="block">From Tomcat 8.5.6, 传统的FixedThreadPool有Queue但线程数量不变，而CachedThreadPool线程数可变但没有Queue
 
 Tomcat的线程池，通过控制TaskQueue，线程数，但线程数到达最大时会进入Queue中.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPool.ControllableQueue.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">QueuableCachedThreadPool.ControllableQueue</a></td>
<td class="colLast">
<div class="block">https://github.com/apache/tomcat/blob/trunk/java/org/apache/tomcat/util/threads/TaskQueue.java</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPoolTest.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">QueuableCachedThreadPoolTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/QueuableCachedThreadPoolTest.LongRunTask.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">QueuableCachedThreadPoolTest.LongRunTask</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder</a></td>
<td class="colLast">
<div class="block">ThreadPool创建的工具类.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.CachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.CachedThreadPoolBuilder</a></td>
<td class="colLast">
<div class="block">创建CachedThreadPool, maxSize建议设置
 
 1.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.FixedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.FixedThreadPoolBuilder</a></td>
<td class="colLast">
<div class="block">创建FixedThreadPool.建议必须设置queueSize保证有界。</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.QueuableCachedThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.QueuableCachedThreadPoolBuilder</a></td>
<td class="colLast">
<div class="block">从Tomcat移植过来的可扩展可用Queue缓存任务的ThreadPool</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilder.ScheduledThreadPoolBuilder.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilder.ScheduledThreadPoolBuilder</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolBuilderTest.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolBuilderTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtil.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolUtil</a></td>
<td class="colLast">
<div class="block">线程池工具集
 
 1.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../../com/huazheng/tunny/tools/concurrent/threadpool/ThreadPoolUtilTest.html" title="com.huazheng.tunny.tools.concurrent.threadpool中的类">ThreadPoolUtilTest</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li class="navBarCell1Rev">程序包</li>
<li>类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/package-summary.html">上一个程序包</a></li>
<li><a href="../../../../../../com/huazheng/tunny/tools/concurrent/type/package-summary.html">下一个程序包</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/huazheng/tunny/tools/concurrent/threadpool/package-summary.html" target="_top">框架</a></li>
<li><a href="package-summary.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
