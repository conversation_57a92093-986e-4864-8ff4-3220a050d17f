<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:32 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Concurrents</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Concurrents";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/concurrent/BasicFutureTest.MyFuture.html" title="com.huazheng.tunny.tools.concurrent中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/concurrent/ConcurrentsTest.html" title="com.huazheng.tunny.tools.concurrent中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/concurrent/Concurrents.html" target="_top">框架</a></li>
<li><a href="Concurrents.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.concurrent</div>
<h2 title="类 Concurrents" class="title">类 Concurrents</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.concurrent.Concurrents</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Concurrents</span>
extends java.lang.Object</pre>
<div class="block">并发常用工具类</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/concurrent/Concurrents.html#Concurrents--">Concurrents</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static java.util.concurrent.CountDownLatch</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/concurrent/Concurrents.html#countDownLatch-int-">countDownLatch</a></span>(int&nbsp;count)</code>
<div class="block">返回CountDownLatch, 每条线程减1，减到0时正在latch.wait()的进程继续进行</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static java.util.concurrent.CyclicBarrier</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/concurrent/Concurrents.html#cyclicBarrier-int-">cyclicBarrier</a></span>(int&nbsp;count)</code>
<div class="block">返回CyclicBarrier，每条线程减1并等待，减到0时，所有线程继续运行</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static java.util.concurrent.Semaphore</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/concurrent/Concurrents.html#fairSemaphore-int-">fairSemaphore</a></span>(int&nbsp;permits)</code>
<div class="block">返回公平的信号量，先请求的线程先拿到信号量</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html" title="com.huazheng.tunny.tools.concurrent.jsr166e中的类">LongAdder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/concurrent/Concurrents.html#longAdder--">longAdder</a></span>()</code>
<div class="block">返回没有激烈CAS冲突的LongAdder, 并发的＋1将在不同的Counter里进行，只在取值时将多个Counter求和.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static java.util.concurrent.Semaphore</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/concurrent/Concurrents.html#nonFairSemaphore-int-">nonFairSemaphore</a></span>(int&nbsp;permits)</code>
<div class="block">返回默认的非公平信号量，先请求的线程不一定先拿到信号量</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static com.google.common.util.concurrent.RateLimiter</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/concurrent/Concurrents.html#rateLimiter-int-">rateLimiter</a></span>(int&nbsp;permitsPerSecond)</code>
<div class="block">返回令牌桶算法的RateLimiter</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/huazheng/tunny/tools/concurrent/Sampler.html" title="com.huazheng.tunny.tools.concurrent中的类">Sampler</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/concurrent/Concurrents.html#sampler-double-">sampler</a></span>(double&nbsp;selectPercent)</code>
<div class="block">返回采样器.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/huazheng/tunny/tools/concurrent/TimeIntervalLimiter.html" title="com.huazheng.tunny.tools.concurrent中的类">TimeIntervalLimiter</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/concurrent/Concurrents.html#timeIntervalLimiter-long-java.util.concurrent.TimeUnit-">timeIntervalLimiter</a></span>(long&nbsp;interval,
                   java.util.concurrent.TimeUnit&nbsp;timeUnit)</code>
<div class="block">返回时间间隔限制器.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="Concurrents--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Concurrents</h4>
<pre>public&nbsp;Concurrents()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="longAdder--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>longAdder</h4>
<pre>public static&nbsp;<a href="../../../../../com/huazheng/tunny/tools/concurrent/jsr166e/LongAdder.html" title="com.huazheng.tunny.tools.concurrent.jsr166e中的类">LongAdder</a>&nbsp;longAdder()</pre>
<div class="block">返回没有激烈CAS冲突的LongAdder, 并发的＋1将在不同的Counter里进行，只在取值时将多个Counter求和.
 
 为了保持JDK版本兼容性，统一采用移植版</div>
</li>
</ul>
<a name="countDownLatch-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>countDownLatch</h4>
<pre>public static&nbsp;java.util.concurrent.CountDownLatch&nbsp;countDownLatch(int&nbsp;count)</pre>
<div class="block">返回CountDownLatch, 每条线程减1，减到0时正在latch.wait()的进程继续进行</div>
</li>
</ul>
<a name="cyclicBarrier-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cyclicBarrier</h4>
<pre>public static&nbsp;java.util.concurrent.CyclicBarrier&nbsp;cyclicBarrier(int&nbsp;count)</pre>
<div class="block">返回CyclicBarrier，每条线程减1并等待，减到0时，所有线程继续运行</div>
</li>
</ul>
<a name="nonFairSemaphore-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nonFairSemaphore</h4>
<pre>public static&nbsp;java.util.concurrent.Semaphore&nbsp;nonFairSemaphore(int&nbsp;permits)</pre>
<div class="block">返回默认的非公平信号量，先请求的线程不一定先拿到信号量</div>
</li>
</ul>
<a name="fairSemaphore-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fairSemaphore</h4>
<pre>public static&nbsp;java.util.concurrent.Semaphore&nbsp;fairSemaphore(int&nbsp;permits)</pre>
<div class="block">返回公平的信号量，先请求的线程先拿到信号量</div>
</li>
</ul>
<a name="rateLimiter-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rateLimiter</h4>
<pre>public static&nbsp;com.google.common.util.concurrent.RateLimiter&nbsp;rateLimiter(int&nbsp;permitsPerSecond)</pre>
<div class="block">返回令牌桶算法的RateLimiter</div>
</li>
</ul>
<a name="sampler-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sampler</h4>
<pre>public static&nbsp;<a href="../../../../../com/huazheng/tunny/tools/concurrent/Sampler.html" title="com.huazheng.tunny.tools.concurrent中的类">Sampler</a>&nbsp;sampler(double&nbsp;selectPercent)</pre>
<div class="block">返回采样器.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>selectPercent</code> - 采样率，在0-100 之间，可以有小数位</dd>
</dl>
</li>
</ul>
<a name="timeIntervalLimiter-long-java.util.concurrent.TimeUnit-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>timeIntervalLimiter</h4>
<pre>public static&nbsp;<a href="../../../../../com/huazheng/tunny/tools/concurrent/TimeIntervalLimiter.html" title="com.huazheng.tunny.tools.concurrent中的类">TimeIntervalLimiter</a>&nbsp;timeIntervalLimiter(long&nbsp;interval,
                                                      java.util.concurrent.TimeUnit&nbsp;timeUnit)</pre>
<div class="block">返回时间间隔限制器.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>interval</code> - 间隔时间</dd>
<dd><code>timeUnit</code> - 间隔时间单位</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/concurrent/BasicFutureTest.MyFuture.html" title="com.huazheng.tunny.tools.concurrent中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/concurrent/ConcurrentsTest.html" title="com.huazheng.tunny.tools.concurrent中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/concurrent/Concurrents.html" target="_top">框架</a></li>
<li><a href="Concurrents.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
