<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:32 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Sampler.NeverSampler</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Sampler.NeverSampler";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/concurrent/Sampler.AlwaysSampler.html" title="com.huazheng.tunny.tools.concurrent中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/concurrent/SamplerTest.html" title="com.huazheng.tunny.tools.concurrent中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/concurrent/Sampler.NeverSampler.html" target="_top">框架</a></li>
<li><a href="Sampler.NeverSampler.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.huazheng.tunny.tools.concurrent.Sampler">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.concurrent</div>
<h2 title="类 Sampler.NeverSampler" class="title">类 Sampler.NeverSampler</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../../com/huazheng/tunny/tools/concurrent/Sampler.html" title="com.huazheng.tunny.tools.concurrent中的类">com.huazheng.tunny.tools.concurrent.Sampler</a></li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.concurrent.Sampler.NeverSampler</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>封闭类:</dt>
<dd><a href="../../../../../com/huazheng/tunny/tools/concurrent/Sampler.html" title="com.huazheng.tunny.tools.concurrent中的类">Sampler</a></dd>
</dl>
<hr>
<br>
<pre>protected static class <span class="typeNameLabel">Sampler.NeverSampler</span>
extends <a href="../../../../../com/huazheng/tunny/tools/concurrent/Sampler.html" title="com.huazheng.tunny.tools.concurrent中的类">Sampler</a></pre>
<div class="block">采样率为0时，总是返回false</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>嵌套类概要</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.huazheng.tunny.tools.concurrent.Sampler">
<!--   -->
</a>
<h3>从类继承的嵌套类/接口&nbsp;com.huazheng.tunny.tools.concurrent.<a href="../../../../../com/huazheng/tunny/tools/concurrent/Sampler.html" title="com.huazheng.tunny.tools.concurrent中的类">Sampler</a></h3>
<code><a href="../../../../../com/huazheng/tunny/tools/concurrent/Sampler.AlwaysSampler.html" title="com.huazheng.tunny.tools.concurrent中的类">Sampler.AlwaysSampler</a>, <a href="../../../../../com/huazheng/tunny/tools/concurrent/Sampler.NeverSampler.html" title="com.huazheng.tunny.tools.concurrent中的类">Sampler.NeverSampler</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符</th>
<th class="colLast" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected </code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/concurrent/Sampler.NeverSampler.html#NeverSampler--">NeverSampler</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/concurrent/Sampler.NeverSampler.html#select--">select</a></span>()</code>
<div class="block">判断当前请求是否命中采样</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.huazheng.tunny.tools.concurrent.Sampler">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;com.huazheng.tunny.tools.concurrent.<a href="../../../../../com/huazheng/tunny/tools/concurrent/Sampler.html" title="com.huazheng.tunny.tools.concurrent中的类">Sampler</a></h3>
<code><a href="../../../../../com/huazheng/tunny/tools/concurrent/Sampler.html#create-java.lang.Double-">create</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="NeverSampler--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>NeverSampler</h4>
<pre>protected&nbsp;NeverSampler()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="select--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>select</h4>
<pre>public&nbsp;boolean&nbsp;select()</pre>
<div class="block"><span class="descfrmTypeLabel">从类复制的说明:&nbsp;<code><a href="../../../../../com/huazheng/tunny/tools/concurrent/Sampler.html#select--">Sampler</a></code></span></div>
<div class="block">判断当前请求是否命中采样</div>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code><a href="../../../../../com/huazheng/tunny/tools/concurrent/Sampler.html#select--">select</a></code>&nbsp;在类中&nbsp;<code><a href="../../../../../com/huazheng/tunny/tools/concurrent/Sampler.html" title="com.huazheng.tunny.tools.concurrent中的类">Sampler</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/concurrent/Sampler.AlwaysSampler.html" title="com.huazheng.tunny.tools.concurrent中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/concurrent/SamplerTest.html" title="com.huazheng.tunny.tools.concurrent中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/concurrent/Sampler.NeverSampler.html" target="_top">框架</a></li>
<li><a href="Sampler.NeverSampler.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.huazheng.tunny.tools.concurrent.Sampler">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
