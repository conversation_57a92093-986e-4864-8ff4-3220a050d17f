<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:34 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>SortedArrayList</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SortedArrayList";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":42,"i2":10,"i3":42,"i4":10,"i5":10,"i6":10,"i7":10,"i8":42};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSetTest.html" title="com.huazheng.tunny.tools.collection.type中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayListTest.html" title="com.huazheng.tunny.tools.collection.type中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/huazheng/tunny/tools/collection/type/SortedArrayList.html" target="_top">框架</a></li>
<li><a href="SortedArrayList.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.collection.type</div>
<h2 title="类 SortedArrayList" class="title">类 SortedArrayList&lt;E&gt;</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.util.AbstractCollection&lt;E&gt;</li>
<li>
<ul class="inheritance">
<li>java.util.AbstractList&lt;E&gt;</li>
<li>
<ul class="inheritance">
<li>java.util.ArrayList&lt;E&gt;</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.collection.type.SortedArrayList&lt;E&gt;</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>java.io.Serializable, java.lang.Cloneable, java.lang.Iterable&lt;E&gt;, java.util.Collection&lt;E&gt;, java.util.List&lt;E&gt;, java.util.RandomAccess</dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">SortedArrayList&lt;E&gt;</span>
extends java.util.ArrayList&lt;E&gt;</pre>
<div class="block">从Jodd整体复制，部分指定了index的操作不支持，如 add(index, element)
 
 修改包括：改进Comparator泛型定义，findInsertionPoint的位移改进
 
 https://github.com/oblac/jodd/blob/master/jodd-core/src/main/java/jodd/util/collection/SortedArrayList.java
 
 An extension of <code>ArrayList</code> that insures that all of the items
 added are sorted. <b>This breaks original list contract!</b>.
 A binary search method is used to provide a quick way to
 auto sort this list.Note: Not all methods for adding and
 removing elements are supported.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../../serialized-form.html#com.huazheng.tunny.tools.collection.type.SortedArrayList">序列化表格</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected java.util.Comparator&lt;? super <a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html#comparator">comparator</a></span></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.util.AbstractList">
<!--   -->
</a>
<h3>从类继承的字段&nbsp;java.util.AbstractList</h3>
<code>modCount</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html#SortedArrayList--">SortedArrayList</a></span>()</code>
<div class="block">Constructs a new <code>SortedArrayList</code> expecting
 elements are comparable.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html#SortedArrayList-java.util.Collection-">SortedArrayList</a></span>(java.util.Collection&lt;? extends <a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&gt;&nbsp;c)</code>
<div class="block">Constructs a new <code>SortedArrayList</code> expecting
 elements are comparable.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html#SortedArrayList-java.util.Comparator-">SortedArrayList</a></span>(java.util.Comparator&lt;? super <a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&gt;&nbsp;c)</code>
<div class="block">Constructs a new <code>SortedArrayList</code>.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html#add-E-">add</a></span>(<a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&nbsp;o)</code>
<div class="block">Adds an Object to sorted list.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html#add-int-E-">add</a></span>(int&nbsp;index,
   <a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&nbsp;element)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html#addAll-java.util.Collection-">addAll</a></span>(java.util.Collection&lt;? extends <a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&gt;&nbsp;c)</code>
<div class="block">Add all of the elements in the given collection to this list.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html#addAll-int-java.util.Collection-">addAll</a></span>(int&nbsp;index,
      java.util.Collection&lt;? extends <a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&gt;&nbsp;c)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html#compare-E-E-">compare</a></span>(<a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&nbsp;k1,
       <a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&nbsp;k2)</code>
<div class="block">Compares two keys using the correct comparison method for this
 collection.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html#findInsertionPoint-E-">findInsertionPoint</a></span>(<a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&nbsp;o)</code>
<div class="block">Finds the index at which object should be inserted.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html#findInsertionPoint-E-int-int-">findInsertionPoint</a></span>(<a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&nbsp;o,
                  int&nbsp;originalLow,
                  int&nbsp;originalHigh)</code>
<div class="block">Conducts a binary search to find the index where Object
 should be inserted.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.util.Comparator</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html#getComparator--">getComparator</a></span>()</code>
<div class="block">Returns comparator assigned to this collection, if such exist.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html#set-int-E-">set</a></span>(int&nbsp;index,
   <a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&nbsp;element)</code>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.ArrayList">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.util.ArrayList</h3>
<code>clear, clone, contains, ensureCapacity, forEach, get, indexOf, isEmpty, iterator, lastIndexOf, listIterator, listIterator, remove, remove, removeAll, removeIf, removeRange, replaceAll, retainAll, size, sort, spliterator, subList, toArray, toArray, trimToSize</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.AbstractList">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.util.AbstractList</h3>
<code>equals, hashCode</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.AbstractCollection">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.util.AbstractCollection</h3>
<code>containsAll, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>finalize, getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.List">
<!--   -->
</a>
<h3>从接口继承的方法&nbsp;java.util.List</h3>
<code>containsAll, equals, hashCode</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.Collection">
<!--   -->
</a>
<h3>从接口继承的方法&nbsp;java.util.Collection</h3>
<code>parallelStream, stream</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="comparator">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>comparator</h4>
<pre>protected final&nbsp;java.util.Comparator&lt;? super <a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&gt; comparator</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="SortedArrayList-java.util.Comparator-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SortedArrayList</h4>
<pre>public&nbsp;SortedArrayList(java.util.Comparator&lt;? super <a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&gt;&nbsp;c)</pre>
<div class="block">Constructs a new <code>SortedArrayList</code>.</div>
</li>
</ul>
<a name="SortedArrayList--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SortedArrayList</h4>
<pre>public&nbsp;SortedArrayList()</pre>
<div class="block">Constructs a new <code>SortedArrayList</code> expecting
 elements are comparable.</div>
</li>
</ul>
<a name="SortedArrayList-java.util.Collection-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>SortedArrayList</h4>
<pre>public&nbsp;SortedArrayList(java.util.Collection&lt;? extends <a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&gt;&nbsp;c)</pre>
<div class="block">Constructs a new <code>SortedArrayList</code> expecting
 elements are comparable.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getComparator--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getComparator</h4>
<pre>public&nbsp;java.util.Comparator&nbsp;getComparator()</pre>
<div class="block">Returns comparator assigned to this collection, if such exist.</div>
</li>
</ul>
<a name="add-java.lang.Object-">
<!--   -->
</a><a name="add-E-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>add</h4>
<pre>public&nbsp;boolean&nbsp;add(<a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&nbsp;o)</pre>
<div class="block">Adds an Object to sorted list. Object is inserted at correct place, found
 using binary search. If the same item exist, it will be put to the end of
 the range.
 <p>
 This method breaks original list contract since objects are not
 added at the list end, but in sorted manner.</div>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>add</code>&nbsp;在接口中&nbsp;<code>java.util.Collection&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>add</code>&nbsp;在接口中&nbsp;<code>java.util.List&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>add</code>&nbsp;在类中&nbsp;<code>java.util.ArrayList&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="addAll-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addAll</h4>
<pre>public&nbsp;boolean&nbsp;addAll(java.util.Collection&lt;? extends <a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&gt;&nbsp;c)</pre>
<div class="block">Add all of the elements in the given collection to this list.</div>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>addAll</code>&nbsp;在接口中&nbsp;<code>java.util.Collection&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>addAll</code>&nbsp;在接口中&nbsp;<code>java.util.List&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>addAll</code>&nbsp;在类中&nbsp;<code>java.util.ArrayList&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="findInsertionPoint-java.lang.Object-">
<!--   -->
</a><a name="findInsertionPoint-E-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findInsertionPoint</h4>
<pre>public&nbsp;int&nbsp;findInsertionPoint(<a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&nbsp;o)</pre>
<div class="block">Finds the index at which object should be inserted.</div>
</li>
</ul>
<a name="add-int-java.lang.Object-">
<!--   -->
</a><a name="add-int-E-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>add</h4>
<pre>@Deprecated
public&nbsp;void&nbsp;add(int&nbsp;index,
                             <a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&nbsp;element)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>add</code>&nbsp;在接口中&nbsp;<code>java.util.List&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>add</code>&nbsp;在类中&nbsp;<code>java.util.ArrayList&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&gt;</code></dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code> - This method not supported.</dd>
</dl>
</li>
</ul>
<a name="set-int-java.lang.Object-">
<!--   -->
</a><a name="set-int-E-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set</h4>
<pre>@Deprecated
public&nbsp;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&nbsp;set(int&nbsp;index,
                          <a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&nbsp;element)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>set</code>&nbsp;在接口中&nbsp;<code>java.util.List&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>set</code>&nbsp;在类中&nbsp;<code>java.util.ArrayList&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&gt;</code></dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code> - This method not supported.</dd>
</dl>
</li>
</ul>
<a name="addAll-int-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addAll</h4>
<pre>@Deprecated
public&nbsp;boolean&nbsp;addAll(int&nbsp;index,
                                   java.util.Collection&lt;? extends <a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&gt;&nbsp;c)</pre>
<div class="block"><span class="deprecatedLabel">已过时。</span>&nbsp;</div>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>addAll</code>&nbsp;在接口中&nbsp;<code>java.util.List&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>addAll</code>&nbsp;在类中&nbsp;<code>java.util.ArrayList&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&gt;</code></dd>
<dt><span class="throwsLabel">抛出:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code> - This method not supported.</dd>
</dl>
</li>
</ul>
<a name="compare-java.lang.Object-java.lang.Object-">
<!--   -->
</a><a name="compare-E-E-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>compare</h4>
<pre>protected&nbsp;int&nbsp;compare(<a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&nbsp;k1,
                      <a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&nbsp;k2)</pre>
<div class="block">Compares two keys using the correct comparison method for this
 collection.</div>
</li>
</ul>
<a name="findInsertionPoint-java.lang.Object-int-int-">
<!--   -->
</a><a name="findInsertionPoint-E-int-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>findInsertionPoint</h4>
<pre>protected&nbsp;int&nbsp;findInsertionPoint(<a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayList.html" title="SortedArrayList中的类型参数">E</a>&nbsp;o,
                                 int&nbsp;originalLow,
                                 int&nbsp;originalHigh)</pre>
<div class="block">Conducts a binary search to find the index where Object
 should be inserted.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSetTest.html" title="com.huazheng.tunny.tools.collection.type中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../../com/huazheng/tunny/tools/collection/type/SortedArrayListTest.html" title="com.huazheng.tunny.tools.collection.type中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/huazheng/tunny/tools/collection/type/SortedArrayList.html" target="_top">框架</a></li>
<li><a href="SortedArrayList.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
