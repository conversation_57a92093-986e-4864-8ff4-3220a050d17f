<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:34 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>CollectionUtil</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CollectionUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9,"i14":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/collection/ArrayUtilTest.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/collection/CollectionUtilTest.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/collection/CollectionUtil.html" target="_top">框架</a></li>
<li><a href="CollectionUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.collection</div>
<h2 title="类 CollectionUtil" class="title">类 CollectionUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.collection.CollectionUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">CollectionUtil</span>
extends java.lang.Object</pre>
<div class="block">通用Collection的工具集
 
 1. 集合是否为空，取得集合中首个及最后一个元素，判断集合是否完全相等
 
 2. 集合的最大最小值，及Top N, Bottom N
 
 关于List, Map, Queue, Set的特殊工具集，另见特定的Util.
 
 另JDK中缺少ComparableComparator和NullComparator，直到JDK8才补上。
 因此平时请使用guava的Ordering.natural(),fluentable的API更好用，可以链式设置nullFirst，nullLast,reverse</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Ordering</code></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/CollectionUtil.html#CollectionUtil--">CollectionUtil</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static &lt;T extends java.lang.Comparable&lt;?&gt;&gt;<br>java.util.List&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/CollectionUtil.html#bottomN-java.lang.Iterable-int-">bottomN</a></span>(java.lang.Iterable&lt;T&gt;&nbsp;coll,
       int&nbsp;n)</code>
<div class="block">返回Iterable中最小的N个对象, back by guava.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.List&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/CollectionUtil.html#bottomN-java.lang.Iterable-int-java.util.Comparator-">bottomN</a></span>(java.lang.Iterable&lt;T&gt;&nbsp;coll,
       int&nbsp;n,
       java.util.Comparator&lt;? super T&gt;&nbsp;comp)</code>
<div class="block">返回Iterable中最小的N个对象, back by guava.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/CollectionUtil.html#elementsEqual-java.lang.Iterable-java.lang.Iterable-">elementsEqual</a></span>(java.lang.Iterable&lt;?&gt;&nbsp;iterable1,
             java.lang.Iterable&lt;?&gt;&nbsp;iterable2)</code>
<div class="block">两个集合中的所有元素按顺序相等.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/CollectionUtil.html#getFirst-java.util.Collection-">getFirst</a></span>(java.util.Collection&lt;T&gt;&nbsp;collection)</code>
<div class="block">取得Collection的第一个元素，如果collection为空返回null.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/CollectionUtil.html#getLast-java.util.Collection-">getLast</a></span>(java.util.Collection&lt;T&gt;&nbsp;collection)</code>
<div class="block">获取Collection的最后一个元素，如果collection为空返回null.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/CollectionUtil.html#isEmpty-java.util.Collection-">isEmpty</a></span>(java.util.Collection&lt;?&gt;&nbsp;collection)</code>
<div class="block">判断是否为空.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/CollectionUtil.html#isNotEmpty-java.util.Collection-">isNotEmpty</a></span>(java.util.Collection&lt;?&gt;&nbsp;collection)</code>
<div class="block">判断是否不为空.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static &lt;T extends java.lang.Object &amp; java.lang.Comparable&lt;? super T&gt;&gt;<br>T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/CollectionUtil.html#max-java.util.Collection-">max</a></span>(java.util.Collection&lt;? extends T&gt;&nbsp;coll)</code>
<div class="block">返回无序集合中的最大值，使用元素默认排序</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/CollectionUtil.html#max-java.util.Collection-java.util.Comparator-">max</a></span>(java.util.Collection&lt;? extends T&gt;&nbsp;coll,
   java.util.Comparator&lt;? super T&gt;&nbsp;comp)</code>
<div class="block">返回无序集合中的最大值</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static &lt;T extends java.lang.Object &amp; java.lang.Comparable&lt;? super T&gt;&gt;<br>T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/CollectionUtil.html#min-java.util.Collection-">min</a></span>(java.util.Collection&lt;? extends T&gt;&nbsp;coll)</code>
<div class="block">返回无序集合中的最小值，使用元素默认排序</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/CollectionUtil.html#min-java.util.Collection-java.util.Comparator-">min</a></span>(java.util.Collection&lt;? extends T&gt;&nbsp;coll,
   java.util.Comparator&lt;? super T&gt;&nbsp;comp)</code>
<div class="block">返回无序集合中的最小值</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static &lt;T extends java.lang.Object &amp; java.lang.Comparable&lt;? super T&gt;&gt;<br><a href="../../../../../com/huazheng/tunny/tools/base/type/Pair.html" title="com.huazheng.tunny.tools.base.type中的类">Pair</a>&lt;T,T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/CollectionUtil.html#minAndMax-java.util.Collection-">minAndMax</a></span>(java.util.Collection&lt;? extends T&gt;&nbsp;coll)</code>
<div class="block">同时返回无序集合中的最小值和最大值，使用元素默认排序
 
 在返回的Pair中，第一个为最小值，第二个为最大值</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;<a href="../../../../../com/huazheng/tunny/tools/base/type/Pair.html" title="com.huazheng.tunny.tools.base.type中的类">Pair</a>&lt;T,T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/CollectionUtil.html#minAndMax-java.util.Collection-java.util.Comparator-">minAndMax</a></span>(java.util.Collection&lt;? extends T&gt;&nbsp;coll,
         java.util.Comparator&lt;? super T&gt;&nbsp;comp)</code>
<div class="block">返回无序集合中的最小值和最大值
 
 在返回的Pair中，第一个为最小值，第二个为最大值</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static &lt;T extends java.lang.Comparable&lt;?&gt;&gt;<br>java.util.List&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/CollectionUtil.html#topN-java.lang.Iterable-int-">topN</a></span>(java.lang.Iterable&lt;T&gt;&nbsp;coll,
    int&nbsp;n)</code>
<div class="block">返回Iterable中最大的N个对象, back by guava.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.List&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/CollectionUtil.html#topN-java.lang.Iterable-int-java.util.Comparator-">topN</a></span>(java.lang.Iterable&lt;T&gt;&nbsp;coll,
    int&nbsp;n,
    java.util.Comparator&lt;? super T&gt;&nbsp;comp)</code>
<div class="block">返回Iterable中最大的N个对象, back by guava.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="CollectionUtil--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CollectionUtil</h4>
<pre>public&nbsp;CollectionUtil()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="isEmpty-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEmpty</h4>
<pre>public static&nbsp;boolean&nbsp;isEmpty(java.util.Collection&lt;?&gt;&nbsp;collection)</pre>
<div class="block">判断是否为空.</div>
</li>
</ul>
<a name="isNotEmpty-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isNotEmpty</h4>
<pre>public static&nbsp;boolean&nbsp;isNotEmpty(java.util.Collection&lt;?&gt;&nbsp;collection)</pre>
<div class="block">判断是否不为空.</div>
</li>
</ul>
<a name="getFirst-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFirst</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;T&nbsp;getFirst(java.util.Collection&lt;T&gt;&nbsp;collection)</pre>
<div class="block">取得Collection的第一个元素，如果collection为空返回null.</div>
</li>
</ul>
<a name="getLast-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLast</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;T&nbsp;getLast(java.util.Collection&lt;T&gt;&nbsp;collection)</pre>
<div class="block">获取Collection的最后一个元素，如果collection为空返回null.</div>
</li>
</ul>
<a name="elementsEqual-java.lang.Iterable-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>elementsEqual</h4>
<pre>public static&nbsp;boolean&nbsp;elementsEqual(java.lang.Iterable&lt;?&gt;&nbsp;iterable1,
                                    java.lang.Iterable&lt;?&gt;&nbsp;iterable2)</pre>
<div class="block">两个集合中的所有元素按顺序相等.</div>
</li>
</ul>
<a name="min-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>min</h4>
<pre>public static&nbsp;&lt;T extends java.lang.Object &amp; java.lang.Comparable&lt;? super T&gt;&gt;&nbsp;T&nbsp;min(java.util.Collection&lt;? extends T&gt;&nbsp;coll)</pre>
<div class="block">返回无序集合中的最小值，使用元素默认排序</div>
</li>
</ul>
<a name="min-java.util.Collection-java.util.Comparator-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>min</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;T&nbsp;min(java.util.Collection&lt;? extends T&gt;&nbsp;coll,
                        java.util.Comparator&lt;? super T&gt;&nbsp;comp)</pre>
<div class="block">返回无序集合中的最小值</div>
</li>
</ul>
<a name="max-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>max</h4>
<pre>public static&nbsp;&lt;T extends java.lang.Object &amp; java.lang.Comparable&lt;? super T&gt;&gt;&nbsp;T&nbsp;max(java.util.Collection&lt;? extends T&gt;&nbsp;coll)</pre>
<div class="block">返回无序集合中的最大值，使用元素默认排序</div>
</li>
</ul>
<a name="max-java.util.Collection-java.util.Comparator-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>max</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;T&nbsp;max(java.util.Collection&lt;? extends T&gt;&nbsp;coll,
                        java.util.Comparator&lt;? super T&gt;&nbsp;comp)</pre>
<div class="block">返回无序集合中的最大值</div>
</li>
</ul>
<a name="minAndMax-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>minAndMax</h4>
<pre>public static&nbsp;&lt;T extends java.lang.Object &amp; java.lang.Comparable&lt;? super T&gt;&gt;&nbsp;<a href="../../../../../com/huazheng/tunny/tools/base/type/Pair.html" title="com.huazheng.tunny.tools.base.type中的类">Pair</a>&lt;T,T&gt;&nbsp;minAndMax(java.util.Collection&lt;? extends T&gt;&nbsp;coll)</pre>
<div class="block">同时返回无序集合中的最小值和最大值，使用元素默认排序
 
 在返回的Pair中，第一个为最小值，第二个为最大值</div>
</li>
</ul>
<a name="minAndMax-java.util.Collection-java.util.Comparator-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>minAndMax</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;<a href="../../../../../com/huazheng/tunny/tools/base/type/Pair.html" title="com.huazheng.tunny.tools.base.type中的类">Pair</a>&lt;T,T&gt;&nbsp;minAndMax(java.util.Collection&lt;? extends T&gt;&nbsp;coll,
                                      java.util.Comparator&lt;? super T&gt;&nbsp;comp)</pre>
<div class="block">返回无序集合中的最小值和最大值
 
 在返回的Pair中，第一个为最小值，第二个为最大值</div>
</li>
</ul>
<a name="topN-java.lang.Iterable-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>topN</h4>
<pre>public static&nbsp;&lt;T extends java.lang.Comparable&lt;?&gt;&gt;&nbsp;java.util.List&lt;T&gt;&nbsp;topN(java.lang.Iterable&lt;T&gt;&nbsp;coll,
                                                                         int&nbsp;n)</pre>
<div class="block">返回Iterable中最大的N个对象, back by guava.</div>
</li>
</ul>
<a name="topN-java.lang.Iterable-int-java.util.Comparator-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>topN</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;java.util.List&lt;T&gt;&nbsp;topN(java.lang.Iterable&lt;T&gt;&nbsp;coll,
                                         int&nbsp;n,
                                         java.util.Comparator&lt;? super T&gt;&nbsp;comp)</pre>
<div class="block">返回Iterable中最大的N个对象, back by guava.</div>
</li>
</ul>
<a name="bottomN-java.lang.Iterable-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>bottomN</h4>
<pre>public static&nbsp;&lt;T extends java.lang.Comparable&lt;?&gt;&gt;&nbsp;java.util.List&lt;T&gt;&nbsp;bottomN(java.lang.Iterable&lt;T&gt;&nbsp;coll,
                                                                            int&nbsp;n)</pre>
<div class="block">返回Iterable中最小的N个对象, back by guava.</div>
</li>
</ul>
<a name="bottomN-java.lang.Iterable-int-java.util.Comparator-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>bottomN</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;java.util.List&lt;T&gt;&nbsp;bottomN(java.lang.Iterable&lt;T&gt;&nbsp;coll,
                                            int&nbsp;n,
                                            java.util.Comparator&lt;? super T&gt;&nbsp;comp)</pre>
<div class="block">返回Iterable中最小的N个对象, back by guava.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/collection/ArrayUtilTest.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/collection/CollectionUtilTest.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/collection/CollectionUtil.html" target="_top">框架</a></li>
<li><a href="CollectionUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
