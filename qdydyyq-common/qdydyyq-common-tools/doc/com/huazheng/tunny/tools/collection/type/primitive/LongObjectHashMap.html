<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:31 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>LongObjectHashMap</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="LongObjectHashMap";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/IntObjectMap.PrimitiveEntry.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../../index.html?com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" target="_top">框架</a></li>
<li><a href="LongObjectHashMap.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.collection.type.primitive</div>
<h2 title="类 LongObjectHashMap" class="title">类 LongObjectHashMap&lt;V&gt;</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.collection.type.primitive.LongObjectHashMap&lt;V&gt;</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt><span class="paramLabel">类型参数:</span></dt>
<dd><code>V</code> - The value type stored in the map.</dd>
</dl>
<dl>
<dt>所有已实现的接口:</dt>
<dd><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口">LongObjectMap</a>&lt;V&gt;, java.util.Map&lt;java.lang.Long,V&gt;</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">LongObjectHashMap&lt;V&gt;</span>
extends java.lang.Object
implements <a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口">LongObjectMap</a>&lt;V&gt;</pre>
<div class="block">移植Netty 4.1.6的Key为原子类型的集合类, 在数据结构上与HashMap不一样，空间占用与读写性能俱比原来更优.
 
 原子类型集合类有多个实现，选择Netty是因为有在实战中使用.
 
 A hash map implementation of <a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口"><code>LongObjectMap</code></a> that uses open addressing for keys. To minimize the memory
 footprint, this class uses open addressing rather than chaining. Collisions are resolved using linear probing.
 Deletions implement compaction, so cost of remove can approach O(N) for full maps, which makes a small loadFactor
 recommended.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>嵌套类概要</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.huazheng.tunny.tools.collection.type.primitive.LongObjectMap">
<!--   -->
</a>
<h3>从接口继承的嵌套类/接口&nbsp;com.huazheng.tunny.tools.collection.type.primitive.<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口">LongObjectMap</a></h3>
<code><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.PrimitiveEntry.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口">LongObjectMap.PrimitiveEntry</a>&lt;<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.PrimitiveEntry.html" title="LongObjectMap.PrimitiveEntry中的类型参数">V</a>&gt;</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.java.util.Map">
<!--   -->
</a>
<h3>从接口继承的嵌套类/接口&nbsp;java.util.Map</h3>
<code>java.util.Map.Entry&lt;K,V&gt;</code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#DEFAULT_CAPACITY">DEFAULT_CAPACITY</a></span></code>
<div class="block">Default initial capacity.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#DEFAULT_LOAD_FACTOR">DEFAULT_LOAD_FACTOR</a></span></code>
<div class="block">Default load factor.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#LongObjectHashMap--">LongObjectHashMap</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#LongObjectHashMap-int-">LongObjectHashMap</a></span>(int&nbsp;initialCapacity)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#LongObjectHashMap-int-float-">LongObjectHashMap</a></span>(int&nbsp;initialCapacity,
                 float&nbsp;loadFactor)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#clear--">clear</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#containsKey-long-">containsKey</a></span>(long&nbsp;key)</code>
<div class="block">Indicates whether or not this map contains a value for the specified key.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#containsKey-java.lang.Object-">containsKey</a></span>(java.lang.Object&nbsp;key)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#containsValue-java.lang.Object-">containsValue</a></span>(java.lang.Object&nbsp;value)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.Iterable&lt;<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.PrimitiveEntry.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口">LongObjectMap.PrimitiveEntry</a>&lt;<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&gt;&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#entries--">entries</a></span>()</code>
<div class="block">Gets an iterable to traverse over the primitive entries contained in this map.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.util.Set&lt;java.util.Map.Entry&lt;java.lang.Long,<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&gt;&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#entrySet--">entrySet</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#equals-java.lang.Object-">equals</a></span>(java.lang.Object&nbsp;obj)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#get-long-">get</a></span>(long&nbsp;key)</code>
<div class="block">Gets the value in the map with the specified key.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#get-java.lang.Object-">get</a></span>(java.lang.Object&nbsp;key)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#hashCode--">hashCode</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#isEmpty--">isEmpty</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>java.util.Set&lt;java.lang.Long&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#keySet--">keySet</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>protected java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#keyToString-long-">keyToString</a></span>(long&nbsp;key)</code>
<div class="block">Helper method called by <a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#toString--"><code>toString()</code></a> in order to convert a single map key into a string.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#put-long-V-">put</a></span>(long&nbsp;key,
   <a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&nbsp;value)</code>
<div class="block">Puts the given entry into the map.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#put-java.lang.Long-V-">put</a></span>(java.lang.Long&nbsp;key,
   <a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&nbsp;value)</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#putAll-java.util.Map-">putAll</a></span>(java.util.Map&lt;? extends java.lang.Long,? extends <a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&gt;&nbsp;sourceMap)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#remove-long-">remove</a></span>(long&nbsp;key)</code>
<div class="block">Removes the entry with the specified key.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#remove-java.lang.Object-">remove</a></span>(java.lang.Object&nbsp;key)</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#size--">size</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#toString--">toString</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>java.util.Collection&lt;<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#values--">values</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, finalize, getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.Map">
<!--   -->
</a>
<h3>从接口继承的方法&nbsp;java.util.Map</h3>
<code>compute, computeIfAbsent, computeIfPresent, forEach, getOrDefault, merge, putIfAbsent, remove, replace, replace, replaceAll</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="DEFAULT_CAPACITY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DEFAULT_CAPACITY</h4>
<pre>public static final&nbsp;int DEFAULT_CAPACITY</pre>
<div class="block">Default initial capacity. Used if not specified in the constructor</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../../../constant-values.html#com.huazheng.tunny.tools.collection.type.primitive.LongObjectHashMap.DEFAULT_CAPACITY">常量字段值</a></dd>
</dl>
</li>
</ul>
<a name="DEFAULT_LOAD_FACTOR">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DEFAULT_LOAD_FACTOR</h4>
<pre>public static final&nbsp;float DEFAULT_LOAD_FACTOR</pre>
<div class="block">Default load factor. Used if not specified in the constructor</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../../../constant-values.html#com.huazheng.tunny.tools.collection.type.primitive.LongObjectHashMap.DEFAULT_LOAD_FACTOR">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="LongObjectHashMap--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LongObjectHashMap</h4>
<pre>public&nbsp;LongObjectHashMap()</pre>
</li>
</ul>
<a name="LongObjectHashMap-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LongObjectHashMap</h4>
<pre>public&nbsp;LongObjectHashMap(int&nbsp;initialCapacity)</pre>
</li>
</ul>
<a name="LongObjectHashMap-int-float-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>LongObjectHashMap</h4>
<pre>public&nbsp;LongObjectHashMap(int&nbsp;initialCapacity,
                         float&nbsp;loadFactor)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="get-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get</h4>
<pre>public&nbsp;<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&nbsp;get(long&nbsp;key)</pre>
<div class="block"><span class="descfrmTypeLabel">从接口复制的说明:&nbsp;<code><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html#get-long-">LongObjectMap</a></code></span></div>
<div class="block">Gets the value in the map with the specified key.</div>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html#get-long-">get</a></code>&nbsp;在接口中&nbsp;<code><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口">LongObjectMap</a>&lt;<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&gt;</code></dd>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>key</code> - the key whose associated value is to be returned.</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>the value or <code>null</code> if the key was not found in the map.</dd>
</dl>
</li>
</ul>
<a name="put-long-java.lang.Object-">
<!--   -->
</a><a name="put-long-V-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>put</h4>
<pre>public&nbsp;<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&nbsp;put(long&nbsp;key,
             <a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&nbsp;value)</pre>
<div class="block"><span class="descfrmTypeLabel">从接口复制的说明:&nbsp;<code><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html#put-long-V-">LongObjectMap</a></code></span></div>
<div class="block">Puts the given entry into the map.</div>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html#put-long-V-">put</a></code>&nbsp;在接口中&nbsp;<code><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口">LongObjectMap</a>&lt;<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&gt;</code></dd>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>key</code> - the key of the entry.</dd>
<dd><code>value</code> - the value of the entry.</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>the previous value for this key or <code>null</code> if there was no previous mapping.</dd>
</dl>
</li>
</ul>
<a name="putAll-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>putAll</h4>
<pre>public&nbsp;void&nbsp;putAll(java.util.Map&lt;? extends java.lang.Long,? extends <a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&gt;&nbsp;sourceMap)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>putAll</code>&nbsp;在接口中&nbsp;<code>java.util.Map&lt;java.lang.Long,<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="remove-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remove</h4>
<pre>public&nbsp;<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&nbsp;remove(long&nbsp;key)</pre>
<div class="block"><span class="descfrmTypeLabel">从接口复制的说明:&nbsp;<code><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html#remove-long-">LongObjectMap</a></code></span></div>
<div class="block">Removes the entry with the specified key.</div>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html#remove-long-">remove</a></code>&nbsp;在接口中&nbsp;<code><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口">LongObjectMap</a>&lt;<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&gt;</code></dd>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>key</code> - the key for the entry to be removed from this map.</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>the previous value for the key, or <code>null</code> if there was no mapping.</dd>
</dl>
</li>
</ul>
<a name="size--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>size</h4>
<pre>public&nbsp;int&nbsp;size()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>size</code>&nbsp;在接口中&nbsp;<code>java.util.Map&lt;java.lang.Long,<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="isEmpty--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEmpty</h4>
<pre>public&nbsp;boolean&nbsp;isEmpty()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>isEmpty</code>&nbsp;在接口中&nbsp;<code>java.util.Map&lt;java.lang.Long,<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="clear--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clear</h4>
<pre>public&nbsp;void&nbsp;clear()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>clear</code>&nbsp;在接口中&nbsp;<code>java.util.Map&lt;java.lang.Long,<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="containsKey-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>containsKey</h4>
<pre>public&nbsp;boolean&nbsp;containsKey(long&nbsp;key)</pre>
<div class="block"><span class="descfrmTypeLabel">从接口复制的说明:&nbsp;<code><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html#containsKey-long-">LongObjectMap</a></code></span></div>
<div class="block">Indicates whether or not this map contains a value for the specified key.</div>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html#containsKey-long-">containsKey</a></code>&nbsp;在接口中&nbsp;<code><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口">LongObjectMap</a>&lt;<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="containsValue-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>containsValue</h4>
<pre>public&nbsp;boolean&nbsp;containsValue(java.lang.Object&nbsp;value)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>containsValue</code>&nbsp;在接口中&nbsp;<code>java.util.Map&lt;java.lang.Long,<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="entries--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>entries</h4>
<pre>public&nbsp;java.lang.Iterable&lt;<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.PrimitiveEntry.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口">LongObjectMap.PrimitiveEntry</a>&lt;<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&gt;&gt;&nbsp;entries()</pre>
<div class="block"><span class="descfrmTypeLabel">从接口复制的说明:&nbsp;<code><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html#entries--">LongObjectMap</a></code></span></div>
<div class="block">Gets an iterable to traverse over the primitive entries contained in this map. As an optimization, the
 <a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.PrimitiveEntry.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口"><code>LongObjectMap.PrimitiveEntry</code></a>s returned by the <code>Iterator</code> may change as the <code>Iterator</code> progresses. The
 caller should not rely on <a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.PrimitiveEntry.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口"><code>LongObjectMap.PrimitiveEntry</code></a> key/value stability.</div>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html#entries--">entries</a></code>&nbsp;在接口中&nbsp;<code><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口">LongObjectMap</a>&lt;<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="values--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>values</h4>
<pre>public&nbsp;java.util.Collection&lt;<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&gt;&nbsp;values()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>values</code>&nbsp;在接口中&nbsp;<code>java.util.Map&lt;java.lang.Long,<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="hashCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hashCode</h4>
<pre>public&nbsp;int&nbsp;hashCode()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>hashCode</code>&nbsp;在接口中&nbsp;<code>java.util.Map&lt;java.lang.Long,<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>hashCode</code>&nbsp;在类中&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>equals</h4>
<pre>public&nbsp;boolean&nbsp;equals(java.lang.Object&nbsp;obj)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>equals</code>&nbsp;在接口中&nbsp;<code>java.util.Map&lt;java.lang.Long,<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>equals</code>&nbsp;在类中&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
<a name="containsKey-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>containsKey</h4>
<pre>public&nbsp;boolean&nbsp;containsKey(java.lang.Object&nbsp;key)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>containsKey</code>&nbsp;在接口中&nbsp;<code>java.util.Map&lt;java.lang.Long,<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="get-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get</h4>
<pre>public&nbsp;<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&nbsp;get(java.lang.Object&nbsp;key)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>get</code>&nbsp;在接口中&nbsp;<code>java.util.Map&lt;java.lang.Long,<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="put-java.lang.Long-java.lang.Object-">
<!--   -->
</a><a name="put-java.lang.Long-V-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>put</h4>
<pre>public&nbsp;<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&nbsp;put(java.lang.Long&nbsp;key,
             <a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&nbsp;value)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>put</code>&nbsp;在接口中&nbsp;<code>java.util.Map&lt;java.lang.Long,<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="remove-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remove</h4>
<pre>public&nbsp;<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&nbsp;remove(java.lang.Object&nbsp;key)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>remove</code>&nbsp;在接口中&nbsp;<code>java.util.Map&lt;java.lang.Long,<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="keySet--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>keySet</h4>
<pre>public&nbsp;java.util.Set&lt;java.lang.Long&gt;&nbsp;keySet()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>keySet</code>&nbsp;在接口中&nbsp;<code>java.util.Map&lt;java.lang.Long,<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="entrySet--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>entrySet</h4>
<pre>public&nbsp;java.util.Set&lt;java.util.Map.Entry&lt;java.lang.Long,<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&gt;&gt;&nbsp;entrySet()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>entrySet</code>&nbsp;在接口中&nbsp;<code>java.util.Map&lt;java.lang.Long,<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="LongObjectHashMap中的类型参数">V</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;java.lang.String&nbsp;toString()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>toString</code>&nbsp;在类中&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
<a name="keyToString-long-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>keyToString</h4>
<pre>protected&nbsp;java.lang.String&nbsp;keyToString(long&nbsp;key)</pre>
<div class="block">Helper method called by <a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html#toString--"><code>toString()</code></a> in order to convert a single map key into a string. This is protected
 to allow subclasses to override the appearance of a given key.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/IntObjectMap.PrimitiveEntry.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../../index.html?com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" target="_top">框架</a></li>
<li><a href="LongObjectHashMap.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
