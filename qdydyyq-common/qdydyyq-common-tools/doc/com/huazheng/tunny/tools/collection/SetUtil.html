<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:35 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>SetUtil</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SetUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9,"i14":9,"i15":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/collection/QueueUtilTest.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/collection/SetUtilTest.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/collection/SetUtil.html" target="_top">框架</a></li>
<li><a href="SetUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.collection</div>
<h2 title="类 SetUtil" class="title">类 SetUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.collection.SetUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">SetUtil</span>
extends java.lang.Object</pre>
<div class="block">关于Set的工具集合.
 
 1. 各种Set的创建函数, 包括ConcurrentHashSet
 
 2. 集合运算函数(交集，并集等,from guava)</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/SetUtil.html#SetUtil--">SetUtil</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static &lt;E&gt;&nbsp;java.util.Set&lt;E&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/SetUtil.html#differenceView-java.util.Set-java.util.Set-">differenceView</a></span>(java.util.Set&lt;E&gt;&nbsp;set1,
              java.util.Set&lt;?&gt;&nbsp;set2)</code>
<div class="block">set1, set2的差集（在set1，不在set2中的对象）的只读view，不复制产生新的Set对象.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static &lt;E&gt;&nbsp;java.util.Set&lt;E&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/SetUtil.html#disjointView-java.util.Set-java.util.Set-">disjointView</a></span>(java.util.Set&lt;? extends E&gt;&nbsp;set1,
            java.util.Set&lt;? extends E&gt;&nbsp;set2)</code>
<div class="block">set1, set2的补集（在set1或set2中，但不在交集中的对象，又叫反交集）的只读view，不复制产生新的Set对象.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.Set&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/SetUtil.html#emptySet--">emptySet</a></span>()</code>
<div class="block">返回一个空的结构特殊的Set，节约空间.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.Set&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/SetUtil.html#emptySetIfNull-java.util.Set-">emptySetIfNull</a></span>(java.util.Set&lt;T&gt;&nbsp;set)</code>
<div class="block">如果set为null，转化为一个安全的空Set.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static &lt;E&gt;&nbsp;java.util.Set&lt;E&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/SetUtil.html#intersectionView-java.util.Set-java.util.Set-">intersectionView</a></span>(java.util.Set&lt;E&gt;&nbsp;set1,
                java.util.Set&lt;?&gt;&nbsp;set2)</code>
<div class="block">set1, set2的交集（同时在set1和set2的对象）的只读view，不复制产生新的Set对象.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;<a href="../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="com.huazheng.tunny.tools.collection.type中的类">ConcurrentHashSet</a>&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/SetUtil.html#newConcurrentHashSet--">newConcurrentHashSet</a></span>()</code>
<div class="block">根据等号左边的类型，构造类型正确的ConcurrentHashSet</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.HashSet&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/SetUtil.html#newHashSet--">newHashSet</a></span>()</code>
<div class="block">根据等号左边的类型，构造类型正确的HashSet.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.HashSet&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/SetUtil.html#newHashSet-java.lang.Iterable-">newHashSet</a></span>(java.lang.Iterable&lt;? extends T&gt;&nbsp;elements)</code>
<div class="block">HashSet涉及HashMap大小，因此建议在构造时传入需要初始的集合，其他如TreeSet不需要.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.HashSet&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/SetUtil.html#newHashSet-T...-">newHashSet</a></span>(T...&nbsp;elements)</code>
<div class="block">根据等号左边的类型，构造类型正确的HashSet, 并初始化元素.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.HashSet&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/SetUtil.html#newHashSetWithCapacity-int-">newHashSetWithCapacity</a></span>(int&nbsp;expectedSize)</code>
<div class="block">创建HashSet并设置初始大小，因为HashSet内部是HashMap，会计算LoadFactor后的真实大小.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.Set&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/SetUtil.html#newSetFromMap-java.util.Map-">newSetFromMap</a></span>(java.util.Map&lt;T,java.lang.Boolean&gt;&nbsp;map)</code>
<div class="block">从Map构造Set的大杀器, 可以用来制造各种Set</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static &lt;T extends java.lang.Comparable&gt;<br>java.util.TreeSet&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/SetUtil.html#newSortedSet--">newSortedSet</a></span>()</code>
<div class="block">根据等号左边的类型，构造类型正确的TreeSet, 通过实现了Comparable的元素自身进行排序.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.TreeSet&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/SetUtil.html#newSortedSet-java.util.Comparator-">newSortedSet</a></span>(java.util.Comparator&lt;? super T&gt;&nbsp;comparator)</code>
<div class="block">根据等号左边的类型，构造类型正确的TreeSet, 并设置comparator.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.Set&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/SetUtil.html#singletonSet-T-">singletonSet</a></span>(T&nbsp;o)</code>
<div class="block">返回只含一个元素但结构特殊的Set，节约空间.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static &lt;E&gt;&nbsp;java.util.Set&lt;E&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/SetUtil.html#unionView-java.util.Set-java.util.Set-">unionView</a></span>(java.util.Set&lt;? extends E&gt;&nbsp;set1,
         java.util.Set&lt;? extends E&gt;&nbsp;set2)</code>
<div class="block">set1, set2的并集（在set1或set2的对象）的只读view，不复制产生新的Set对象.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.Set&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/SetUtil.html#unmodifiableSet-java.util.Set-">unmodifiableSet</a></span>(java.util.Set&lt;? extends T&gt;&nbsp;s)</code>
<div class="block">返回包装后不可修改的Set.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="SetUtil--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>SetUtil</h4>
<pre>public&nbsp;SetUtil()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="newHashSet--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newHashSet</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;java.util.HashSet&lt;T&gt;&nbsp;newHashSet()</pre>
<div class="block">根据等号左边的类型，构造类型正确的HashSet.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Sets.newHashSet()</code></dd>
</dl>
</li>
</ul>
<a name="newHashSet-java.lang.Object:A-">
<!--   -->
</a><a name="newHashSet-T...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newHashSet</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;java.util.HashSet&lt;T&gt;&nbsp;newHashSet(T...&nbsp;elements)</pre>
<div class="block">根据等号左边的类型，构造类型正确的HashSet, 并初始化元素.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Sets.newHashSet(Object...)</code></dd>
</dl>
</li>
</ul>
<a name="newHashSet-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newHashSet</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;java.util.HashSet&lt;T&gt;&nbsp;newHashSet(java.lang.Iterable&lt;? extends T&gt;&nbsp;elements)</pre>
<div class="block">HashSet涉及HashMap大小，因此建议在构造时传入需要初始的集合，其他如TreeSet不需要.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Sets.newHashSet(Iterable)</code></dd>
</dl>
</li>
</ul>
<a name="newHashSetWithCapacity-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newHashSetWithCapacity</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;java.util.HashSet&lt;T&gt;&nbsp;newHashSetWithCapacity(int&nbsp;expectedSize)</pre>
<div class="block">创建HashSet并设置初始大小，因为HashSet内部是HashMap，会计算LoadFactor后的真实大小.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Sets.newHashSetWithExpectedSize(int)</code></dd>
</dl>
</li>
</ul>
<a name="newSortedSet--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newSortedSet</h4>
<pre>public static&nbsp;&lt;T extends java.lang.Comparable&gt;&nbsp;java.util.TreeSet&lt;T&gt;&nbsp;newSortedSet()</pre>
<div class="block">根据等号左边的类型，构造类型正确的TreeSet, 通过实现了Comparable的元素自身进行排序.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Sets.newTreeSet()</code></dd>
</dl>
</li>
</ul>
<a name="newSortedSet-java.util.Comparator-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newSortedSet</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;java.util.TreeSet&lt;T&gt;&nbsp;newSortedSet(java.util.Comparator&lt;? super T&gt;&nbsp;comparator)</pre>
<div class="block">根据等号左边的类型，构造类型正确的TreeSet, 并设置comparator.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Sets.newTreeSet(Comparator)</code></dd>
</dl>
</li>
</ul>
<a name="newConcurrentHashSet--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newConcurrentHashSet</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;<a href="../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="com.huazheng.tunny.tools.collection.type中的类">ConcurrentHashSet</a>&lt;T&gt;&nbsp;newConcurrentHashSet()</pre>
<div class="block">根据等号左边的类型，构造类型正确的ConcurrentHashSet</div>
</li>
</ul>
<a name="emptySet--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>emptySet</h4>
<pre>public static final&nbsp;&lt;T&gt;&nbsp;java.util.Set&lt;T&gt;&nbsp;emptySet()</pre>
<div class="block">返回一个空的结构特殊的Set，节约空间.
 
 注意返回的Set不可写, 写入会抛出UnsupportedOperationException.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Collections.emptySet()</code></dd>
</dl>
</li>
</ul>
<a name="emptySetIfNull-java.util.Set-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>emptySetIfNull</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;java.util.Set&lt;T&gt;&nbsp;emptySetIfNull(java.util.Set&lt;T&gt;&nbsp;set)</pre>
<div class="block">如果set为null，转化为一个安全的空Set.
 
 注意返回的Set不可写, 写入会抛出UnsupportedOperationException.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Collections.emptySet()</code></dd>
</dl>
</li>
</ul>
<a name="singletonSet-java.lang.Object-">
<!--   -->
</a><a name="singletonSet-T-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>singletonSet</h4>
<pre>public static final&nbsp;&lt;T&gt;&nbsp;java.util.Set&lt;T&gt;&nbsp;singletonSet(T&nbsp;o)</pre>
<div class="block">返回只含一个元素但结构特殊的Set，节约空间.
 
 注意返回的Set不可写, 写入会抛出UnsupportedOperationException.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Collections.singleton(Object)</code></dd>
</dl>
</li>
</ul>
<a name="unmodifiableSet-java.util.Set-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unmodifiableSet</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;java.util.Set&lt;T&gt;&nbsp;unmodifiableSet(java.util.Set&lt;? extends T&gt;&nbsp;s)</pre>
<div class="block">返回包装后不可修改的Set.
 
 如果尝试修改，会抛出UnsupportedOperationException</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Collections.unmodifiableSet(Set)</code></dd>
</dl>
</li>
</ul>
<a name="newSetFromMap-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newSetFromMap</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;java.util.Set&lt;T&gt;&nbsp;newSetFromMap(java.util.Map&lt;T,java.lang.Boolean&gt;&nbsp;map)</pre>
<div class="block">从Map构造Set的大杀器, 可以用来制造各种Set</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Collections.newSetFromMap(Map)</code></dd>
</dl>
</li>
</ul>
<a name="unionView-java.util.Set-java.util.Set-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unionView</h4>
<pre>public static&nbsp;&lt;E&gt;&nbsp;java.util.Set&lt;E&gt;&nbsp;unionView(java.util.Set&lt;? extends E&gt;&nbsp;set1,
                                             java.util.Set&lt;? extends E&gt;&nbsp;set2)</pre>
<div class="block">set1, set2的并集（在set1或set2的对象）的只读view，不复制产生新的Set对象.
 
 如果尝试写入该View会抛出UnsupportedOperationException</div>
</li>
</ul>
<a name="intersectionView-java.util.Set-java.util.Set-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>intersectionView</h4>
<pre>public static&nbsp;&lt;E&gt;&nbsp;java.util.Set&lt;E&gt;&nbsp;intersectionView(java.util.Set&lt;E&gt;&nbsp;set1,
                                                    java.util.Set&lt;?&gt;&nbsp;set2)</pre>
<div class="block">set1, set2的交集（同时在set1和set2的对象）的只读view，不复制产生新的Set对象.
 
 如果尝试写入该View会抛出UnsupportedOperationException</div>
</li>
</ul>
<a name="differenceView-java.util.Set-java.util.Set-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>differenceView</h4>
<pre>public static&nbsp;&lt;E&gt;&nbsp;java.util.Set&lt;E&gt;&nbsp;differenceView(java.util.Set&lt;E&gt;&nbsp;set1,
                                                  java.util.Set&lt;?&gt;&nbsp;set2)</pre>
<div class="block">set1, set2的差集（在set1，不在set2中的对象）的只读view，不复制产生新的Set对象.
 
 如果尝试写入该View会抛出UnsupportedOperationException</div>
</li>
</ul>
<a name="disjointView-java.util.Set-java.util.Set-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>disjointView</h4>
<pre>public static&nbsp;&lt;E&gt;&nbsp;java.util.Set&lt;E&gt;&nbsp;disjointView(java.util.Set&lt;? extends E&gt;&nbsp;set1,
                                                java.util.Set&lt;? extends E&gt;&nbsp;set2)</pre>
<div class="block">set1, set2的补集（在set1或set2中，但不在交集中的对象，又叫反交集）的只读view，不复制产生新的Set对象.
 
 如果尝试写入该View会抛出UnsupportedOperationException</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/collection/QueueUtilTest.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/collection/SetUtilTest.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/collection/SetUtil.html" target="_top">框架</a></li>
<li><a href="SetUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
