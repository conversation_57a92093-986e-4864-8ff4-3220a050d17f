<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:31 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>LongObjectMap</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="LongObjectMap";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.PrimitiveEntry.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../../index.html?com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html" target="_top">框架</a></li>
<li><a href="LongObjectMap.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.collection.type.primitive</div>
<h2 title="接口 LongObjectMap" class="title">接口 LongObjectMap&lt;V&gt;</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt><span class="paramLabel">类型参数:</span></dt>
<dd><code>V</code> - the value type stored in the map.</dd>
</dl>
<dl>
<dt>所有超级接口:</dt>
<dd>java.util.Map&lt;java.lang.Long,V&gt;</dd>
</dl>
<dl>
<dt>所有已知实现类:</dt>
<dd><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">LongObjectHashMap</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">LongObjectMap&lt;V&gt;</span>
extends java.util.Map&lt;java.lang.Long,V&gt;</pre>
<div class="block">Interface for a primitive map that uses <code>long</code>s as keys.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>嵌套类概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="嵌套类概要表, 列表嵌套类和解释">
<caption><span>嵌套类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">接口和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.PrimitiveEntry.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口">LongObjectMap.PrimitiveEntry</a>&lt;<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.PrimitiveEntry.html" title="LongObjectMap.PrimitiveEntry中的类型参数">V</a>&gt;</span></code>
<div class="block">A primitive entry in the map, provided by the iterator from <a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html#entries--"><code>entries()</code></a></div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.java.util.Map">
<!--   -->
</a>
<h3>从接口继承的嵌套类/接口&nbsp;java.util.Map</h3>
<code>java.util.Map.Entry&lt;K,V&gt;</code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html#containsKey-long-">containsKey</a></span>(long&nbsp;key)</code>
<div class="block">Indicates whether or not this map contains a value for the specified key.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.Iterable&lt;<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.PrimitiveEntry.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口">LongObjectMap.PrimitiveEntry</a>&lt;<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html" title="LongObjectMap中的类型参数">V</a>&gt;&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html#entries--">entries</a></span>()</code>
<div class="block">Gets an iterable to traverse over the primitive entries contained in this map.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html" title="LongObjectMap中的类型参数">V</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html#get-long-">get</a></span>(long&nbsp;key)</code>
<div class="block">Gets the value in the map with the specified key.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html" title="LongObjectMap中的类型参数">V</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html#put-long-V-">put</a></span>(long&nbsp;key,
   <a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html" title="LongObjectMap中的类型参数">V</a>&nbsp;value)</code>
<div class="block">Puts the given entry into the map.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html" title="LongObjectMap中的类型参数">V</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html#remove-long-">remove</a></span>(long&nbsp;key)</code>
<div class="block">Removes the entry with the specified key.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.Map">
<!--   -->
</a>
<h3>从接口继承的方法&nbsp;java.util.Map</h3>
<code>clear, compute, computeIfAbsent, computeIfPresent, containsKey, containsValue, entrySet, equals, forEach, get, getOrDefault, hashCode, isEmpty, keySet, merge, put, putAll, putIfAbsent, remove, remove, replace, replace, replaceAll, size, values</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="get-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get</h4>
<pre><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html" title="LongObjectMap中的类型参数">V</a>&nbsp;get(long&nbsp;key)</pre>
<div class="block">Gets the value in the map with the specified key.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>key</code> - the key whose associated value is to be returned.</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>the value or <code>null</code> if the key was not found in the map.</dd>
</dl>
</li>
</ul>
<a name="put-long-java.lang.Object-">
<!--   -->
</a><a name="put-long-V-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>put</h4>
<pre><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html" title="LongObjectMap中的类型参数">V</a>&nbsp;put(long&nbsp;key,
      <a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html" title="LongObjectMap中的类型参数">V</a>&nbsp;value)</pre>
<div class="block">Puts the given entry into the map.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>key</code> - the key of the entry.</dd>
<dd><code>value</code> - the value of the entry.</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>the previous value for this key or <code>null</code> if there was no previous mapping.</dd>
</dl>
</li>
</ul>
<a name="remove-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remove</h4>
<pre><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html" title="LongObjectMap中的类型参数">V</a>&nbsp;remove(long&nbsp;key)</pre>
<div class="block">Removes the entry with the specified key.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>key</code> - the key for the entry to be removed from this map.</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>the previous value for the key, or <code>null</code> if there was no mapping.</dd>
</dl>
</li>
</ul>
<a name="entries--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>entries</h4>
<pre>java.lang.Iterable&lt;<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.PrimitiveEntry.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口">LongObjectMap.PrimitiveEntry</a>&lt;<a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html" title="LongObjectMap中的类型参数">V</a>&gt;&gt;&nbsp;entries()</pre>
<div class="block">Gets an iterable to traverse over the primitive entries contained in this map. As an optimization, the
 <a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.PrimitiveEntry.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口"><code>LongObjectMap.PrimitiveEntry</code></a>s returned by the <code>Iterator</code> may change as the <code>Iterator</code> progresses. The
 caller should not rely on <a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.PrimitiveEntry.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口"><code>LongObjectMap.PrimitiveEntry</code></a> key/value stability.</div>
</li>
</ul>
<a name="containsKey-long-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>containsKey</h4>
<pre>boolean&nbsp;containsKey(long&nbsp;key)</pre>
<div class="block">Indicates whether or not this map contains a value for the specified key.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.PrimitiveEntry.html" title="com.huazheng.tunny.tools.collection.type.primitive中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../../index.html?com/huazheng/tunny/tools/collection/type/primitive/LongObjectMap.html" target="_top">框架</a></li>
<li><a href="LongObjectMap.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li>构造器&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
