<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:34 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ArrayUtil</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ArrayUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../com/huazheng/tunny/tools/collection/ArrayUtilTest.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/collection/ArrayUtil.html" target="_top">框架</a></li>
<li><a href="ArrayUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.collection</div>
<h2 title="类 ArrayUtil" class="title">类 ArrayUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.collection.ArrayUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ArrayUtil</span>
extends java.lang.Object</pre>
<div class="block">数组工具类.
 
 1. 创建Array的函数
 
 2. 数组的乱序与contact相加
 
 3. 从Array转换到Guava的底层为原子类型的List
 
 JDK Arrays的其他函数，如sort(), toString() 请直接调用
 
 Common Lang ArrayUtils的其他函数，如subarray(),reverse(),indexOf(), 请直接调用</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ArrayUtil.html#ArrayUtil--">ArrayUtil</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.List&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ArrayUtil.html#asList-T...-">asList</a></span>(T...&nbsp;a)</code>
<div class="block">原版将数组转换为List.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;T[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ArrayUtil.html#concat-T:A-T-">concat</a></span>(T[]&nbsp;array,
      T&nbsp;element)</code>
<div class="block">添加元素到数组末尾.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;T[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ArrayUtil.html#concat-T-T:A-">concat</a></span>(T&nbsp;element,
      T[]&nbsp;array)</code>
<div class="block">添加元素到数组头.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static java.util.List&lt;java.lang.Double&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ArrayUtil.html#doubleAsList-double...-">doubleAsList</a></span>(double...&nbsp;backingArray)</code>
<div class="block">Arrays.asList()的加强版, 返回一个底层为原始类型double的List
 
 与保存Double相比节约空间，同时也避免了AutoBoxing.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static java.util.List&lt;java.lang.Integer&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ArrayUtil.html#intAsList-int...-">intAsList</a></span>(int...&nbsp;backingArray)</code>
<div class="block">Arrays.asList()的加强版, 返回一个底层为原始类型int的List
 
 与保存Integer相比节约空间，同时只在读取数据时AutoBoxing.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static java.util.List&lt;java.lang.Long&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ArrayUtil.html#longAsList-long...-">longAsList</a></span>(long...&nbsp;backingArray)</code>
<div class="block">Arrays.asList()的加强版, 返回一个底层为原始类型long的List
 
 与保存Long相比节约空间，同时只在读取数据时AutoBoxing.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;T[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ArrayUtil.html#newArray-java.lang.Class-int-">newArray</a></span>(java.lang.Class&lt;T&gt;&nbsp;type,
        int&nbsp;length)</code>
<div class="block">传入类型与大小创建数组.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;T[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ArrayUtil.html#shuffle-T:A-">shuffle</a></span>(T[]&nbsp;array)</code>
<div class="block">将传入的数组乱序</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;T[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ArrayUtil.html#shuffle-T:A-java.util.Random-">shuffle</a></span>(T[]&nbsp;array,
       java.util.Random&nbsp;random)</code>
<div class="block">将传入的数组乱序</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;T[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ArrayUtil.html#toArray-java.util.Collection-java.lang.Class-">toArray</a></span>(java.util.Collection&lt;T&gt;&nbsp;col,
       java.lang.Class&lt;T&gt;&nbsp;type)</code>
<div class="block">从collection转为Array, 以 list.toArray(new String[0]); 最快 不需要创建list.size()的数组.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="ArrayUtil--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ArrayUtil</h4>
<pre>public&nbsp;ArrayUtil()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="newArray-java.lang.Class-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newArray</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;T[]&nbsp;newArray(java.lang.Class&lt;T&gt;&nbsp;type,
                               int&nbsp;length)</pre>
<div class="block">传入类型与大小创建数组.
 
 Array.newInstance()的性能并不差</div>
</li>
</ul>
<a name="toArray-java.util.Collection-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toArray</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;T[]&nbsp;toArray(java.util.Collection&lt;T&gt;&nbsp;col,
                              java.lang.Class&lt;T&gt;&nbsp;type)</pre>
<div class="block">从collection转为Array, 以 list.toArray(new String[0]); 最快 不需要创建list.size()的数组.
 
 本函数等价于list.toArray(new String[0]); 用户也可以直接用后者.
 
 https://shipilev.net/blog/2016/arrays-wisdom-ancients/</div>
</li>
</ul>
<a name="shuffle-java.lang.Object:A-">
<!--   -->
</a><a name="shuffle-T:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>shuffle</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;T[]&nbsp;shuffle(T[]&nbsp;array)</pre>
<div class="block">将传入的数组乱序</div>
</li>
</ul>
<a name="shuffle-java.lang.Object:A-java.util.Random-">
<!--   -->
</a><a name="shuffle-T:A-java.util.Random-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>shuffle</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;T[]&nbsp;shuffle(T[]&nbsp;array,
                              java.util.Random&nbsp;random)</pre>
<div class="block">将传入的数组乱序</div>
</li>
</ul>
<a name="concat-java.lang.Object-java.lang.Object:A-">
<!--   -->
</a><a name="concat-T-T:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>concat</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;T[]&nbsp;concat(T&nbsp;element,
                             T[]&nbsp;array)</pre>
<div class="block">添加元素到数组头.</div>
</li>
</ul>
<a name="concat-java.lang.Object:A-java.lang.Object-">
<!--   -->
</a><a name="concat-T:A-T-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>concat</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;T[]&nbsp;concat(T[]&nbsp;array,
                             T&nbsp;element)</pre>
<div class="block">添加元素到数组末尾.</div>
</li>
</ul>
<a name="asList-java.lang.Object:A-">
<!--   -->
</a><a name="asList-T...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>asList</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;java.util.List&lt;T&gt;&nbsp;asList(T...&nbsp;a)</pre>
<div class="block">原版将数组转换为List.
 
 注意转换后的List不能写入, 否则抛出UnsupportedOperationException</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Arrays.asList(Object...)</code></dd>
</dl>
</li>
</ul>
<a name="intAsList-int...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>intAsList</h4>
<pre>public static&nbsp;java.util.List&lt;java.lang.Integer&gt;&nbsp;intAsList(int...&nbsp;backingArray)</pre>
<div class="block">Arrays.asList()的加强版, 返回一个底层为原始类型int的List
 
 与保存Integer相比节约空间，同时只在读取数据时AutoBoxing.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Arrays.asList(Object...)</code>, 
<code>Ints.asList(int...)</code></dd>
</dl>
</li>
</ul>
<a name="longAsList-long...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>longAsList</h4>
<pre>public static&nbsp;java.util.List&lt;java.lang.Long&gt;&nbsp;longAsList(long...&nbsp;backingArray)</pre>
<div class="block">Arrays.asList()的加强版, 返回一个底层为原始类型long的List
 
 与保存Long相比节约空间，同时只在读取数据时AutoBoxing.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Arrays.asList(Object...)</code>, 
<code>Longs.asList(long...)</code></dd>
</dl>
</li>
</ul>
<a name="doubleAsList-double...-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>doubleAsList</h4>
<pre>public static&nbsp;java.util.List&lt;java.lang.Double&gt;&nbsp;doubleAsList(double...&nbsp;backingArray)</pre>
<div class="block">Arrays.asList()的加强版, 返回一个底层为原始类型double的List
 
 与保存Double相比节约空间，同时也避免了AutoBoxing.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Arrays.asList(Object...)</code>, 
<code>Doubles.asList(double...)</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../com/huazheng/tunny/tools/collection/ArrayUtilTest.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/collection/ArrayUtil.html" target="_top">框架</a></li>
<li><a href="ArrayUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
