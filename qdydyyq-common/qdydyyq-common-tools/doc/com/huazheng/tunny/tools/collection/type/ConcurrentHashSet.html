<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:34 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ConcurrentHashSet</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ConcurrentHashSet";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSetTest.html" title="com.huazheng.tunny.tools.collection.type中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" target="_top">框架</a></li>
<li><a href="ConcurrentHashSet.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.collection.type</div>
<h2 title="类 ConcurrentHashSet" class="title">类 ConcurrentHashSet&lt;E&gt;</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.util.AbstractCollection&lt;E&gt;</li>
<li>
<ul class="inheritance">
<li>java.util.AbstractSet&lt;E&gt;</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.collection.type.ConcurrentHashSet&lt;E&gt;</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd>java.io.Serializable, java.lang.Iterable&lt;E&gt;, java.util.Collection&lt;E&gt;, java.util.Set&lt;E&gt;</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">ConcurrentHashSet&lt;E&gt;</span>
extends java.util.AbstractSet&lt;E&gt;
implements java.util.Set&lt;E&gt;, java.io.Serializable</pre>
<div class="block">JDK并没有提供ConcurrenHashSet，考虑到JDK的HashSet也是基于HashMap实现的，因此ConcurrenHashSet也由ConcurrenHashMap完成。
 
 虽然也可以通过Collections.newSetFromMap(new ConcurrentHashMap())，
 
 但声明一个单独的类型，阅读代码时能更清晰的知道set的并发友好性，代码来自JDK的SetFromMap，去除JDK8接口.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../../serialized-form.html#com.huazheng.tunny.tools.collection.type.ConcurrentHashSet">序列化表格</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html#ConcurrentHashSet--">ConcurrentHashSet</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html#add-E-">add</a></span>(<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&nbsp;e)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html#clear--">clear</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html#contains-java.lang.Object-">contains</a></span>(java.lang.Object&nbsp;o)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html#containsAll-java.util.Collection-">containsAll</a></span>(java.util.Collection&lt;?&gt;&nbsp;c)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html#equals-java.lang.Object-">equals</a></span>(java.lang.Object&nbsp;o)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html#hashCode--">hashCode</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html#isEmpty--">isEmpty</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.util.Iterator&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html#iterator--">iterator</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html#remove-java.lang.Object-">remove</a></span>(java.lang.Object&nbsp;o)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html#removeAll-java.util.Collection-">removeAll</a></span>(java.util.Collection&lt;?&gt;&nbsp;c)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html#retainAll-java.util.Collection-">retainAll</a></span>(java.util.Collection&lt;?&gt;&nbsp;c)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html#size--">size</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>java.lang.Object[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html#toArray--">toArray</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>&lt;T&gt;&nbsp;T[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html#toArray-T:A-">toArray</a></span>(T[]&nbsp;a)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html#toString--">toString</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.AbstractCollection">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.util.AbstractCollection</h3>
<code>addAll</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, finalize, getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.Set">
<!--   -->
</a>
<h3>从接口继承的方法&nbsp;java.util.Set</h3>
<code>addAll, spliterator</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.Collection">
<!--   -->
</a>
<h3>从接口继承的方法&nbsp;java.util.Collection</h3>
<code>parallelStream, removeIf, stream</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Iterable">
<!--   -->
</a>
<h3>从接口继承的方法&nbsp;java.lang.Iterable</h3>
<code>forEach</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="ConcurrentHashSet--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ConcurrentHashSet</h4>
<pre>public&nbsp;ConcurrentHashSet()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="clear--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clear</h4>
<pre>public&nbsp;void&nbsp;clear()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>clear</code>&nbsp;在接口中&nbsp;<code>java.util.Collection&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>clear</code>&nbsp;在接口中&nbsp;<code>java.util.Set&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>clear</code>&nbsp;在类中&nbsp;<code>java.util.AbstractCollection&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="size--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>size</h4>
<pre>public&nbsp;int&nbsp;size()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>size</code>&nbsp;在接口中&nbsp;<code>java.util.Collection&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>size</code>&nbsp;在接口中&nbsp;<code>java.util.Set&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>size</code>&nbsp;在类中&nbsp;<code>java.util.AbstractCollection&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="isEmpty--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEmpty</h4>
<pre>public&nbsp;boolean&nbsp;isEmpty()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>isEmpty</code>&nbsp;在接口中&nbsp;<code>java.util.Collection&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>isEmpty</code>&nbsp;在接口中&nbsp;<code>java.util.Set&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>isEmpty</code>&nbsp;在类中&nbsp;<code>java.util.AbstractCollection&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="contains-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contains</h4>
<pre>public&nbsp;boolean&nbsp;contains(java.lang.Object&nbsp;o)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>contains</code>&nbsp;在接口中&nbsp;<code>java.util.Collection&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>contains</code>&nbsp;在接口中&nbsp;<code>java.util.Set&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>contains</code>&nbsp;在类中&nbsp;<code>java.util.AbstractCollection&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="remove-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remove</h4>
<pre>public&nbsp;boolean&nbsp;remove(java.lang.Object&nbsp;o)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>remove</code>&nbsp;在接口中&nbsp;<code>java.util.Collection&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>remove</code>&nbsp;在接口中&nbsp;<code>java.util.Set&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>remove</code>&nbsp;在类中&nbsp;<code>java.util.AbstractCollection&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="add-java.lang.Object-">
<!--   -->
</a><a name="add-E-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>add</h4>
<pre>public&nbsp;boolean&nbsp;add(<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&nbsp;e)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>add</code>&nbsp;在接口中&nbsp;<code>java.util.Collection&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>add</code>&nbsp;在接口中&nbsp;<code>java.util.Set&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>add</code>&nbsp;在类中&nbsp;<code>java.util.AbstractCollection&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="iterator--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>iterator</h4>
<pre>public&nbsp;java.util.Iterator&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;&nbsp;iterator()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>iterator</code>&nbsp;在接口中&nbsp;<code>java.lang.Iterable&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>iterator</code>&nbsp;在接口中&nbsp;<code>java.util.Collection&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>iterator</code>&nbsp;在接口中&nbsp;<code>java.util.Set&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>iterator</code>&nbsp;在类中&nbsp;<code>java.util.AbstractCollection&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="toArray--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toArray</h4>
<pre>public&nbsp;java.lang.Object[]&nbsp;toArray()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>toArray</code>&nbsp;在接口中&nbsp;<code>java.util.Collection&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>toArray</code>&nbsp;在接口中&nbsp;<code>java.util.Set&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>toArray</code>&nbsp;在类中&nbsp;<code>java.util.AbstractCollection&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="toArray-java.lang.Object:A-">
<!--   -->
</a><a name="toArray-T:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toArray</h4>
<pre>public&nbsp;&lt;T&gt;&nbsp;T[]&nbsp;toArray(T[]&nbsp;a)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>toArray</code>&nbsp;在接口中&nbsp;<code>java.util.Collection&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>toArray</code>&nbsp;在接口中&nbsp;<code>java.util.Set&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>toArray</code>&nbsp;在类中&nbsp;<code>java.util.AbstractCollection&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;java.lang.String&nbsp;toString()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>toString</code>&nbsp;在类中&nbsp;<code>java.util.AbstractCollection&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="hashCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hashCode</h4>
<pre>public&nbsp;int&nbsp;hashCode()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>hashCode</code>&nbsp;在接口中&nbsp;<code>java.util.Collection&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>hashCode</code>&nbsp;在接口中&nbsp;<code>java.util.Set&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>hashCode</code>&nbsp;在类中&nbsp;<code>java.util.AbstractSet&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>equals</h4>
<pre>public&nbsp;boolean&nbsp;equals(java.lang.Object&nbsp;o)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>equals</code>&nbsp;在接口中&nbsp;<code>java.util.Collection&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>equals</code>&nbsp;在接口中&nbsp;<code>java.util.Set&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>equals</code>&nbsp;在类中&nbsp;<code>java.util.AbstractSet&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="containsAll-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>containsAll</h4>
<pre>public&nbsp;boolean&nbsp;containsAll(java.util.Collection&lt;?&gt;&nbsp;c)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>containsAll</code>&nbsp;在接口中&nbsp;<code>java.util.Collection&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>containsAll</code>&nbsp;在接口中&nbsp;<code>java.util.Set&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>containsAll</code>&nbsp;在类中&nbsp;<code>java.util.AbstractCollection&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="removeAll-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeAll</h4>
<pre>public&nbsp;boolean&nbsp;removeAll(java.util.Collection&lt;?&gt;&nbsp;c)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>removeAll</code>&nbsp;在接口中&nbsp;<code>java.util.Collection&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>removeAll</code>&nbsp;在接口中&nbsp;<code>java.util.Set&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>removeAll</code>&nbsp;在类中&nbsp;<code>java.util.AbstractSet&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="retainAll-java.util.Collection-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>retainAll</h4>
<pre>public&nbsp;boolean&nbsp;retainAll(java.util.Collection&lt;?&gt;&nbsp;c)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>retainAll</code>&nbsp;在接口中&nbsp;<code>java.util.Collection&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code>retainAll</code>&nbsp;在接口中&nbsp;<code>java.util.Set&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code>retainAll</code>&nbsp;在类中&nbsp;<code>java.util.AbstractCollection&lt;<a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" title="ConcurrentHashSet中的类型参数">E</a>&gt;</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../../com/huazheng/tunny/tools/collection/type/ConcurrentHashSetTest.html" title="com.huazheng.tunny.tools.collection.type中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/huazheng/tunny/tools/collection/type/ConcurrentHashSet.html" target="_top">框架</a></li>
<li><a href="ConcurrentHashSet.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
