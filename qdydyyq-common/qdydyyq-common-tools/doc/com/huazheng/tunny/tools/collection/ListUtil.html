<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:35 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ListUtil</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ListUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9,"i14":9,"i15":9,"i16":9,"i17":9,"i18":9,"i19":9,"i20":9,"i21":9,"i22":9,"i23":9,"i24":9,"i25":9,"i26":9,"i27":9,"i28":9,"i29":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/collection/CollectionUtilTest.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtilTest.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/collection/ListUtil.html" target="_top">框架</a></li>
<li><a href="ListUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.collection</div>
<h2 title="类 ListUtil" class="title">类 ListUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.collection.ListUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ListUtil</span>
extends java.lang.Object</pre>
<div class="block">关于List的工具集合.
 
 1. 常用函数(如是否为空，sort/binarySearch/shuffle/reverse(via JDK Collection)
 
 2. 各种构造函数(from guava and JDK Collection)
 
 3. 各种扩展List类型的创建函数
 
 5. 集合运算：交集，并集, 差集, 补集，from Commons Collection，但对其不合理的地方做了修正</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtil.html#ListUtil--">ListUtil</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtil.html#binarySearch-java.util.List-T-">binarySearch</a></span>(java.util.List&lt;? extends java.lang.Comparable&lt;? super T&gt;&gt;&nbsp;sortedList,
            T&nbsp;key)</code>
<div class="block">二分法快速查找对象, 使用Comparable对象自身的比较.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtil.html#binarySearch-java.util.List-T-java.util.Comparator-">binarySearch</a></span>(java.util.List&lt;? extends T&gt;&nbsp;sortedList,
            T&nbsp;key,
            java.util.Comparator&lt;? super T&gt;&nbsp;c)</code>
<div class="block">二分法快速查找对象，使用Comparator.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.List&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtil.html#difference-java.util.List-java.util.List-">difference</a></span>(java.util.List&lt;? extends T&gt;&nbsp;list1,
          java.util.List&lt;? extends T&gt;&nbsp;list2)</code>
<div class="block">list1, list2的差集（在list1，不在list2中的对象），产生新List.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.List&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtil.html#disjoint-java.util.List-java.util.List-">disjoint</a></span>(java.util.List&lt;? extends T&gt;&nbsp;list1,
        java.util.List&lt;? extends T&gt;&nbsp;list2)</code>
<div class="block">list1, list2的补集（在list1或list2中，但不在交集中的对象，又叫反交集）产生新List.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.List&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtil.html#emptyList--">emptyList</a></span>()</code>
<div class="block">返回一个空的结构特殊的List，节约空间.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.List&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtil.html#emptyListIfNull-java.util.List-">emptyListIfNull</a></span>(java.util.List&lt;T&gt;&nbsp;list)</code>
<div class="block">如果list为null，转化为一个安全的空List.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtil.html#getFirst-java.util.List-">getFirst</a></span>(java.util.List&lt;T&gt;&nbsp;list)</code>
<div class="block">获取第一个元素, 如果List为空返回 null.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtil.html#getLast-java.util.List-">getLast</a></span>(java.util.List&lt;T&gt;&nbsp;list)</code>
<div class="block">获取最后一个元素，如果List为空返回null.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.List&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtil.html#intersection-java.util.List-java.util.List-">intersection</a></span>(java.util.List&lt;? extends T&gt;&nbsp;list1,
            java.util.List&lt;? extends T&gt;&nbsp;list2)</code>
<div class="block">list1, list2的交集（同时在list1和list2的对象），产生新List
 
 copy from Apache Common Collection4 ListUtils，但其做了不合理的去重，因此重新改为性能较低但不去重的版本
 
 与List.retainAll()相比，考虑了的List中相同元素出现的次数, 如"a"在list1出现两次，而在list2中只出现一次，则交集里会保留一个"a".</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtil.html#isEmpty-java.util.List-">isEmpty</a></span>(java.util.List&lt;?&gt;&nbsp;list)</code>
<div class="block">判断是否为空.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtil.html#isNotEmpty-java.util.List-">isNotEmpty</a></span>(java.util.List&lt;?&gt;&nbsp;list)</code>
<div class="block">判断是否不为空.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.ArrayList&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtil.html#newArrayList--">newArrayList</a></span>()</code>
<div class="block">根据等号左边的类型，构造类型正确的ArrayList.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.ArrayList&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtil.html#newArrayList-java.lang.Iterable-">newArrayList</a></span>(java.lang.Iterable&lt;T&gt;&nbsp;elements)</code>
<div class="block">根据等号左边的类型，构造类型正确的ArrayList, 并初始化元素.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.ArrayList&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtil.html#newArrayList-T...-">newArrayList</a></span>(T...&nbsp;elements)</code>
<div class="block">根据等号左边的类型，构造类型正确的ArrayList, 并初始化元素.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.ArrayList&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtil.html#newArrayListWithCapacity-int-">newArrayListWithCapacity</a></span>(int&nbsp;initSize)</code>
<div class="block">根据等号左边的类型，构造类型正确的ArrayList, 并初始化数组大小.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.concurrent.CopyOnWriteArrayList&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtil.html#newCopyOnWriteArrayList--">newCopyOnWriteArrayList</a></span>()</code>
<div class="block">根据等号左边的类型，构造类型正确的CopyOnWriteArrayList.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.concurrent.CopyOnWriteArrayList&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtil.html#newCopyOnWriteArrayList-T...-">newCopyOnWriteArrayList</a></span>(T...&nbsp;elements)</code>
<div class="block">根据等号左边的类型，构造类型转换的CopyOnWriteArrayList, 并初始化元素.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.LinkedList&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtil.html#newLinkedList--">newLinkedList</a></span>()</code>
<div class="block">根据等号左边的类型，构造类型正确的LinkedList.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.LinkedList&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtil.html#newLinkedList-java.lang.Iterable-">newLinkedList</a></span>(java.lang.Iterable&lt;? extends T&gt;&nbsp;elements)</code>
<div class="block">根据等号左边的类型，构造类型正确的LinkedList.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.List&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtil.html#reverse-java.util.List-">reverse</a></span>(java.util.List&lt;T&gt;&nbsp;list)</code>
<div class="block">返回一个倒转顺序访问的List，仅仅是一个倒序的View，不会实际多生成一个List</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtil.html#shuffle-java.util.List-">shuffle</a></span>(java.util.List&lt;?&gt;&nbsp;list)</code>
<div class="block">随机乱序，使用默认的Random.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtil.html#shuffle-java.util.List-java.util.Random-">shuffle</a></span>(java.util.List&lt;?&gt;&nbsp;list,
       java.util.Random&nbsp;rnd)</code>
<div class="block">随机乱序，使用传入的Random.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.List&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtil.html#singletonList-T-">singletonList</a></span>(T&nbsp;o)</code>
<div class="block">返回只含一个元素但结构特殊的List，节约空间.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>static &lt;T extends java.lang.Comparable&lt;? super T&gt;&gt;<br>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtil.html#sort-java.util.List-">sort</a></span>(java.util.List&lt;T&gt;&nbsp;list)</code>
<div class="block">升序排序, 采用JDK认为最优的排序算法, 使用元素自身的compareTo()方法</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtil.html#sort-java.util.List-java.util.Comparator-">sort</a></span>(java.util.List&lt;T&gt;&nbsp;list,
    java.util.Comparator&lt;? super T&gt;&nbsp;c)</code>
<div class="block">升序排序, 采用JDK认为最优的排序算法, 使用Comparetor.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>static &lt;T extends java.lang.Comparable&lt;? super T&gt;&gt;<br>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtil.html#sortReverse-java.util.List-">sortReverse</a></span>(java.util.List&lt;T&gt;&nbsp;list)</code>
<div class="block">倒序排序, 采用JDK认为最优的排序算法,使用元素自身的compareTo()方法</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtil.html#sortReverse-java.util.List-java.util.Comparator-">sortReverse</a></span>(java.util.List&lt;T&gt;&nbsp;list,
           java.util.Comparator&lt;? super T&gt;&nbsp;c)</code>
<div class="block">倒序排序, 采用JDK认为最优的排序算法, 使用Comparator</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.List&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtil.html#synchronizedList-java.util.List-">synchronizedList</a></span>(java.util.List&lt;T&gt;&nbsp;list)</code>
<div class="block">返回包装后同步的List，所有方法都会被synchronized原语同步.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>static &lt;E&gt;&nbsp;java.util.List&lt;E&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtil.html#union-java.util.List-java.util.List-">union</a></span>(java.util.List&lt;? extends E&gt;&nbsp;list1,
     java.util.List&lt;? extends E&gt;&nbsp;list2)</code>
<div class="block">list1,list2的并集（在list1或list2中的对象），产生新List
 
 对比Apache Common Collection4 ListUtils, 优化了初始大小</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.List&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtil.html#unmodifiableList-java.util.List-">unmodifiableList</a></span>(java.util.List&lt;? extends T&gt;&nbsp;list)</code>
<div class="block">返回包装后不可修改的List.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="ListUtil--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ListUtil</h4>
<pre>public&nbsp;ListUtil()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="isEmpty-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEmpty</h4>
<pre>public static&nbsp;boolean&nbsp;isEmpty(java.util.List&lt;?&gt;&nbsp;list)</pre>
<div class="block">判断是否为空.</div>
</li>
</ul>
<a name="isNotEmpty-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isNotEmpty</h4>
<pre>public static&nbsp;boolean&nbsp;isNotEmpty(java.util.List&lt;?&gt;&nbsp;list)</pre>
<div class="block">判断是否不为空.</div>
</li>
</ul>
<a name="getFirst-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFirst</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;T&nbsp;getFirst(java.util.List&lt;T&gt;&nbsp;list)</pre>
<div class="block">获取第一个元素, 如果List为空返回 null.</div>
</li>
</ul>
<a name="getLast-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLast</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;T&nbsp;getLast(java.util.List&lt;T&gt;&nbsp;list)</pre>
<div class="block">获取最后一个元素，如果List为空返回null.</div>
</li>
</ul>
<a name="newArrayList--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newArrayList</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;java.util.ArrayList&lt;T&gt;&nbsp;newArrayList()</pre>
<div class="block">根据等号左边的类型，构造类型正确的ArrayList.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Lists.newArrayList()</code></dd>
</dl>
</li>
</ul>
<a name="newArrayList-java.lang.Object:A-">
<!--   -->
</a><a name="newArrayList-T...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newArrayList</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;java.util.ArrayList&lt;T&gt;&nbsp;newArrayList(T...&nbsp;elements)</pre>
<div class="block">根据等号左边的类型，构造类型正确的ArrayList, 并初始化元素.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Lists.newArrayList(Object...)</code></dd>
</dl>
</li>
</ul>
<a name="newArrayList-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newArrayList</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;java.util.ArrayList&lt;T&gt;&nbsp;newArrayList(java.lang.Iterable&lt;T&gt;&nbsp;elements)</pre>
<div class="block">根据等号左边的类型，构造类型正确的ArrayList, 并初始化元素.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Lists.newArrayList(Iterable)</code></dd>
</dl>
</li>
</ul>
<a name="newArrayListWithCapacity-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newArrayListWithCapacity</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;java.util.ArrayList&lt;T&gt;&nbsp;newArrayListWithCapacity(int&nbsp;initSize)</pre>
<div class="block">根据等号左边的类型，构造类型正确的ArrayList, 并初始化数组大小.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Lists.newArrayListWithCapacity(int)</code></dd>
</dl>
</li>
</ul>
<a name="newLinkedList--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newLinkedList</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;java.util.LinkedList&lt;T&gt;&nbsp;newLinkedList()</pre>
<div class="block">根据等号左边的类型，构造类型正确的LinkedList.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Lists.newLinkedList()</code></dd>
</dl>
</li>
</ul>
<a name="newLinkedList-java.lang.Iterable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newLinkedList</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;java.util.LinkedList&lt;T&gt;&nbsp;newLinkedList(java.lang.Iterable&lt;? extends T&gt;&nbsp;elements)</pre>
<div class="block">根据等号左边的类型，构造类型正确的LinkedList.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Lists.newLinkedList()</code></dd>
</dl>
</li>
</ul>
<a name="newCopyOnWriteArrayList--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newCopyOnWriteArrayList</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;java.util.concurrent.CopyOnWriteArrayList&lt;T&gt;&nbsp;newCopyOnWriteArrayList()</pre>
<div class="block">根据等号左边的类型，构造类型正确的CopyOnWriteArrayList.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Lists.newCopyOnWriteArrayList()</code></dd>
</dl>
</li>
</ul>
<a name="newCopyOnWriteArrayList-java.lang.Object:A-">
<!--   -->
</a><a name="newCopyOnWriteArrayList-T...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newCopyOnWriteArrayList</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;java.util.concurrent.CopyOnWriteArrayList&lt;T&gt;&nbsp;newCopyOnWriteArrayList(T...&nbsp;elements)</pre>
<div class="block">根据等号左边的类型，构造类型转换的CopyOnWriteArrayList, 并初始化元素.</div>
</li>
</ul>
<a name="emptyList--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>emptyList</h4>
<pre>public static final&nbsp;&lt;T&gt;&nbsp;java.util.List&lt;T&gt;&nbsp;emptyList()</pre>
<div class="block">返回一个空的结构特殊的List，节约空间.
 
 注意返回的List不可写, 写入会抛出UnsupportedOperationException.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Collections.emptyList()</code></dd>
</dl>
</li>
</ul>
<a name="emptyListIfNull-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>emptyListIfNull</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;java.util.List&lt;T&gt;&nbsp;emptyListIfNull(java.util.List&lt;T&gt;&nbsp;list)</pre>
<div class="block">如果list为null，转化为一个安全的空List.
 
 注意返回的List不可写, 写入会抛出UnsupportedOperationException.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Collections.emptyList()</code></dd>
</dl>
</li>
</ul>
<a name="singletonList-java.lang.Object-">
<!--   -->
</a><a name="singletonList-T-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>singletonList</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;java.util.List&lt;T&gt;&nbsp;singletonList(T&nbsp;o)</pre>
<div class="block">返回只含一个元素但结构特殊的List，节约空间.
 
 注意返回的List不可写, 写入会抛出UnsupportedOperationException.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Collections.singletonList(Object)</code></dd>
</dl>
</li>
</ul>
<a name="unmodifiableList-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unmodifiableList</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;java.util.List&lt;T&gt;&nbsp;unmodifiableList(java.util.List&lt;? extends T&gt;&nbsp;list)</pre>
<div class="block">返回包装后不可修改的List.
 
 如果尝试写入会抛出UnsupportedOperationException.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Collections.unmodifiableList(List)</code></dd>
</dl>
</li>
</ul>
<a name="synchronizedList-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>synchronizedList</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;java.util.List&lt;T&gt;&nbsp;synchronizedList(java.util.List&lt;T&gt;&nbsp;list)</pre>
<div class="block">返回包装后同步的List，所有方法都会被synchronized原语同步.
 
 用于CopyOnWriteArrayList与 ArrayDequeue均不符合的场景</div>
</li>
</ul>
<a name="sort-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sort</h4>
<pre>public static&nbsp;&lt;T extends java.lang.Comparable&lt;? super T&gt;&gt;&nbsp;void&nbsp;sort(java.util.List&lt;T&gt;&nbsp;list)</pre>
<div class="block">升序排序, 采用JDK认为最优的排序算法, 使用元素自身的compareTo()方法</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Collections.sort(List)</code></dd>
</dl>
</li>
</ul>
<a name="sortReverse-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sortReverse</h4>
<pre>public static&nbsp;&lt;T extends java.lang.Comparable&lt;? super T&gt;&gt;&nbsp;void&nbsp;sortReverse(java.util.List&lt;T&gt;&nbsp;list)</pre>
<div class="block">倒序排序, 采用JDK认为最优的排序算法,使用元素自身的compareTo()方法</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Collections.sort(List)</code></dd>
</dl>
</li>
</ul>
<a name="sort-java.util.List-java.util.Comparator-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sort</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;void&nbsp;sort(java.util.List&lt;T&gt;&nbsp;list,
                            java.util.Comparator&lt;? super T&gt;&nbsp;c)</pre>
<div class="block">升序排序, 采用JDK认为最优的排序算法, 使用Comparetor.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Collections.sort(List, Comparator)</code></dd>
</dl>
</li>
</ul>
<a name="sortReverse-java.util.List-java.util.Comparator-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sortReverse</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;void&nbsp;sortReverse(java.util.List&lt;T&gt;&nbsp;list,
                                   java.util.Comparator&lt;? super T&gt;&nbsp;c)</pre>
<div class="block">倒序排序, 采用JDK认为最优的排序算法, 使用Comparator</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Collections.sort(List, Comparator)</code></dd>
</dl>
</li>
</ul>
<a name="binarySearch-java.util.List-java.lang.Object-">
<!--   -->
</a><a name="binarySearch-java.util.List-T-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>binarySearch</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;int&nbsp;binarySearch(java.util.List&lt;? extends java.lang.Comparable&lt;? super T&gt;&gt;&nbsp;sortedList,
                                   T&nbsp;key)</pre>
<div class="block">二分法快速查找对象, 使用Comparable对象自身的比较.
 
 list必须已按升序排序.
 
 如果不存在，返回一个负数，代表如果要插入这个对象，应该插入的位置</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Collections.binarySearch(List, Object)</code></dd>
</dl>
</li>
</ul>
<a name="binarySearch-java.util.List-java.lang.Object-java.util.Comparator-">
<!--   -->
</a><a name="binarySearch-java.util.List-T-java.util.Comparator-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>binarySearch</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;int&nbsp;binarySearch(java.util.List&lt;? extends T&gt;&nbsp;sortedList,
                                   T&nbsp;key,
                                   java.util.Comparator&lt;? super T&gt;&nbsp;c)</pre>
<div class="block">二分法快速查找对象，使用Comparator.
 
 list必须已按升序排序.
 
 如果不存在，返回一个负数，代表如果要插入这个对象，应该插入的位置</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Collections.binarySearch(List, Object, Comparator)</code></dd>
</dl>
</li>
</ul>
<a name="shuffle-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>shuffle</h4>
<pre>public static&nbsp;void&nbsp;shuffle(java.util.List&lt;?&gt;&nbsp;list)</pre>
<div class="block">随机乱序，使用默认的Random.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Collections.shuffle(List)</code></dd>
</dl>
</li>
</ul>
<a name="reverse-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>reverse</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;java.util.List&lt;T&gt;&nbsp;reverse(java.util.List&lt;T&gt;&nbsp;list)</pre>
<div class="block">返回一个倒转顺序访问的List，仅仅是一个倒序的View，不会实际多生成一个List</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Lists.reverse(List)</code></dd>
</dl>
</li>
</ul>
<a name="shuffle-java.util.List-java.util.Random-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>shuffle</h4>
<pre>public static&nbsp;void&nbsp;shuffle(java.util.List&lt;?&gt;&nbsp;list,
                           java.util.Random&nbsp;rnd)</pre>
<div class="block">随机乱序，使用传入的Random.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Collections.shuffle(List, Random)</code></dd>
</dl>
</li>
</ul>
<a name="union-java.util.List-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>union</h4>
<pre>public static&nbsp;&lt;E&gt;&nbsp;java.util.List&lt;E&gt;&nbsp;union(java.util.List&lt;? extends E&gt;&nbsp;list1,
                                          java.util.List&lt;? extends E&gt;&nbsp;list2)</pre>
<div class="block">list1,list2的并集（在list1或list2中的对象），产生新List
 
 对比Apache Common Collection4 ListUtils, 优化了初始大小</div>
</li>
</ul>
<a name="intersection-java.util.List-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>intersection</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;java.util.List&lt;T&gt;&nbsp;intersection(java.util.List&lt;? extends T&gt;&nbsp;list1,
                                                 java.util.List&lt;? extends T&gt;&nbsp;list2)</pre>
<div class="block">list1, list2的交集（同时在list1和list2的对象），产生新List
 
 copy from Apache Common Collection4 ListUtils，但其做了不合理的去重，因此重新改为性能较低但不去重的版本
 
 与List.retainAll()相比，考虑了的List中相同元素出现的次数, 如"a"在list1出现两次，而在list2中只出现一次，则交集里会保留一个"a".</div>
</li>
</ul>
<a name="difference-java.util.List-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>difference</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;java.util.List&lt;T&gt;&nbsp;difference(java.util.List&lt;? extends T&gt;&nbsp;list1,
                                               java.util.List&lt;? extends T&gt;&nbsp;list2)</pre>
<div class="block">list1, list2的差集（在list1，不在list2中的对象），产生新List.
 
 与List.removeAll()相比，会计算元素出现的次数，如"a"在list1出现两次，而在list2中只出现一次，则差集里会保留一个"a".</div>
</li>
</ul>
<a name="disjoint-java.util.List-java.util.List-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>disjoint</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;java.util.List&lt;T&gt;&nbsp;disjoint(java.util.List&lt;? extends T&gt;&nbsp;list1,
                                             java.util.List&lt;? extends T&gt;&nbsp;list2)</pre>
<div class="block">list1, list2的补集（在list1或list2中，但不在交集中的对象，又叫反交集）产生新List.
 
 copy from Apache Common Collection4 ListUtils，但其并集－交集时，初始大小没有对交集*2，所以做了修改</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/collection/CollectionUtilTest.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtilTest.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/collection/ListUtil.html" target="_top">框架</a></li>
<li><a href="ListUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
