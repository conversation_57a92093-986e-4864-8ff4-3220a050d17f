<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:35 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>MoreMaps</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="MoreMaps";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/collection/MoreLists.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/collection/MoreQueues.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/collection/MoreMaps.html" target="_top">框架</a></li>
<li><a href="MoreMaps.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.collection</div>
<h2 title="类 MoreMaps" class="title">类 MoreMaps</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.collection.MoreMaps</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">MoreMaps</span>
extends java.lang.Object</pre>
<div class="block">来自Guava，Netty等的特殊Map类型</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MoreMaps.html#MoreMaps--">MoreMaps</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static &lt;E&gt;&nbsp;com.google.common.util.concurrent.AtomicLongMap&lt;E&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MoreMaps.html#createConcurrentCounterMap--">createConcurrentCounterMap</a></span>()</code>
<div class="block">以Guava的AtomicLongMap，实现线程安全的HashMap<E,AtomicLong>结构的Counter</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static &lt;K,V&gt;&nbsp;com.google.common.collect.ArrayListMultimap&lt;K,V&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MoreMaps.html#createListMultiValueMap-int-int-">createListMultiValueMap</a></span>(int&nbsp;expectedKeys,
                       int&nbsp;expectedValuesPerKey)</code>
<div class="block">以Guava的MultiMap，实现的HashMap<E,List<V>>结构的一个Key对应多个值的map.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static &lt;K&gt;&nbsp;java.util.HashMap&lt;K,org.apache.commons.lang3.mutable.MutableInt&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MoreMaps.html#createMutableIntValueMap-int-float-">createMutableIntValueMap</a></span>(int&nbsp;initialCapacity,
                        float&nbsp;loadFactor)</code>
<div class="block">创建值为可更改的Integer的HashMap.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static &lt;K&gt;&nbsp;java.util.HashMap&lt;K,org.apache.commons.lang3.mutable.MutableLong&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MoreMaps.html#createMutableLongValueMap-int-float-">createMutableLongValueMap</a></span>(int&nbsp;initialCapacity,
                         float&nbsp;loadFactor)</code>
<div class="block">创建值为可更改的Long的HashMap.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static &lt;V&gt;&nbsp;<a href="../../../../../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">IntObjectHashMap</a>&lt;V&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MoreMaps.html#createPrimitiveIntKeyMap-int-float-">createPrimitiveIntKeyMap</a></span>(int&nbsp;initialCapacity,
                        float&nbsp;loadFactor)</code>
<div class="block">创建移植自Netty的key为int的优化HashMap</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static &lt;V&gt;&nbsp;<a href="../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">LongObjectHashMap</a>&lt;V&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MoreMaps.html#createPrimitiveLongKeyMap-int-float-">createPrimitiveLongKeyMap</a></span>(int&nbsp;initialCapacity,
                         float&nbsp;loadFactor)</code>
<div class="block">创建移植自Netty的key为long的优化HashMap</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static &lt;K extends java.lang.Comparable,V&gt;<br>com.google.common.collect.TreeRangeMap&lt;K,V&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MoreMaps.html#createRangeMap--">createRangeMap</a></span>()</code>
<div class="block">以Guava TreeRangeMap实现的, 一段范围的Key指向同一个Value的Map</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static &lt;K,V extends java.lang.Comparable&gt;<br>com.google.common.collect.SortedSetMultimap&lt;K,V&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MoreMaps.html#createSortedSetMultiValueMap--">createSortedSetMultiValueMap</a></span>()</code>
<div class="block">以Guava的MultiMap，实现的HashMap<E,TreeSet<V>>结构的一个Key对应多个值的map.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static &lt;K,V&gt;&nbsp;com.google.common.collect.SortedSetMultimap&lt;K,V&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MoreMaps.html#createSortedSetMultiValueMap-java.util.Comparator-">createSortedSetMultiValueMap</a></span>(java.util.Comparator&lt;V&gt;&nbsp;comparator)</code>
<div class="block">以Guava的MultiMap，实现的HashMap<E,TreeSet<V>>结构的一个Key对应多个值的map.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static &lt;K,V&gt;&nbsp;java.util.concurrent.ConcurrentMap&lt;K,V&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MoreMaps.html#createWeakKeyConcurrentMap-int-int-">createWeakKeyConcurrentMap</a></span>(int&nbsp;initialCapacity,
                          int&nbsp;concurrencyLevel)</code>
<div class="block">创建Key为弱引用的ConcurrentMap，Key对象可被回收.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static &lt;K,V&gt;&nbsp;java.util.concurrent.ConcurrentMap&lt;K,V&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MoreMaps.html#createWeakValueConcurrentMap-int-int-">createWeakValueConcurrentMap</a></span>(int&nbsp;initialCapacity,
                            int&nbsp;concurrencyLevel)</code>
<div class="block">创建Value为弱引用的ConcurrentMap，Value对象可被回收.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="MoreMaps--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>MoreMaps</h4>
<pre>public&nbsp;MoreMaps()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="createWeakKeyConcurrentMap-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createWeakKeyConcurrentMap</h4>
<pre>public static&nbsp;&lt;K,V&gt;&nbsp;java.util.concurrent.ConcurrentMap&lt;K,V&gt;&nbsp;createWeakKeyConcurrentMap(int&nbsp;initialCapacity,
                                                                                       int&nbsp;concurrencyLevel)</pre>
<div class="block">创建Key为弱引用的ConcurrentMap，Key对象可被回收.
 
 JDK没有WeakHashMap的并发实现, 由Guava提供</div>
</li>
</ul>
<a name="createWeakValueConcurrentMap-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createWeakValueConcurrentMap</h4>
<pre>public static&nbsp;&lt;K,V&gt;&nbsp;java.util.concurrent.ConcurrentMap&lt;K,V&gt;&nbsp;createWeakValueConcurrentMap(int&nbsp;initialCapacity,
                                                                                         int&nbsp;concurrencyLevel)</pre>
<div class="block">创建Value为弱引用的ConcurrentMap，Value对象可被回收.
 
 JDK没有WeakHashMap的并发实现, 由Guava提供</div>
</li>
</ul>
<a name="createPrimitiveIntKeyMap-int-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createPrimitiveIntKeyMap</h4>
<pre>public static&nbsp;&lt;V&gt;&nbsp;<a href="../../../../../com/huazheng/tunny/tools/collection/type/primitive/IntObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">IntObjectHashMap</a>&lt;V&gt;&nbsp;createPrimitiveIntKeyMap(int&nbsp;initialCapacity,
                                                               float&nbsp;loadFactor)</pre>
<div class="block">创建移植自Netty的key为int的优化HashMap</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>initialCapacity</code> - 默认为8</dd>
<dd><code>loadFactor</code> - 默认为0.5</dd>
</dl>
</li>
</ul>
<a name="createPrimitiveLongKeyMap-int-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createPrimitiveLongKeyMap</h4>
<pre>public static&nbsp;&lt;V&gt;&nbsp;<a href="../../../../../com/huazheng/tunny/tools/collection/type/primitive/LongObjectHashMap.html" title="com.huazheng.tunny.tools.collection.type.primitive中的类">LongObjectHashMap</a>&lt;V&gt;&nbsp;createPrimitiveLongKeyMap(int&nbsp;initialCapacity,
                                                                 float&nbsp;loadFactor)</pre>
<div class="block">创建移植自Netty的key为long的优化HashMap</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>initialCapacity</code> - 默认为8</dd>
<dd><code>loadFactor</code> - 默认为0.5</dd>
</dl>
</li>
</ul>
<a name="createMutableIntValueMap-int-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createMutableIntValueMap</h4>
<pre>public static&nbsp;&lt;K&gt;&nbsp;java.util.HashMap&lt;K,org.apache.commons.lang3.mutable.MutableInt&gt;&nbsp;createMutableIntValueMap(int&nbsp;initialCapacity,
                                                                                                            float&nbsp;loadFactor)</pre>
<div class="block">创建值为可更改的Integer的HashMap. 可更改的Integer在更改时不需要重新创建Integer对象，节约了内存</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>initialCapacity</code> - 建议为16</dd>
<dd><code>loadFactor</code> - 建议为0.5</dd>
</dl>
</li>
</ul>
<a name="createMutableLongValueMap-int-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createMutableLongValueMap</h4>
<pre>public static&nbsp;&lt;K&gt;&nbsp;java.util.HashMap&lt;K,org.apache.commons.lang3.mutable.MutableLong&gt;&nbsp;createMutableLongValueMap(int&nbsp;initialCapacity,
                                                                                                              float&nbsp;loadFactor)</pre>
<div class="block">创建值为可更改的Long的HashMap. 可更改的Long在更改时不需要重新创建Long对象，节约了内存</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>initialCapacity</code> - 建议为16</dd>
<dd><code>loadFactor</code> - 建议为0.5</dd>
</dl>
</li>
</ul>
<a name="createConcurrentCounterMap--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createConcurrentCounterMap</h4>
<pre>public static&nbsp;&lt;E&gt;&nbsp;com.google.common.util.concurrent.AtomicLongMap&lt;E&gt;&nbsp;createConcurrentCounterMap()</pre>
<div class="block">以Guava的AtomicLongMap，实现线程安全的HashMap<E,AtomicLong>结构的Counter</div>
</li>
</ul>
<a name="createListMultiValueMap-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createListMultiValueMap</h4>
<pre>public static&nbsp;&lt;K,V&gt;&nbsp;com.google.common.collect.ArrayListMultimap&lt;K,V&gt;&nbsp;createListMultiValueMap(int&nbsp;expectedKeys,
                                                                                             int&nbsp;expectedValuesPerKey)</pre>
<div class="block">以Guava的MultiMap，实现的HashMap<E,List<V>>结构的一个Key对应多个值的map.
 
 注意非线程安全, MultiMap无线程安全的实现.
 
 另有其他结构存储values的MultiMap，请自行参考MultimapBuilder使用.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>expectedKeys</code> - 默认为16</dd>
<dd><code>expectedValuesPerKey</code> - 默认为3</dd>
</dl>
</li>
</ul>
<a name="createSortedSetMultiValueMap--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createSortedSetMultiValueMap</h4>
<pre>public static&nbsp;&lt;K,V extends java.lang.Comparable&gt;&nbsp;com.google.common.collect.SortedSetMultimap&lt;K,V&gt;&nbsp;createSortedSetMultiValueMap()</pre>
<div class="block">以Guava的MultiMap，实现的HashMap<E,TreeSet<V>>结构的一个Key对应多个值的map.
 
 注意非线程安全, MultiValueMap无线程安全的实现.
 
 另有其他结构存储values的MultiMap，请自行参考MultimapBuilder使用.</div>
</li>
</ul>
<a name="createSortedSetMultiValueMap-java.util.Comparator-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createSortedSetMultiValueMap</h4>
<pre>public static&nbsp;&lt;K,V&gt;&nbsp;com.google.common.collect.SortedSetMultimap&lt;K,V&gt;&nbsp;createSortedSetMultiValueMap(java.util.Comparator&lt;V&gt;&nbsp;comparator)</pre>
<div class="block">以Guava的MultiMap，实现的HashMap<E,TreeSet<V>>结构的一个Key对应多个值的map.
 
 注意非线程安全, MultiValueMap无线程安全的实现.
 
 另有其他结构存储values的MultiMap，请自行参考MultimapBuilder使用.</div>
</li>
</ul>
<a name="createRangeMap--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>createRangeMap</h4>
<pre>public static&nbsp;&lt;K extends java.lang.Comparable,V&gt;&nbsp;com.google.common.collect.TreeRangeMap&lt;K,V&gt;&nbsp;createRangeMap()</pre>
<div class="block">以Guava TreeRangeMap实现的, 一段范围的Key指向同一个Value的Map</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/collection/MoreLists.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/collection/MoreQueues.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/collection/MoreMaps.html" target="_top">框架</a></li>
<li><a href="MoreMaps.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
