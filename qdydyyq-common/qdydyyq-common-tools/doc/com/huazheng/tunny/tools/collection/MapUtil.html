<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:35 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>MapUtil</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="MapUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9,"i14":9,"i15":9,"i16":9,"i17":9,"i18":9,"i19":9,"i20":9,"i21":9,"i22":9,"i23":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtilTest.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.ValueCreator.html" title="com.huazheng.tunny.tools.collection中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/collection/MapUtil.html" target="_top">框架</a></li>
<li><a href="MapUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.collection</div>
<h2 title="类 MapUtil" class="title">类 MapUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.collection.MapUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">MapUtil</span>
extends java.lang.Object</pre>
<div class="block">关于Map的工具集合，
 
 1. 常用函数(如是否为空, 两个map的Diff对比，针对value值的排序)
 
 2. 对于并发Map，增加putIfAbsent(返回最终值版), createIfAbsent这两个重要函数(from Common Lang)
 
 3. 便捷的构造函数(via guava,Java Collections，并增加了用数组，List等方式初始化Map的函数)
 
 4. JDK Collections的empty,singleton</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>嵌套类概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="嵌套类概要表, 列表嵌套类和解释">
<caption><span>嵌套类</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">类和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.ValueCreator.html" title="com.huazheng.tunny.tools.collection中的接口">MapUtil.ValueCreator</a>&lt;<a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.ValueCreator.html" title="MapUtil.ValueCreator中的类型参数">T</a>&gt;</span></code>
<div class="block">Lazy创建Value值的回调类</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.html#DEFAULT_LOAD_FACTOR">DEFAULT_LOAD_FACTOR</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.html#MapUtil--">MapUtil</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static &lt;K,V&gt;&nbsp;V</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.html#createIfAbsentReturnLast-java.util.concurrent.ConcurrentMap-K-com.huazheng.tunny.tools.collection.MapUtil.ValueCreator-">createIfAbsentReturnLast</a></span>(java.util.concurrent.ConcurrentMap&lt;K,V&gt;&nbsp;map,
                        K&nbsp;key,
                        <a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.ValueCreator.html" title="com.huazheng.tunny.tools.collection中的接口">MapUtil.ValueCreator</a>&lt;? extends V&gt;&nbsp;creator)</code>
<div class="block">如果Key不存在则创建，返回最后存储在Map中的Value.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static &lt;K,V&gt;&nbsp;com.google.common.collect.MapDifference&lt;K,V&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.html#difference-java.util.Map-java.util.Map-">difference</a></span>(java.util.Map&lt;? extends K,? extends V&gt;&nbsp;left,
          java.util.Map&lt;? extends K,? extends V&gt;&nbsp;right)</code>
<div class="block">对两个Map进行比较，返回MapDifference，然后各种妙用.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static &lt;K,V&gt;&nbsp;java.util.Map&lt;K,V&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.html#emptyMap--">emptyMap</a></span>()</code>
<div class="block">返回一个空的结构特殊的Map，节约空间.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static &lt;K,V&gt;&nbsp;java.util.Map&lt;K,V&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.html#emptyMapIfNull-java.util.Map-">emptyMapIfNull</a></span>(java.util.Map&lt;K,V&gt;&nbsp;map)</code>
<div class="block">如果map为null，转化为一个安全的空Map.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.html#isEmpty-java.util.Map-">isEmpty</a></span>(java.util.Map&lt;?,?&gt;&nbsp;map)</code>
<div class="block">判断是否为空.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.html#isNotEmpty-java.util.Map-">isNotEmpty</a></span>(java.util.Map&lt;?,?&gt;&nbsp;map)</code>
<div class="block">判断是否为空.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static &lt;K,V&gt;&nbsp;java.util.concurrent.ConcurrentHashMap&lt;K,V&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.html#newConcurrentHashMap--">newConcurrentHashMap</a></span>()</code>
<div class="block">根据等号左边的类型，构造类型正确的ConcurrentHashMap.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static &lt;K,V&gt;&nbsp;java.util.concurrent.ConcurrentSkipListMap&lt;K,V&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.html#newConcurrentSortedMap--">newConcurrentSortedMap</a></span>()</code>
<div class="block">根据等号左边的类型，构造类型正确的ConcurrentSkipListMap.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static &lt;K extends java.lang.Enum&lt;K&gt;,V&gt;<br>java.util.EnumMap&lt;K,V&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.html#newEnumMap-java.lang.Class-">newEnumMap</a></span>(java.lang.Class&lt;K&gt;&nbsp;type)</code>
<div class="block">相比HashMap，当key是枚举类时, 性能与空间占用俱佳.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static &lt;K,V&gt;&nbsp;java.util.HashMap&lt;K,V&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.html#newHashMap--">newHashMap</a></span>()</code>
<div class="block">根据等号左边的类型, 构造类型正确的HashMap.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static &lt;K,V&gt;&nbsp;java.util.HashMap&lt;K,V&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.html#newHashMap-K:A-V:A-">newHashMap</a></span>(K[]&nbsp;keys,
          V[]&nbsp;values)</code>
<div class="block">根据等号左边的类型, 构造类型正确的HashMap.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static &lt;K,V&gt;&nbsp;java.util.HashMap&lt;K,V&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.html#newHashMap-K-V-">newHashMap</a></span>(K&nbsp;key,
          V&nbsp;value)</code>
<div class="block">根据等号左边的类型, 构造类型正确的HashMap.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static &lt;K,V&gt;&nbsp;java.util.HashMap&lt;K,V&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.html#newHashMap-java.util.List-java.util.List-">newHashMap</a></span>(java.util.List&lt;K&gt;&nbsp;keys,
          java.util.List&lt;V&gt;&nbsp;values)</code>
<div class="block">根据等号左边的类型, 构造类型正确的HashMap.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static &lt;K,V&gt;&nbsp;java.util.HashMap&lt;K,V&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.html#newHashMapWithCapacity-int-float-">newHashMapWithCapacity</a></span>(int&nbsp;expectedSize,
                      float&nbsp;loadFactor)</code>
<div class="block">根据等号左边的类型, 构造类型正确的HashMap.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static &lt;K extends java.lang.Comparable,V&gt;<br>java.util.TreeMap&lt;K,V&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.html#newSortedMap--">newSortedMap</a></span>()</code>
<div class="block">根据等号左边的类型，构造类型正确的TreeMap.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>static &lt;C,K extends C,V&gt;<br>java.util.TreeMap&lt;K,V&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.html#newSortedMap-java.util.Comparator-">newSortedMap</a></span>(java.util.Comparator&lt;C&gt;&nbsp;comparator)</code>
<div class="block">根据等号左边的类型，构造类型正确的TreeMap.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>static &lt;K,V&gt;&nbsp;V</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.html#putIfAbsentReturnLast-java.util.concurrent.ConcurrentMap-K-V-">putIfAbsentReturnLast</a></span>(java.util.concurrent.ConcurrentMap&lt;K,V&gt;&nbsp;map,
                     K&nbsp;key,
                     V&nbsp;value)</code>
<div class="block">ConcurrentMap的putIfAbsent()返回之前的Value，此函数封装返回最终存储在Map中的Value</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>static &lt;K,V&gt;&nbsp;java.util.Map&lt;K,V&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.html#singletonMap-K-V-">singletonMap</a></span>(K&nbsp;key,
            V&nbsp;value)</code>
<div class="block">返回一个只含一个元素但结构特殊的Map，节约空间.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>static &lt;K,V extends java.lang.Comparable&gt;<br>java.util.Map&lt;K,V&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.html#sortByValue-java.util.Map-boolean-">sortByValue</a></span>(java.util.Map&lt;K,V&gt;&nbsp;map,
           boolean&nbsp;reverse)</code>
<div class="block">对一个Map按Value进行排序，返回排序LinkedHashMap，多用于Value是Counter的情况.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>static &lt;K,V&gt;&nbsp;java.util.Map&lt;K,V&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.html#sortByValue-java.util.Map-java.util.Comparator-">sortByValue</a></span>(java.util.Map&lt;K,V&gt;&nbsp;map,
           java.util.Comparator&lt;? super V&gt;&nbsp;comparator)</code>
<div class="block">对一个Map按Value进行排序，返回排序LinkedHashMap.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>static &lt;K,V extends java.lang.Comparable&gt;<br>java.util.Map&lt;K,V&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.html#topNByValue-java.util.Map-boolean-int-">topNByValue</a></span>(java.util.Map&lt;K,V&gt;&nbsp;map,
           boolean&nbsp;reverse,
           int&nbsp;n)</code>
<div class="block">对一个Map按Value进行排序，返回排序LinkedHashMap，最多只返回n条，多用于Value是Counter的情况.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>static &lt;K,V&gt;&nbsp;java.util.Map&lt;K,V&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.html#topNByValue-java.util.Map-java.util.Comparator-int-">topNByValue</a></span>(java.util.Map&lt;K,V&gt;&nbsp;map,
           java.util.Comparator&lt;? super V&gt;&nbsp;comparator,
           int&nbsp;n)</code>
<div class="block">对一个Map按Value进行排序，返回排序LinkedHashMap, 最多只返回n条，多用于Value是Counter的情况.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>static &lt;K,V&gt;&nbsp;java.util.Map&lt;K,V&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.html#unmodifiableMap-java.util.Map-">unmodifiableMap</a></span>(java.util.Map&lt;? extends K,? extends V&gt;&nbsp;m)</code>
<div class="block">返回包装后不可修改的Map.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>static &lt;K,V&gt;&nbsp;java.util.SortedMap&lt;K,V&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.html#unmodifiableSortedMap-java.util.SortedMap-">unmodifiableSortedMap</a></span>(java.util.SortedMap&lt;K,? extends V&gt;&nbsp;m)</code>
<div class="block">返回包装后不可修改的有序Map.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="DEFAULT_LOAD_FACTOR">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DEFAULT_LOAD_FACTOR</h4>
<pre>public static final&nbsp;float DEFAULT_LOAD_FACTOR</pre>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><a href="../../../../../constant-values.html#com.huazheng.tunny.tools.collection.MapUtil.DEFAULT_LOAD_FACTOR">常量字段值</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="MapUtil--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>MapUtil</h4>
<pre>public&nbsp;MapUtil()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="isEmpty-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEmpty</h4>
<pre>public static&nbsp;boolean&nbsp;isEmpty(java.util.Map&lt;?,?&gt;&nbsp;map)</pre>
<div class="block">判断是否为空.</div>
</li>
</ul>
<a name="isNotEmpty-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isNotEmpty</h4>
<pre>public static&nbsp;boolean&nbsp;isNotEmpty(java.util.Map&lt;?,?&gt;&nbsp;map)</pre>
<div class="block">判断是否为空.</div>
</li>
</ul>
<a name="putIfAbsentReturnLast-java.util.concurrent.ConcurrentMap-java.lang.Object-java.lang.Object-">
<!--   -->
</a><a name="putIfAbsentReturnLast-java.util.concurrent.ConcurrentMap-K-V-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>putIfAbsentReturnLast</h4>
<pre>public static&nbsp;&lt;K,V&gt;&nbsp;V&nbsp;putIfAbsentReturnLast(java.util.concurrent.ConcurrentMap&lt;K,V&gt;&nbsp;map,
                                            K&nbsp;key,
                                            V&nbsp;value)</pre>
<div class="block">ConcurrentMap的putIfAbsent()返回之前的Value，此函数封装返回最终存储在Map中的Value</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>ConcurrentUtils.putIfAbsent(ConcurrentMap, Object, Object)</code></dd>
</dl>
</li>
</ul>
<a name="createIfAbsentReturnLast-java.util.concurrent.ConcurrentMap-java.lang.Object-com.huazheng.tunny.tools.collection.MapUtil.ValueCreator-">
<!--   -->
</a><a name="createIfAbsentReturnLast-java.util.concurrent.ConcurrentMap-K-com.huazheng.tunny.tools.collection.MapUtil.ValueCreator-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createIfAbsentReturnLast</h4>
<pre>public static&nbsp;&lt;K,V&gt;&nbsp;V&nbsp;createIfAbsentReturnLast(java.util.concurrent.ConcurrentMap&lt;K,V&gt;&nbsp;map,
                                               K&nbsp;key,
                                               <a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.ValueCreator.html" title="com.huazheng.tunny.tools.collection中的接口">MapUtil.ValueCreator</a>&lt;? extends V&gt;&nbsp;creator)</pre>
<div class="block">如果Key不存在则创建，返回最后存储在Map中的Value.
 
 如果创建Value对象有一定成本, 直接使用PutIfAbsent可能重复浪费，则使用此类，传入一个被回调的ValueCreator，Lazy创建对象。</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>ConcurrentUtils.createIfAbsent(ConcurrentMap, Object,
 org.apache.commons.lang3.concurrent.ConcurrentInitializer)</code></dd>
</dl>
</li>
</ul>
<a name="newHashMap--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newHashMap</h4>
<pre>public static&nbsp;&lt;K,V&gt;&nbsp;java.util.HashMap&lt;K,V&gt;&nbsp;newHashMap()</pre>
<div class="block">根据等号左边的类型, 构造类型正确的HashMap.
 
 未初始化数组大小, 默认为16个桶.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Maps.newHashMap()</code></dd>
</dl>
</li>
</ul>
<a name="newHashMapWithCapacity-int-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newHashMapWithCapacity</h4>
<pre>public static&nbsp;&lt;K,V&gt;&nbsp;java.util.HashMap&lt;K,V&gt;&nbsp;newHashMapWithCapacity(int&nbsp;expectedSize,
                                                                  float&nbsp;loadFactor)</pre>
<div class="block">根据等号左边的类型, 构造类型正确的HashMap.
 
 注意HashMap中有0.75的加载因子的影响, 需要进行运算后才能正确初始化HashMap的大小.
 
 加载因子也是HashMap中减少Hash冲突的重要一环，如果读写频繁，总记录数不多的Map，可以比默认值0.75进一步降低，建议0.5</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>com.google.common.collect.Maps#newHashMap(int)</code></dd>
</dl>
</li>
</ul>
<a name="newHashMap-java.lang.Object-java.lang.Object-">
<!--   -->
</a><a name="newHashMap-K-V-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newHashMap</h4>
<pre>public static&nbsp;&lt;K,V&gt;&nbsp;java.util.HashMap&lt;K,V&gt;&nbsp;newHashMap(K&nbsp;key,
                                                      V&nbsp;value)</pre>
<div class="block">根据等号左边的类型, 构造类型正确的HashMap.
 
 同时初始化第一个元素</div>
</li>
</ul>
<a name="newHashMap-java.lang.Object:A-java.lang.Object:A-">
<!--   -->
</a><a name="newHashMap-K:A-V:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newHashMap</h4>
<pre>public static&nbsp;&lt;K,V&gt;&nbsp;java.util.HashMap&lt;K,V&gt;&nbsp;newHashMap(K[]&nbsp;keys,
                                                      V[]&nbsp;values)</pre>
<div class="block">根据等号左边的类型, 构造类型正确的HashMap.
 
 同时初始化元素.</div>
</li>
</ul>
<a name="newHashMap-java.util.List-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newHashMap</h4>
<pre>public static&nbsp;&lt;K,V&gt;&nbsp;java.util.HashMap&lt;K,V&gt;&nbsp;newHashMap(java.util.List&lt;K&gt;&nbsp;keys,
                                                      java.util.List&lt;V&gt;&nbsp;values)</pre>
<div class="block">根据等号左边的类型, 构造类型正确的HashMap.
 
 同时初始化元素.</div>
</li>
</ul>
<a name="newSortedMap--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newSortedMap</h4>
<pre>public static&nbsp;&lt;K extends java.lang.Comparable,V&gt;&nbsp;java.util.TreeMap&lt;K,V&gt;&nbsp;newSortedMap()</pre>
<div class="block">根据等号左边的类型，构造类型正确的TreeMap.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Maps.newTreeMap()</code></dd>
</dl>
</li>
</ul>
<a name="newSortedMap-java.util.Comparator-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newSortedMap</h4>
<pre>public static&nbsp;&lt;C,K extends C,V&gt;&nbsp;java.util.TreeMap&lt;K,V&gt;&nbsp;newSortedMap(java.util.Comparator&lt;C&gt;&nbsp;comparator)</pre>
<div class="block">根据等号左边的类型，构造类型正确的TreeMap.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Maps.newTreeMap(Comparator)</code></dd>
</dl>
</li>
</ul>
<a name="newEnumMap-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newEnumMap</h4>
<pre>public static&nbsp;&lt;K extends java.lang.Enum&lt;K&gt;,V&gt;&nbsp;java.util.EnumMap&lt;K,V&gt;&nbsp;newEnumMap(java.lang.Class&lt;K&gt;&nbsp;type)</pre>
<div class="block">相比HashMap，当key是枚举类时, 性能与空间占用俱佳.</div>
</li>
</ul>
<a name="newConcurrentHashMap--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newConcurrentHashMap</h4>
<pre>public static&nbsp;&lt;K,V&gt;&nbsp;java.util.concurrent.ConcurrentHashMap&lt;K,V&gt;&nbsp;newConcurrentHashMap()</pre>
<div class="block">根据等号左边的类型，构造类型正确的ConcurrentHashMap.</div>
</li>
</ul>
<a name="newConcurrentSortedMap--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newConcurrentSortedMap</h4>
<pre>public static&nbsp;&lt;K,V&gt;&nbsp;java.util.concurrent.ConcurrentSkipListMap&lt;K,V&gt;&nbsp;newConcurrentSortedMap()</pre>
<div class="block">根据等号左边的类型，构造类型正确的ConcurrentSkipListMap.</div>
</li>
</ul>
<a name="emptyMap--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>emptyMap</h4>
<pre>public static final&nbsp;&lt;K,V&gt;&nbsp;java.util.Map&lt;K,V&gt;&nbsp;emptyMap()</pre>
<div class="block">返回一个空的结构特殊的Map，节约空间.
 
 注意返回的Map不可写, 写入会抛出UnsupportedOperationException.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Collections.emptyMap()</code></dd>
</dl>
</li>
</ul>
<a name="emptyMapIfNull-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>emptyMapIfNull</h4>
<pre>public static&nbsp;&lt;K,V&gt;&nbsp;java.util.Map&lt;K,V&gt;&nbsp;emptyMapIfNull(java.util.Map&lt;K,V&gt;&nbsp;map)</pre>
<div class="block">如果map为null，转化为一个安全的空Map.
 
 注意返回的Map不可写, 写入会抛出UnsupportedOperationException.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Collections.emptyMap()</code></dd>
</dl>
</li>
</ul>
<a name="singletonMap-java.lang.Object-java.lang.Object-">
<!--   -->
</a><a name="singletonMap-K-V-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>singletonMap</h4>
<pre>public static&nbsp;&lt;K,V&gt;&nbsp;java.util.Map&lt;K,V&gt;&nbsp;singletonMap(K&nbsp;key,
                                                    V&nbsp;value)</pre>
<div class="block">返回一个只含一个元素但结构特殊的Map，节约空间.
 
 注意返回的Map不可写, 写入会抛出UnsupportedOperationException.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Collections.singletonMap(Object, Object)</code></dd>
</dl>
</li>
</ul>
<a name="unmodifiableMap-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unmodifiableMap</h4>
<pre>public static&nbsp;&lt;K,V&gt;&nbsp;java.util.Map&lt;K,V&gt;&nbsp;unmodifiableMap(java.util.Map&lt;? extends K,? extends V&gt;&nbsp;m)</pre>
<div class="block">返回包装后不可修改的Map.
 
 如果尝试修改，会抛出UnsupportedOperationException</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Collections.unmodifiableMap(Map)</code></dd>
</dl>
</li>
</ul>
<a name="unmodifiableSortedMap-java.util.SortedMap-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unmodifiableSortedMap</h4>
<pre>public static&nbsp;&lt;K,V&gt;&nbsp;java.util.SortedMap&lt;K,V&gt;&nbsp;unmodifiableSortedMap(java.util.SortedMap&lt;K,? extends V&gt;&nbsp;m)</pre>
<div class="block">返回包装后不可修改的有序Map.</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>Collections.unmodifiableSortedMap(SortedMap)</code></dd>
</dl>
</li>
</ul>
<a name="difference-java.util.Map-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>difference</h4>
<pre>public static&nbsp;&lt;K,V&gt;&nbsp;com.google.common.collect.MapDifference&lt;K,V&gt;&nbsp;difference(java.util.Map&lt;? extends K,? extends V&gt;&nbsp;left,
                                                                            java.util.Map&lt;? extends K,? extends V&gt;&nbsp;right)</pre>
<div class="block">对两个Map进行比较，返回MapDifference，然后各种妙用.
 
 包括key的差集，key的交集，以及key相同但value不同的元素。</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>MapDifference</code></dd>
</dl>
</li>
</ul>
<a name="sortByValue-java.util.Map-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sortByValue</h4>
<pre>public static&nbsp;&lt;K,V extends java.lang.Comparable&gt;&nbsp;java.util.Map&lt;K,V&gt;&nbsp;sortByValue(java.util.Map&lt;K,V&gt;&nbsp;map,
                                                                                boolean&nbsp;reverse)</pre>
<div class="block">对一个Map按Value进行排序，返回排序LinkedHashMap，多用于Value是Counter的情况.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>reverse</code> - 按Value的倒序 or 正序排列</dd>
</dl>
</li>
</ul>
<a name="sortByValue-java.util.Map-java.util.Comparator-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sortByValue</h4>
<pre>public static&nbsp;&lt;K,V&gt;&nbsp;java.util.Map&lt;K,V&gt;&nbsp;sortByValue(java.util.Map&lt;K,V&gt;&nbsp;map,
                                                   java.util.Comparator&lt;? super V&gt;&nbsp;comparator)</pre>
<div class="block">对一个Map按Value进行排序，返回排序LinkedHashMap.</div>
</li>
</ul>
<a name="topNByValue-java.util.Map-boolean-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>topNByValue</h4>
<pre>public static&nbsp;&lt;K,V extends java.lang.Comparable&gt;&nbsp;java.util.Map&lt;K,V&gt;&nbsp;topNByValue(java.util.Map&lt;K,V&gt;&nbsp;map,
                                                                                boolean&nbsp;reverse,
                                                                                int&nbsp;n)</pre>
<div class="block">对一个Map按Value进行排序，返回排序LinkedHashMap，最多只返回n条，多用于Value是Counter的情况.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>reverse</code> - 按Value的倒序 or 正序排列</dd>
</dl>
</li>
</ul>
<a name="topNByValue-java.util.Map-java.util.Comparator-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>topNByValue</h4>
<pre>public static&nbsp;&lt;K,V&gt;&nbsp;java.util.Map&lt;K,V&gt;&nbsp;topNByValue(java.util.Map&lt;K,V&gt;&nbsp;map,
                                                   java.util.Comparator&lt;? super V&gt;&nbsp;comparator,
                                                   int&nbsp;n)</pre>
<div class="block">对一个Map按Value进行排序，返回排序LinkedHashMap, 最多只返回n条，多用于Value是Counter的情况.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/collection/ListUtilTest.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/collection/MapUtil.ValueCreator.html" title="com.huazheng.tunny.tools.collection中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/collection/MapUtil.html" target="_top">框架</a></li>
<li><a href="MapUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li><a href="#nested.class.summary">嵌套</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
