<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:35 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>QueueUtil</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="QueueUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/collection/MoreQueues.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/collection/QueueUtilTest.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/collection/QueueUtil.html" target="_top">框架</a></li>
<li><a href="QueueUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.collection</div>
<h2 title="类 QueueUtil" class="title">类 QueueUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.collection.QueueUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">QueueUtil</span>
extends java.lang.Object</pre>
<div class="block">Queue工具集.
 
 各种Queue，Dequeue的创建</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/QueueUtil.html#QueueUtil--">QueueUtil</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static &lt;E&gt;&nbsp;java.util.concurrent.ArrayBlockingQueue&lt;E&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/QueueUtil.html#newArrayBlockingQueue-int-">newArrayBlockingQueue</a></span>(int&nbsp;capacity)</code>
<div class="block">创建并发阻塞情况下，长度受限，更节约内存，但共用一把锁的队列（无双端队列实现）.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static &lt;E&gt;&nbsp;java.util.ArrayDeque&lt;E&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/QueueUtil.html#newArrayDeque-int-">newArrayDeque</a></span>(int&nbsp;initSize)</code>
<div class="block">创建ArrayDeque (JDK无ArrayQueue)
 
 需设置初始长度，默认为16，数组满时成倍扩容</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static &lt;E&gt;&nbsp;java.util.concurrent.LinkedBlockingDeque&lt;E&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/QueueUtil.html#newBlockingDeque-int-">newBlockingDeque</a></span>(int&nbsp;capacity)</code>
<div class="block">创建并发阻塞情况下，长度受限，头队尾两把锁, 但使用更多内存的双端队列.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static &lt;E&gt;&nbsp;java.util.concurrent.LinkedBlockingDeque&lt;E&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/QueueUtil.html#newBlockingUnlimitDeque--">newBlockingUnlimitDeque</a></span>()</code>
<div class="block">创建并发阻塞情况下，长度不受限的双端队列.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static &lt;E&gt;&nbsp;java.util.concurrent.LinkedBlockingQueue&lt;E&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/QueueUtil.html#newBlockingUnlimitQueue--">newBlockingUnlimitQueue</a></span>()</code>
<div class="block">创建并发阻塞情况下，长度不受限的队列.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static &lt;E&gt;&nbsp;java.util.Deque&lt;E&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/QueueUtil.html#newConcurrentNonBlockingDeque--">newConcurrentNonBlockingDeque</a></span>()</code>
<div class="block">创建无阻塞情况下，性能最优的并发双端队列</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static &lt;E&gt;&nbsp;java.util.concurrent.ConcurrentLinkedQueue&lt;E&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/QueueUtil.html#newConcurrentNonBlockingQueue--">newConcurrentNonBlockingQueue</a></span>()</code>
<div class="block">创建无阻塞情况下，性能最优的并发队列</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static &lt;E&gt;&nbsp;java.util.concurrent.LinkedBlockingQueue&lt;E&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/QueueUtil.html#newLinkedBlockingQueue-int-">newLinkedBlockingQueue</a></span>(int&nbsp;capacity)</code>
<div class="block">创建并发阻塞情况下，长度受限，头队尾两把锁, 但使用更多内存的队列.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static &lt;E&gt;&nbsp;java.util.LinkedList&lt;E&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/collection/QueueUtil.html#newLinkedDeque--">newLinkedDeque</a></span>()</code>
<div class="block">创建LinkedDeque (LinkedList实现了Deque接口)</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="QueueUtil--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>QueueUtil</h4>
<pre>public&nbsp;QueueUtil()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="newArrayDeque-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newArrayDeque</h4>
<pre>public static&nbsp;&lt;E&gt;&nbsp;java.util.ArrayDeque&lt;E&gt;&nbsp;newArrayDeque(int&nbsp;initSize)</pre>
<div class="block">创建ArrayDeque (JDK无ArrayQueue)
 
 需设置初始长度，默认为16，数组满时成倍扩容</div>
</li>
</ul>
<a name="newLinkedDeque--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newLinkedDeque</h4>
<pre>public static&nbsp;&lt;E&gt;&nbsp;java.util.LinkedList&lt;E&gt;&nbsp;newLinkedDeque()</pre>
<div class="block">创建LinkedDeque (LinkedList实现了Deque接口)</div>
</li>
</ul>
<a name="newConcurrentNonBlockingQueue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newConcurrentNonBlockingQueue</h4>
<pre>public static&nbsp;&lt;E&gt;&nbsp;java.util.concurrent.ConcurrentLinkedQueue&lt;E&gt;&nbsp;newConcurrentNonBlockingQueue()</pre>
<div class="block">创建无阻塞情况下，性能最优的并发队列</div>
</li>
</ul>
<a name="newConcurrentNonBlockingDeque--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newConcurrentNonBlockingDeque</h4>
<pre>public static&nbsp;&lt;E&gt;&nbsp;java.util.Deque&lt;E&gt;&nbsp;newConcurrentNonBlockingDeque()</pre>
<div class="block">创建无阻塞情况下，性能最优的并发双端队列</div>
</li>
</ul>
<a name="newBlockingUnlimitQueue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newBlockingUnlimitQueue</h4>
<pre>public static&nbsp;&lt;E&gt;&nbsp;java.util.concurrent.LinkedBlockingQueue&lt;E&gt;&nbsp;newBlockingUnlimitQueue()</pre>
<div class="block">创建并发阻塞情况下，长度不受限的队列.
 
 长度不受限，即生产者不会因为满而阻塞，但消费者会因为空而阻塞.</div>
</li>
</ul>
<a name="newBlockingUnlimitDeque--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newBlockingUnlimitDeque</h4>
<pre>public static&nbsp;&lt;E&gt;&nbsp;java.util.concurrent.LinkedBlockingDeque&lt;E&gt;&nbsp;newBlockingUnlimitDeque()</pre>
<div class="block">创建并发阻塞情况下，长度不受限的双端队列.
 
 长度不受限，即生产者不会因为满而阻塞，但消费者会因为空而阻塞.</div>
</li>
</ul>
<a name="newArrayBlockingQueue-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newArrayBlockingQueue</h4>
<pre>public static&nbsp;&lt;E&gt;&nbsp;java.util.concurrent.ArrayBlockingQueue&lt;E&gt;&nbsp;newArrayBlockingQueue(int&nbsp;capacity)</pre>
<div class="block">创建并发阻塞情况下，长度受限，更节约内存，但共用一把锁的队列（无双端队列实现）.</div>
</li>
</ul>
<a name="newLinkedBlockingQueue-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newLinkedBlockingQueue</h4>
<pre>public static&nbsp;&lt;E&gt;&nbsp;java.util.concurrent.LinkedBlockingQueue&lt;E&gt;&nbsp;newLinkedBlockingQueue(int&nbsp;capacity)</pre>
<div class="block">创建并发阻塞情况下，长度受限，头队尾两把锁, 但使用更多内存的队列.</div>
</li>
</ul>
<a name="newBlockingDeque-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>newBlockingDeque</h4>
<pre>public static&nbsp;&lt;E&gt;&nbsp;java.util.concurrent.LinkedBlockingDeque&lt;E&gt;&nbsp;newBlockingDeque(int&nbsp;capacity)</pre>
<div class="block">创建并发阻塞情况下，长度受限，头队尾两把锁, 但使用更多内存的双端队列.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/collection/MoreQueues.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/collection/QueueUtilTest.html" title="com.huazheng.tunny.tools.collection中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/collection/QueueUtil.html" target="_top">框架</a></li>
<li><a href="QueueUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
