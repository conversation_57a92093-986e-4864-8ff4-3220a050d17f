<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:32 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>CryptoUtil</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CryptoUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../com/huazheng/tunny/tools/security/CryptoUtilTest.html" title="com.huazheng.tunny.tools.security中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/security/CryptoUtil.html" target="_top">框架</a></li>
<li><a href="CryptoUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.security</div>
<h2 title="类 CryptoUtil" class="title">类 CryptoUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.security.CryptoUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">CryptoUtil</span>
extends java.lang.Object</pre>
<div class="block">支持HMAC-SHA1消息签名 及 DES/AES对称加密的工具类.
 
 支持Hex与Base64两种编码方式.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/security/CryptoUtil.html#CryptoUtil--">CryptoUtil</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/security/CryptoUtil.html#aesDecrypt-byte:A-byte:A-">aesDecrypt</a></span>(byte[]&nbsp;input,
          byte[]&nbsp;key)</code>
<div class="block">使用AES解密字符串, 返回原始字符串.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/security/CryptoUtil.html#aesDecrypt-byte:A-byte:A-byte:A-">aesDecrypt</a></span>(byte[]&nbsp;input,
          byte[]&nbsp;key,
          byte[]&nbsp;iv)</code>
<div class="block">使用AES解密字符串, 返回原始字符串.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/security/CryptoUtil.html#aesEncrypt-byte:A-byte:A-">aesEncrypt</a></span>(byte[]&nbsp;input,
          byte[]&nbsp;key)</code>
<div class="block">使用AES加密原始字符串.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/security/CryptoUtil.html#aesEncrypt-byte:A-byte:A-byte:A-">aesEncrypt</a></span>(byte[]&nbsp;input,
          byte[]&nbsp;key,
          byte[]&nbsp;iv)</code>
<div class="block">使用AES加密原始字符串.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/security/CryptoUtil.html#generateAesKey--">generateAesKey</a></span>()</code>
<div class="block">生成AES密钥,返回字节数组, 默认长度为128位(16字节).</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/security/CryptoUtil.html#generateAesKey-int-">generateAesKey</a></span>(int&nbsp;keysize)</code>
<div class="block">生成AES密钥,可选长度为128,192,256位.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/security/CryptoUtil.html#generateHmacSha1Key--">generateHmacSha1Key</a></span>()</code>
<div class="block">生成HMAC-SHA1密钥,返回字节数组,长度为160位(20字节).</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/security/CryptoUtil.html#generateIV--">generateIV</a></span>()</code>
<div class="block">生成随机向量,默认大小为cipher.getBlockSize(), 16字节.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/security/CryptoUtil.html#hmacSha1-byte:A-byte:A-">hmacSha1</a></span>(byte[]&nbsp;input,
        byte[]&nbsp;key)</code>
<div class="block">使用HMAC-SHA1进行消息签名, 返回字节数组,长度为20字节.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/security/CryptoUtil.html#isMacValid-byte:A-byte:A-byte:A-">isMacValid</a></span>(byte[]&nbsp;expected,
          byte[]&nbsp;input,
          byte[]&nbsp;key)</code>
<div class="block">校验HMAC-SHA1签名是否正确.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="CryptoUtil--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CryptoUtil</h4>
<pre>public&nbsp;CryptoUtil()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="hmacSha1-byte:A-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hmacSha1</h4>
<pre>public static&nbsp;byte[]&nbsp;hmacSha1(byte[]&nbsp;input,
                              byte[]&nbsp;key)</pre>
<div class="block">使用HMAC-SHA1进行消息签名, 返回字节数组,长度为20字节.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>input</code> - 原始输入字符数组</dd>
<dd><code>key</code> - HMAC-SHA1密钥</dd>
</dl>
</li>
</ul>
<a name="isMacValid-byte:A-byte:A-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMacValid</h4>
<pre>public static&nbsp;boolean&nbsp;isMacValid(byte[]&nbsp;expected,
                                 byte[]&nbsp;input,
                                 byte[]&nbsp;key)</pre>
<div class="block">校验HMAC-SHA1签名是否正确.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>expected</code> - 已存在的签名</dd>
<dd><code>input</code> - 原始输入字符串</dd>
<dd><code>key</code> - 密钥</dd>
</dl>
</li>
</ul>
<a name="generateHmacSha1Key--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>generateHmacSha1Key</h4>
<pre>public static&nbsp;byte[]&nbsp;generateHmacSha1Key()</pre>
<div class="block">生成HMAC-SHA1密钥,返回字节数组,长度为160位(20字节). HMAC-SHA1算法对密钥无特殊要求, RFC2401建议最少长度为160位(20字节).</div>
</li>
</ul>
<a name="aesEncrypt-byte:A-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>aesEncrypt</h4>
<pre>public static&nbsp;byte[]&nbsp;aesEncrypt(byte[]&nbsp;input,
                                byte[]&nbsp;key)</pre>
<div class="block">使用AES加密原始字符串.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>input</code> - 原始输入字符数组</dd>
<dd><code>key</code> - 符合AES要求的密钥</dd>
</dl>
</li>
</ul>
<a name="aesEncrypt-byte:A-byte:A-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>aesEncrypt</h4>
<pre>public static&nbsp;byte[]&nbsp;aesEncrypt(byte[]&nbsp;input,
                                byte[]&nbsp;key,
                                byte[]&nbsp;iv)</pre>
<div class="block">使用AES加密原始字符串.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>input</code> - 原始输入字符数组</dd>
<dd><code>key</code> - 符合AES要求的密钥</dd>
<dd><code>iv</code> - 初始向量</dd>
</dl>
</li>
</ul>
<a name="aesDecrypt-byte:A-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>aesDecrypt</h4>
<pre>public static&nbsp;java.lang.String&nbsp;aesDecrypt(byte[]&nbsp;input,
                                          byte[]&nbsp;key)</pre>
<div class="block">使用AES解密字符串, 返回原始字符串.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>input</code> - Hex编码的加密字符串</dd>
<dd><code>key</code> - 符合AES要求的密钥</dd>
</dl>
</li>
</ul>
<a name="aesDecrypt-byte:A-byte:A-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>aesDecrypt</h4>
<pre>public static&nbsp;java.lang.String&nbsp;aesDecrypt(byte[]&nbsp;input,
                                          byte[]&nbsp;key,
                                          byte[]&nbsp;iv)</pre>
<div class="block">使用AES解密字符串, 返回原始字符串.</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>input</code> - Hex编码的加密字符串</dd>
<dd><code>key</code> - 符合AES要求的密钥</dd>
<dd><code>iv</code> - 初始向量</dd>
</dl>
</li>
</ul>
<a name="generateAesKey--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>generateAesKey</h4>
<pre>public static&nbsp;byte[]&nbsp;generateAesKey()</pre>
<div class="block">生成AES密钥,返回字节数组, 默认长度为128位(16字节).</div>
</li>
</ul>
<a name="generateAesKey-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>generateAesKey</h4>
<pre>public static&nbsp;byte[]&nbsp;generateAesKey(int&nbsp;keysize)</pre>
<div class="block">生成AES密钥,可选长度为128,192,256位.</div>
</li>
</ul>
<a name="generateIV--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>generateIV</h4>
<pre>public static&nbsp;byte[]&nbsp;generateIV()</pre>
<div class="block">生成随机向量,默认大小为cipher.getBlockSize(), 16字节.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../com/huazheng/tunny/tools/security/CryptoUtilTest.html" title="com.huazheng.tunny.tools.security中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/security/CryptoUtil.html" target="_top">框架</a></li>
<li><a href="CryptoUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
