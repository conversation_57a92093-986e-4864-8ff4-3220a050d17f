<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:33 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ReflectionUtil</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ReflectionUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9,"i14":9,"i15":9,"i16":9,"i17":9,"i18":9,"i19":9,"i20":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.TestBean3.html" title="com.huazheng.tunny.tools.reflect中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.html" title="com.huazheng.tunny.tools.reflect中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/reflect/ReflectionUtil.html" target="_top">框架</a></li>
<li><a href="ReflectionUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.reflect</div>
<h2 title="类 ReflectionUtil" class="title">类 ReflectionUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.reflect.ReflectionUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ReflectionUtil</span>
extends java.lang.Object</pre>
<div class="block">反射工具类.
 
 所有反射均无视modifier的范围限制，同时将反射的Checked异常转为UnChecked异常。
 
 需要平衡性能较差的一次性调用，以及高性能的基于预先获取的Method/Filed对象反复调用两种用法
 
 1. 获取方法与属性 (兼容了原始类型/接口/抽象类的参数, 并默认将方法与属性设为可访问)
 
 2. 方法调用.
 
 3. 构造函数.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#ReflectionUtil--">ReflectionUtil</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static java.lang.RuntimeException</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#convertReflectionExceptionToUnchecked-java.lang.Exception-">convertReflectionExceptionToUnchecked</a></span>(java.lang.Exception&nbsp;e)</code>
<div class="block">将反射时的checked exception转换为unchecked exception.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static java.lang.reflect.Method</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#getAccessibleMethodByName-java.lang.Class-java.lang.String-">getAccessibleMethodByName</a></span>(java.lang.Class&nbsp;clazz,
                         java.lang.String&nbsp;methodName)</code>
<div class="block">循环向上转型, 获取对象的DeclaredMethod,并强制设置为可访问.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static java.lang.reflect.Field</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#getField-java.lang.Class-java.lang.String-">getField</a></span>(java.lang.Class&nbsp;clazz,
        java.lang.String&nbsp;fieldName)</code>
<div class="block">循环向上转型, 获取对象的DeclaredField, 并强制设置为可访问.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#getFieldValue-java.lang.Object-java.lang.reflect.Field-">getFieldValue</a></span>(java.lang.Object&nbsp;obj,
             java.lang.reflect.Field&nbsp;field)</code>
<div class="block">使用已获取的Field, 直接读取对象属性值, 不经过getter函数.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#getFieldValue-java.lang.Object-java.lang.String-">getFieldValue</a></span>(java.lang.Object&nbsp;obj,
             java.lang.String&nbsp;fieldName)</code>
<div class="block">直接读取对象属性值, 无视private/protected修饰符, 不经过getter函数.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static java.lang.reflect.Method</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#getGetterMethod-java.lang.Class-java.lang.String-">getGetterMethod</a></span>(java.lang.Class&lt;?&gt;&nbsp;clazz,
               java.lang.String&nbsp;propertyName)</code>
<div class="block">循环遍历，按属性名获取前缀为get或is的函数，并设为可访问</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static java.lang.reflect.Method</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#getMethod-java.lang.Class-java.lang.String-java.lang.Class...-">getMethod</a></span>(java.lang.Class&lt;?&gt;&nbsp;clazz,
         java.lang.String&nbsp;methodName,
         java.lang.Class&lt;?&gt;...&nbsp;parameterTypes)</code>
<div class="block">循环向上转型, 获取对象的DeclaredMethod, 并强制设置为可访问.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#getProperty-java.lang.Object-java.lang.String-">getProperty</a></span>(java.lang.Object&nbsp;obj,
           java.lang.String&nbsp;propertyName)</code>
<div class="block">先尝试用Getter函数读取, 如果不存在则直接读取变量.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static java.lang.reflect.Method</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#getSetterMethod-java.lang.Class-java.lang.String-java.lang.Class-">getSetterMethod</a></span>(java.lang.Class&lt;?&gt;&nbsp;clazz,
               java.lang.String&nbsp;propertyName,
               java.lang.Class&lt;?&gt;&nbsp;parameterType)</code>
<div class="block">循环遍历，按属性名获取前缀为set的函数，并设为可访问</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#invokeConstructor-java.lang.Class-java.lang.Object...-">invokeConstructor</a></span>(java.lang.Class&lt;T&gt;&nbsp;cls,
                 java.lang.Object...&nbsp;args)</code>
<div class="block">调用构造函数.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#invokeGetter-java.lang.Object-java.lang.String-">invokeGetter</a></span>(java.lang.Object&nbsp;obj,
            java.lang.String&nbsp;propertyName)</code>
<div class="block">调用Getter方法, 无视private/protected修饰符.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#invokeMethod-java.lang.Object-java.lang.reflect.Method-java.lang.Object...-">invokeMethod</a></span>(java.lang.Object&nbsp;obj,
            java.lang.reflect.Method&nbsp;method,
            java.lang.Object...&nbsp;args)</code>
<div class="block">调用预先获取的Method，用于反复调用的场景</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#invokeMethod-java.lang.Object-java.lang.String-java.lang.Object...-">invokeMethod</a></span>(java.lang.Object&nbsp;obj,
            java.lang.String&nbsp;methodName,
            java.lang.Object...&nbsp;args)</code>
<div class="block">反射调用对象方法, 无视private/protected修饰符.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#invokeMethod-java.lang.Object-java.lang.String-java.lang.Object:A-java.lang.Class:A-">invokeMethod</a></span>(java.lang.Object&nbsp;obj,
            java.lang.String&nbsp;methodName,
            java.lang.Object[]&nbsp;args,
            java.lang.Class&lt;?&gt;[]&nbsp;parameterTypes)</code>
<div class="block">反射调用对象方法, 无视private/protected修饰符.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;T</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#invokeMethodByName-java.lang.Object-java.lang.String-java.lang.Object:A-">invokeMethodByName</a></span>(java.lang.Object&nbsp;obj,
                  java.lang.String&nbsp;methodName,
                  java.lang.Object[]&nbsp;args)</code>
<div class="block">反射调用对象方法, 无视private/protected修饰符
 
 只匹配函数名，如果有多个同名函数调用第一个.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#invokeSetter-java.lang.Object-java.lang.String-java.lang.Object-">invokeSetter</a></span>(java.lang.Object&nbsp;obj,
            java.lang.String&nbsp;propertyName,
            java.lang.Object&nbsp;value)</code>
<div class="block">调用Setter方法, 无视private/protected修饰符, 按传入value的类型匹配函数.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#makeAccessible-java.lang.reflect.Field-">makeAccessible</a></span>(java.lang.reflect.Field&nbsp;field)</code>
<div class="block">改变private/protected的成员变量为可访问，尽量不进行改变，避免JDK的SecurityManager抱怨。</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#makeAccessible-java.lang.reflect.Method-">makeAccessible</a></span>(java.lang.reflect.Method&nbsp;method)</code>
<div class="block">改变private/protected的方法为可访问，尽量不进行改变，避免JDK的SecurityManager抱怨。</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#setField-java.lang.Object-java.lang.reflect.Field-java.lang.Object-">setField</a></span>(java.lang.Object&nbsp;obj,
        java.lang.reflect.Field&nbsp;field,
        java.lang.Object&nbsp;value)</code>
<div class="block">使用预先获取的Field, 直接读取对象属性值, 不经过setter函数.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#setFieldValue-java.lang.Object-java.lang.String-java.lang.Object-">setFieldValue</a></span>(java.lang.Object&nbsp;obj,
             java.lang.String&nbsp;fieldName,
             java.lang.Object&nbsp;value)</code>
<div class="block">直接设置对象属性值, 无视private/protected修饰符, 不经过setter函数.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ReflectionUtil.html#setProperty-java.lang.Object-java.lang.String-java.lang.Object-">setProperty</a></span>(java.lang.Object&nbsp;obj,
           java.lang.String&nbsp;propertyName,
           java.lang.Object&nbsp;value)</code>
<div class="block">先尝试用Setter函数写入, 如果不存在则直接写入变量, 按传入value的类型匹配函数.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="ReflectionUtil--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ReflectionUtil</h4>
<pre>public&nbsp;ReflectionUtil()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getSetterMethod-java.lang.Class-java.lang.String-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSetterMethod</h4>
<pre>public static&nbsp;java.lang.reflect.Method&nbsp;getSetterMethod(java.lang.Class&lt;?&gt;&nbsp;clazz,
                                                       java.lang.String&nbsp;propertyName,
                                                       java.lang.Class&lt;?&gt;&nbsp;parameterType)</pre>
<div class="block">循环遍历，按属性名获取前缀为set的函数，并设为可访问</div>
</li>
</ul>
<a name="getGetterMethod-java.lang.Class-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGetterMethod</h4>
<pre>public static&nbsp;java.lang.reflect.Method&nbsp;getGetterMethod(java.lang.Class&lt;?&gt;&nbsp;clazz,
                                                       java.lang.String&nbsp;propertyName)</pre>
<div class="block">循环遍历，按属性名获取前缀为get或is的函数，并设为可访问</div>
</li>
</ul>
<a name="getMethod-java.lang.Class-java.lang.String-java.lang.Class...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMethod</h4>
<pre>public static&nbsp;java.lang.reflect.Method&nbsp;getMethod(java.lang.Class&lt;?&gt;&nbsp;clazz,
                                                 java.lang.String&nbsp;methodName,
                                                 java.lang.Class&lt;?&gt;...&nbsp;parameterTypes)</pre>
<div class="block">循环向上转型, 获取对象的DeclaredMethod, 并强制设置为可访问.
 
 如向上转型到Object仍无法找到, 返回null.
 
 匹配函数名+参数类型.
 
 方法需要被多次调用时，先使用本函数先取得Method，然后调用Method.invoke(Object obj, Object... args)
 
 因为getMethod() 不能获取父类的private函数, 因此采用循环向上的getDeclaredMethod();</div>
</li>
</ul>
<a name="getAccessibleMethodByName-java.lang.Class-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAccessibleMethodByName</h4>
<pre>public static&nbsp;java.lang.reflect.Method&nbsp;getAccessibleMethodByName(java.lang.Class&nbsp;clazz,
                                                                 java.lang.String&nbsp;methodName)</pre>
<div class="block">循环向上转型, 获取对象的DeclaredMethod,并强制设置为可访问.
 
 如向上转型到Object仍无法找到, 返回null.
 
 只匹配函数名, 如果有多个同名函数返回第一个
 
 方法需要被多次调用时，先使用本函数先取得Method，然后调用Method.invoke(Object obj, Object... args)
 
 因为getMethod() 不能获取父类的private函数, 因此采用循环向上的getDeclaredMethods()</div>
</li>
</ul>
<a name="getField-java.lang.Class-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getField</h4>
<pre>public static&nbsp;java.lang.reflect.Field&nbsp;getField(java.lang.Class&nbsp;clazz,
                                               java.lang.String&nbsp;fieldName)</pre>
<div class="block">循环向上转型, 获取对象的DeclaredField, 并强制设置为可访问.
 
 如向上转型到Object仍无法找到, 返回null.
 
 因为getFiled()不能获取父类的private属性, 因此采用循环向上的getDeclaredField();</div>
</li>
</ul>
<a name="invokeGetter-java.lang.Object-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>invokeGetter</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;T&nbsp;invokeGetter(java.lang.Object&nbsp;obj,
                                 java.lang.String&nbsp;propertyName)</pre>
<div class="block">调用Getter方法, 无视private/protected修饰符.
 
 性能较差, 用于单次调用的场景</div>
</li>
</ul>
<a name="invokeSetter-java.lang.Object-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>invokeSetter</h4>
<pre>public static&nbsp;void&nbsp;invokeSetter(java.lang.Object&nbsp;obj,
                                java.lang.String&nbsp;propertyName,
                                java.lang.Object&nbsp;value)</pre>
<div class="block">调用Setter方法, 无视private/protected修饰符, 按传入value的类型匹配函数.
 
 性能较差, 用于单次调用的场景</div>
</li>
</ul>
<a name="getFieldValue-java.lang.Object-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFieldValue</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;T&nbsp;getFieldValue(java.lang.Object&nbsp;obj,
                                  java.lang.String&nbsp;fieldName)</pre>
<div class="block">直接读取对象属性值, 无视private/protected修饰符, 不经过getter函数.
 
 性能较差, 用于单次调用的场景</div>
</li>
</ul>
<a name="getFieldValue-java.lang.Object-java.lang.reflect.Field-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFieldValue</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;T&nbsp;getFieldValue(java.lang.Object&nbsp;obj,
                                  java.lang.reflect.Field&nbsp;field)</pre>
<div class="block">使用已获取的Field, 直接读取对象属性值, 不经过getter函数.
 
 用于反复调用的场景.</div>
</li>
</ul>
<a name="setFieldValue-java.lang.Object-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFieldValue</h4>
<pre>public static&nbsp;void&nbsp;setFieldValue(java.lang.Object&nbsp;obj,
                                 java.lang.String&nbsp;fieldName,
                                 java.lang.Object&nbsp;value)</pre>
<div class="block">直接设置对象属性值, 无视private/protected修饰符, 不经过setter函数.
 
 性能较差, 用于单次调用的场景</div>
</li>
</ul>
<a name="setField-java.lang.Object-java.lang.reflect.Field-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setField</h4>
<pre>public static&nbsp;void&nbsp;setField(java.lang.Object&nbsp;obj,
                            java.lang.reflect.Field&nbsp;field,
                            java.lang.Object&nbsp;value)</pre>
<div class="block">使用预先获取的Field, 直接读取对象属性值, 不经过setter函数.
 
 用于反复调用的场景.</div>
</li>
</ul>
<a name="getProperty-java.lang.Object-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProperty</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;T&nbsp;getProperty(java.lang.Object&nbsp;obj,
                                java.lang.String&nbsp;propertyName)</pre>
<div class="block">先尝试用Getter函数读取, 如果不存在则直接读取变量.
 
 性能较差, 用于单次调用的场景</div>
</li>
</ul>
<a name="setProperty-java.lang.Object-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProperty</h4>
<pre>public static&nbsp;void&nbsp;setProperty(java.lang.Object&nbsp;obj,
                               java.lang.String&nbsp;propertyName,
                               java.lang.Object&nbsp;value)</pre>
<div class="block">先尝试用Setter函数写入, 如果不存在则直接写入变量, 按传入value的类型匹配函数.
 
 性能较差, 用于单次调用的场景</div>
</li>
</ul>
<a name="invokeMethod-java.lang.Object-java.lang.String-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>invokeMethod</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;T&nbsp;invokeMethod(java.lang.Object&nbsp;obj,
                                 java.lang.String&nbsp;methodName,
                                 java.lang.Object...&nbsp;args)</pre>
<div class="block">反射调用对象方法, 无视private/protected修饰符.
 
 根据传入参数的实际类型进行匹配, 支持方法参数定义是接口，父类，原子类型等情况
 
 性能较差，仅用于单次调用.</div>
</li>
</ul>
<a name="invokeMethod-java.lang.Object-java.lang.String-java.lang.Object:A-java.lang.Class:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>invokeMethod</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;T&nbsp;invokeMethod(java.lang.Object&nbsp;obj,
                                 java.lang.String&nbsp;methodName,
                                 java.lang.Object[]&nbsp;args,
                                 java.lang.Class&lt;?&gt;[]&nbsp;parameterTypes)</pre>
<div class="block">反射调用对象方法, 无视private/protected修饰符.
 
 根据参数类型参数进行匹配, 支持方法参数定义是接口，父类，原子类型等情况
 
 性能较低，仅用于单次调用.</div>
</li>
</ul>
<a name="invokeMethodByName-java.lang.Object-java.lang.String-java.lang.Object:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>invokeMethodByName</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;T&nbsp;invokeMethodByName(java.lang.Object&nbsp;obj,
                                       java.lang.String&nbsp;methodName,
                                       java.lang.Object[]&nbsp;args)</pre>
<div class="block">反射调用对象方法, 无视private/protected修饰符
 
 只匹配函数名，如果有多个同名函数调用第一个. 用于确信只有一个同名函数, 但参数类型不确定的情况.
 
 性能较低，仅用于单次调用.</div>
</li>
</ul>
<a name="invokeMethod-java.lang.Object-java.lang.reflect.Method-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>invokeMethod</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;T&nbsp;invokeMethod(java.lang.Object&nbsp;obj,
                                 java.lang.reflect.Method&nbsp;method,
                                 java.lang.Object...&nbsp;args)</pre>
<div class="block">调用预先获取的Method，用于反复调用的场景</div>
</li>
</ul>
<a name="invokeConstructor-java.lang.Class-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>invokeConstructor</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;T&nbsp;invokeConstructor(java.lang.Class&lt;T&gt;&nbsp;cls,
                                      java.lang.Object...&nbsp;args)</pre>
<div class="block">调用构造函数.</div>
</li>
</ul>
<a name="makeAccessible-java.lang.reflect.Method-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>makeAccessible</h4>
<pre>public static&nbsp;void&nbsp;makeAccessible(java.lang.reflect.Method&nbsp;method)</pre>
<div class="block">改变private/protected的方法为可访问，尽量不进行改变，避免JDK的SecurityManager抱怨。</div>
</li>
</ul>
<a name="makeAccessible-java.lang.reflect.Field-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>makeAccessible</h4>
<pre>public static&nbsp;void&nbsp;makeAccessible(java.lang.reflect.Field&nbsp;field)</pre>
<div class="block">改变private/protected的成员变量为可访问，尽量不进行改变，避免JDK的SecurityManager抱怨。</div>
</li>
</ul>
<a name="convertReflectionExceptionToUnchecked-java.lang.Exception-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>convertReflectionExceptionToUnchecked</h4>
<pre>public static&nbsp;java.lang.RuntimeException&nbsp;convertReflectionExceptionToUnchecked(java.lang.Exception&nbsp;e)</pre>
<div class="block">将反射时的checked exception转换为unchecked exception.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.TestBean3.html" title="com.huazheng.tunny.tools.reflect中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/reflect/ReflectionUtilTest.html" title="com.huazheng.tunny.tools.reflect中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/reflect/ReflectionUtil.html" target="_top">框架</a></li>
<li><a href="ReflectionUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
