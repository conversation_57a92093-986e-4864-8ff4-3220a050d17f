<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:33 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ClassUtil</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ClassUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassloaderUtilTest.html" title="com.huazheng.tunny.tools.reflect中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.html" title="com.huazheng.tunny.tools.reflect中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/reflect/ClassUtil.html" target="_top">框架</a></li>
<li><a href="ClassUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.reflect</div>
<h2 title="类 ClassUtil" class="title">类 ClassUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.reflect.ClassUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ClassUtil</span>
extends java.lang.Object</pre>
<div class="block">获取Class信息的工具类
 
 1. 获取类名，包名，循环向上的全部父类，全部接口
 
 2. 其他便捷函数</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtil.html#ClassUtil--">ClassUtil</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static java.util.List&lt;java.lang.Class&lt;?&gt;&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtil.html#getAllInterfaces-java.lang.Class-">getAllInterfaces</a></span>(java.lang.Class&lt;?&gt;&nbsp;cls)</code>
<div class="block">递归返回本类及所有基类继承的接口，及接口继承的接口，比Spring中的相同实现完整</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static java.util.List&lt;java.lang.Class&lt;?&gt;&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtil.html#getAllSuperclasses-java.lang.Class-">getAllSuperclasses</a></span>(java.lang.Class&lt;?&gt;&nbsp;cls)</code>
<div class="block">递归返回所有的SupperClasses，包含Object.class</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.lang.Class&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtil.html#getClassGenericType-java.lang.Class-">getClassGenericType</a></span>(java.lang.Class&nbsp;clazz)</code>
<div class="block">通过反射, 获得Class定义中声明的泛型参数的类型,
 
 注意泛型必须定义在父类处.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static java.lang.Class</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtil.html#getClassGenericType-java.lang.Class-int-">getClassGenericType</a></span>(java.lang.Class&nbsp;clazz,
                   int&nbsp;index)</code>
<div class="block">通过反射, 获得Class定义中声明的父类的泛型参数的类型.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtil.html#getPackageName-java.lang.Class-">getPackageName</a></span>(java.lang.Class&lt;?&gt;&nbsp;cls)</code>
<div class="block">返回PackageName</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtil.html#getPackageName-java.lang.String-">getPackageName</a></span>(java.lang.String&nbsp;className)</code>
<div class="block">返回PackageName</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtil.html#getShortClassName-java.lang.Class-">getShortClassName</a></span>(java.lang.Class&lt;?&gt;&nbsp;cls)</code>
<div class="block">返回短Class名, 不包含PackageName.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtil.html#getShortClassName-java.lang.String-">getShortClassName</a></span>(java.lang.String&nbsp;className)</code>
<div class="block">返回Class名，不包含PackageName
 
 内部类的话，返回"主类.内部类"</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtil.html#isSubClassOrInterfaceOf-java.lang.Class-java.lang.Class-">isSubClassOrInterfaceOf</a></span>(java.lang.Class&nbsp;subclass,
                       java.lang.Class&nbsp;superclass)</code>
<div class="block">https://github.com/linkedin/linkedin-utils/blob/master/org.linkedin.util-core/src/main/java/org/linkedin/util/reflect/ReflectUtils.java
 
 The purpose of this method is somewhat to provide a better naming / documentation than the javadoc of
 <code>Class.isAssignableFrom</code> method.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static java.lang.Class&lt;?&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtil.html#unwrapCglib-java.lang.Object-">unwrapCglib</a></span>(java.lang.Object&nbsp;instance)</code>
<div class="block">获取CGLib处理过后的实体的原Class.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="ClassUtil--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ClassUtil</h4>
<pre>public&nbsp;ClassUtil()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getShortClassName-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShortClassName</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getShortClassName(java.lang.Class&lt;?&gt;&nbsp;cls)</pre>
<div class="block">返回短Class名, 不包含PackageName.
 
 内部类的话，返回"主类.内部类"</div>
</li>
</ul>
<a name="getShortClassName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShortClassName</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getShortClassName(java.lang.String&nbsp;className)</pre>
<div class="block">返回Class名，不包含PackageName
 
 内部类的话，返回"主类.内部类"</div>
</li>
</ul>
<a name="getPackageName-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPackageName</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getPackageName(java.lang.Class&lt;?&gt;&nbsp;cls)</pre>
<div class="block">返回PackageName</div>
</li>
</ul>
<a name="getPackageName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPackageName</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getPackageName(java.lang.String&nbsp;className)</pre>
<div class="block">返回PackageName</div>
</li>
</ul>
<a name="getAllSuperclasses-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAllSuperclasses</h4>
<pre>public static&nbsp;java.util.List&lt;java.lang.Class&lt;?&gt;&gt;&nbsp;getAllSuperclasses(java.lang.Class&lt;?&gt;&nbsp;cls)</pre>
<div class="block">递归返回所有的SupperClasses，包含Object.class</div>
</li>
</ul>
<a name="getAllInterfaces-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAllInterfaces</h4>
<pre>public static&nbsp;java.util.List&lt;java.lang.Class&lt;?&gt;&gt;&nbsp;getAllInterfaces(java.lang.Class&lt;?&gt;&nbsp;cls)</pre>
<div class="block">递归返回本类及所有基类继承的接口，及接口继承的接口，比Spring中的相同实现完整</div>
</li>
</ul>
<a name="isSubClassOrInterfaceOf-java.lang.Class-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isSubClassOrInterfaceOf</h4>
<pre>public static&nbsp;boolean&nbsp;isSubClassOrInterfaceOf(java.lang.Class&nbsp;subclass,
                                              java.lang.Class&nbsp;superclass)</pre>
<div class="block">https://github.com/linkedin/linkedin-utils/blob/master/org.linkedin.util-core/src/main/java/org/linkedin/util/reflect/ReflectUtils.java
 
 The purpose of this method is somewhat to provide a better naming / documentation than the javadoc of
 <code>Class.isAssignableFrom</code> method.</div>
<dl>
<dt><span class="returnLabel">返回:</span></dt>
<dd><code>true</code> if subclass is a subclass or sub interface of superclass</dd>
</dl>
</li>
</ul>
<a name="unwrapCglib-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unwrapCglib</h4>
<pre>public static&nbsp;java.lang.Class&lt;?&gt;&nbsp;unwrapCglib(java.lang.Object&nbsp;instance)</pre>
<div class="block">获取CGLib处理过后的实体的原Class.</div>
</li>
</ul>
<a name="getClassGenericType-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getClassGenericType</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;java.lang.Class&lt;T&gt;&nbsp;getClassGenericType(java.lang.Class&nbsp;clazz)</pre>
<div class="block">通过反射, 获得Class定义中声明的泛型参数的类型,
 
 注意泛型必须定义在父类处. 这是唯一可以通过反射从泛型获得Class实例的地方.
 
 如无法找到, 返回Object.class.
 
 eg. public UserDao extends HibernateDao<User></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>clazz</code> - The class to introspect</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>the first generic declaration, or Object.class if cannot be determined</dd>
</dl>
</li>
</ul>
<a name="getClassGenericType-java.lang.Class-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getClassGenericType</h4>
<pre>public static&nbsp;java.lang.Class&nbsp;getClassGenericType(java.lang.Class&nbsp;clazz,
                                                  int&nbsp;index)</pre>
<div class="block">通过反射, 获得Class定义中声明的父类的泛型参数的类型.
 
 注意泛型必须定义在父类处. 这是唯一可以通过反射从泛型获得Class实例的地方.
 
 如无法找到, 返回Object.class.
 
 如public UserDao extends HibernateDao<User,Long></div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>clazz</code> - clazz The class to introspect</dd>
<dd><code>index</code> - the Index of the generic declaration, start from 0.</dd>
<dt><span class="returnLabel">返回:</span></dt>
<dd>the index generic declaration, or Object.class if cannot be determined</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassloaderUtilTest.html" title="com.huazheng.tunny.tools.reflect中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.html" title="com.huazheng.tunny.tools.reflect中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/reflect/ClassUtil.html" target="_top">框架</a></li>
<li><a href="ClassUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
