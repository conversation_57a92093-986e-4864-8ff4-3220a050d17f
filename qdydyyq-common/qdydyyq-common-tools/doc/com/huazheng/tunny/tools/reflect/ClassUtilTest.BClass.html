<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:33 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ClassUtilTest.BClass</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ClassUtilTest.BClass";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.BAnnotation.html" title="com.huazheng.tunny.tools.reflect中的注释"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.BInterface.html" title="com.huazheng.tunny.tools.reflect中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/reflect/ClassUtilTest.BClass.html" target="_top">框架</a></li>
<li><a href="ClassUtilTest.BClass.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.reflect</div>
<h2 title="类 ClassUtilTest.BClass" class="title">类 ClassUtilTest.BClass</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.AClass.html" title="com.huazheng.tunny.tools.reflect中的类">com.huazheng.tunny.tools.reflect.ClassUtilTest.AClass</a></li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.reflect.ClassUtilTest.BClass</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>所有已实现的接口:</dt>
<dd><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.AInterface.html" title="com.huazheng.tunny.tools.reflect中的接口">ClassUtilTest.AInterface</a>, <a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.BInterface.html" title="com.huazheng.tunny.tools.reflect中的接口">ClassUtilTest.BInterface</a>, <a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.CInterface.html" title="com.huazheng.tunny.tools.reflect中的接口">ClassUtilTest.CInterface</a>, <a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.DInterface.html" title="com.huazheng.tunny.tools.reflect中的接口">ClassUtilTest.DInterface</a></dd>
</dl>
<dl>
<dt>封闭类:</dt>
<dd><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtilTest</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">ClassUtilTest.BClass</span>
extends <a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.AClass.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtilTest.AClass</a>
implements <a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.CInterface.html" title="com.huazheng.tunny.tools.reflect中的接口">ClassUtilTest.CInterface</a>, <a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.BInterface.html" title="com.huazheng.tunny.tools.reflect中的接口">ClassUtilTest.BInterface</a></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>字段概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="字段概要表, 列表字段和解释">
<caption><span>字段</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">字段和说明</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.BClass.html#sfield">sfield</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.BClass.html#ufield">ufield</a></span></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.huazheng.tunny.tools.reflect.ClassUtilTest.AClass">
<!--   -->
</a>
<h3>从类继承的字段&nbsp;com.huazheng.tunny.tools.reflect.<a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.AClass.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtilTest.AClass</a></h3>
<code><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.AClass.html#tfield">tfield</a>, <a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.AClass.html#vfield">vfield</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.BClass.html#BClass--">BClass</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.BClass.html#hello--">hello</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.BClass.html#hello2-int-">hello2</a></span>(int&nbsp;i)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.BClass.html#hello3-int-">hello3</a></span>(int&nbsp;i)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.BClass.html#hello7-int-">hello7</a></span>(int&nbsp;i)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.huazheng.tunny.tools.reflect.ClassUtilTest.AClass">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;com.huazheng.tunny.tools.reflect.<a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.AClass.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtilTest.AClass</a></h3>
<code><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.AClass.html#hello4-int-">hello4</a>, <a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.AClass.html#hello5-int-">hello5</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>字段详细资料</h3>
<a name="sfield">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sfield</h4>
<pre>public&nbsp;int sfield</pre>
</li>
</ul>
<a name="ufield">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ufield</h4>
<pre>protected&nbsp;int ufield</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="BClass--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>BClass</h4>
<pre>public&nbsp;BClass()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="hello--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hello</h4>
<pre>public&nbsp;void&nbsp;hello()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">指定者:</span></dt>
<dd><code><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.BInterface.html#hello--">hello</a></code>&nbsp;在接口中&nbsp;<code><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.BInterface.html" title="com.huazheng.tunny.tools.reflect中的接口">ClassUtilTest.BInterface</a></code></dd>
</dl>
</li>
</ul>
<a name="hello2-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hello2</h4>
<pre>public&nbsp;void&nbsp;hello2(int&nbsp;i)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.AClass.html#hello2-int-">hello2</a></code>&nbsp;在类中&nbsp;<code><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.AClass.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtilTest.AClass</a></code></dd>
</dl>
</li>
</ul>
<a name="hello3-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hello3</h4>
<pre>public&nbsp;void&nbsp;hello3(int&nbsp;i)</pre>
</li>
</ul>
<a name="hello7-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>hello7</h4>
<pre>public&nbsp;void&nbsp;hello7(int&nbsp;i)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">覆盖:</span></dt>
<dd><code><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.AClass.html#hello7-int-">hello7</a></code>&nbsp;在类中&nbsp;<code><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.AClass.html" title="com.huazheng.tunny.tools.reflect中的类">ClassUtilTest.AClass</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.BAnnotation.html" title="com.huazheng.tunny.tools.reflect中的注释"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassUtilTest.BInterface.html" title="com.huazheng.tunny.tools.reflect中的接口"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/reflect/ClassUtilTest.BClass.html" target="_top">框架</a></li>
<li><a href="ClassUtilTest.BClass.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field.summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li><a href="#field.detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
