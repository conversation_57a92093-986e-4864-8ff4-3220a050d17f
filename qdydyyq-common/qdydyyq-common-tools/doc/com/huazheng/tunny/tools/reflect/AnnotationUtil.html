<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:33 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>AnnotationUtil</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AnnotationUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassLoaderUtil.html" title="com.huazheng.tunny.tools.reflect中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/reflect/AnnotationUtil.html" target="_top">框架</a></li>
<li><a href="AnnotationUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.reflect</div>
<h2 title="类 AnnotationUtil" class="title">类 AnnotationUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.reflect.AnnotationUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">AnnotationUtil</span>
extends java.lang.Object</pre>
<div class="block">Annotation的工具类
 
 1.获得类的全部Annotation
 
 2.获取类的标注了annotation的所有属性和方法</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/AnnotationUtil.html#AnnotationUtil--">AnnotationUtil</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static java.util.Set&lt;java.lang.annotation.Annotation&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/AnnotationUtil.html#getAllAnnotations-java.lang.Class-">getAllAnnotations</a></span>(java.lang.Class&lt;?&gt;&nbsp;cls)</code>
<div class="block">递归Class所有的Annotation，一个最彻底的实现.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static &lt;T extends java.lang.annotation.Annotation&gt;<br>java.util.Set&lt;java.lang.reflect.Field&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/AnnotationUtil.html#getAnnotatedFields-java.lang.Class-java.lang.Class-">getAnnotatedFields</a></span>(java.lang.Class&lt;? extends java.lang.Object&gt;&nbsp;clazz,
                  java.lang.Class&lt;T&gt;&nbsp;annotation)</code>
<div class="block">找出所有标注了该annotation的属性，循环遍历父类，包含private属性.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static &lt;T extends java.lang.annotation.Annotation&gt;<br>java.util.Set&lt;java.lang.reflect.Field&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/AnnotationUtil.html#getAnnotatedPublicFields-java.lang.Class-java.lang.Class-">getAnnotatedPublicFields</a></span>(java.lang.Class&lt;? extends java.lang.Object&gt;&nbsp;clazz,
                        java.lang.Class&lt;T&gt;&nbsp;annotation)</code>
<div class="block">找出所有标注了该annotation的公共属性，循环遍历父类.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static &lt;T extends java.lang.annotation.Annotation&gt;<br>java.util.Set&lt;java.lang.reflect.Method&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/reflect/AnnotationUtil.html#getAnnotatedPublicMethods-java.lang.Class-java.lang.Class-">getAnnotatedPublicMethods</a></span>(java.lang.Class&lt;?&gt;&nbsp;clazz,
                         java.lang.Class&lt;T&gt;&nbsp;annotation)</code>
<div class="block">找出所有标注了该annotation的公共方法(含父类的公共函数)，循环其接口.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="AnnotationUtil--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>AnnotationUtil</h4>
<pre>public&nbsp;AnnotationUtil()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="getAllAnnotations-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAllAnnotations</h4>
<pre>public static&nbsp;java.util.Set&lt;java.lang.annotation.Annotation&gt;&nbsp;getAllAnnotations(java.lang.Class&lt;?&gt;&nbsp;cls)</pre>
<div class="block">递归Class所有的Annotation，一个最彻底的实现.
 
 包括所有基类，所有接口的Annotation，同时支持Spring风格的Annotation继承的父Annotation，</div>
</li>
</ul>
<a name="getAnnotatedPublicFields-java.lang.Class-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAnnotatedPublicFields</h4>
<pre>public static&nbsp;&lt;T extends java.lang.annotation.Annotation&gt;&nbsp;java.util.Set&lt;java.lang.reflect.Field&gt;&nbsp;getAnnotatedPublicFields(java.lang.Class&lt;? extends java.lang.Object&gt;&nbsp;clazz,
                                                                                                                          java.lang.Class&lt;T&gt;&nbsp;annotation)</pre>
<div class="block">找出所有标注了该annotation的公共属性，循环遍历父类.
 
 暂未支持Spring风格Annotation继承Annotation
 
 copy from org.unitils.util.AnnotationUtils</div>
</li>
</ul>
<a name="getAnnotatedFields-java.lang.Class-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAnnotatedFields</h4>
<pre>public static&nbsp;&lt;T extends java.lang.annotation.Annotation&gt;&nbsp;java.util.Set&lt;java.lang.reflect.Field&gt;&nbsp;getAnnotatedFields(java.lang.Class&lt;? extends java.lang.Object&gt;&nbsp;clazz,
                                                                                                                    java.lang.Class&lt;T&gt;&nbsp;annotation)</pre>
<div class="block">找出所有标注了该annotation的属性，循环遍历父类，包含private属性.
 
 暂未支持Spring风格Annotation继承Annotation
 
 copy from org.unitils.util.AnnotationUtils</div>
</li>
</ul>
<a name="getAnnotatedPublicMethods-java.lang.Class-java.lang.Class-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getAnnotatedPublicMethods</h4>
<pre>public static&nbsp;&lt;T extends java.lang.annotation.Annotation&gt;&nbsp;java.util.Set&lt;java.lang.reflect.Method&gt;&nbsp;getAnnotatedPublicMethods(java.lang.Class&lt;?&gt;&nbsp;clazz,
                                                                                                                            java.lang.Class&lt;T&gt;&nbsp;annotation)</pre>
<div class="block">找出所有标注了该annotation的公共方法(含父类的公共函数)，循环其接口.
 
 暂未支持Spring风格Annotation继承Annotation
 
 另，如果子类重载父类的公共函数，父类函数上的annotation不会继承，只有接口上的annotation会被继承.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>上一个类</li>
<li><a href="../../../../../com/huazheng/tunny/tools/reflect/ClassLoaderUtil.html" title="com.huazheng.tunny.tools.reflect中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/reflect/AnnotationUtil.html" target="_top">框架</a></li>
<li><a href="AnnotationUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
