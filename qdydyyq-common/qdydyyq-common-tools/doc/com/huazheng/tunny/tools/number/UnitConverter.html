<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:32 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>UnitConverter</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="UnitConverter";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/number/SizeUnit.html" title="com.huazheng.tunny.tools.number中的枚举"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/number/UnitConverterTest.html" title="com.huazheng.tunny.tools.number中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/number/UnitConverter.html" target="_top">框架</a></li>
<li><a href="UnitConverter.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.number</div>
<h2 title="类 UnitConverter" class="title">类 UnitConverter</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.number.UnitConverter</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">UnitConverter</span>
extends java.lang.Object</pre>
<div class="block">1.将带单位的时间，大小字符串转换为数字. copy from Facebook
 https://github.com/facebook/jcommon/blob/master/config/src/main/java/com/facebook/config/ConfigUtil.java

 2.将数字转为带单位的字符串</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/UnitConverter.html#UnitConverter--">UnitConverter</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/UnitConverter.html#toBytes-java.lang.String-">toBytes</a></span>(java.lang.String&nbsp;size)</code>
<div class="block">将带单位的大小字符串转化为字节数.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/UnitConverter.html#toDurationMillis-java.lang.String-">toDurationMillis</a></span>(java.lang.String&nbsp;duration)</code>
<div class="block">将带单位的时间字符串转化为毫秒数.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/UnitConverter.html#toSizeUnit-java.lang.Long-int-">toSizeUnit</a></span>(java.lang.Long&nbsp;bytes,
          int&nbsp;scale)</code>
<div class="block">从bytes转换为带单位的字符串, 单位最大只支持到G级别，四舍五入</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/UnitConverter.html#toTimeUnit-long-int-">toTimeUnit</a></span>(long&nbsp;millis,
          int&nbsp;scale)</code>
<div class="block">转换毫秒为带时间单位的字符串，单位最大到day级别，四舍五入</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/UnitConverter.html#toTimeWithMinorUnit-long-">toTimeWithMinorUnit</a></span>(long&nbsp;millis)</code>
<div class="block">转换毫秒为带时间单位的字符串，会同时带下一级的单位，四舍五入</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="UnitConverter--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>UnitConverter</h4>
<pre>public&nbsp;UnitConverter()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="toDurationMillis-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toDurationMillis</h4>
<pre>public static&nbsp;long&nbsp;toDurationMillis(java.lang.String&nbsp;duration)</pre>
<div class="block">将带单位的时间字符串转化为毫秒数.
 
 单位包括不分大小写的ms(毫秒),s(秒),m(分钟),h(小时),d(日),y(年)
 
 不带任何单位的话，默认单位是毫秒</div>
</li>
</ul>
<a name="toBytes-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toBytes</h4>
<pre>public static&nbsp;long&nbsp;toBytes(java.lang.String&nbsp;size)</pre>
<div class="block">将带单位的大小字符串转化为字节数.
 
 单位包括不分大小写的b(b),k(kb),m(mb),g(gb),t(tb)
 
 不带任何单位的话，默认单位是b</div>
</li>
</ul>
<a name="toSizeUnit-java.lang.Long-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toSizeUnit</h4>
<pre>public static&nbsp;java.lang.String&nbsp;toSizeUnit(java.lang.Long&nbsp;bytes,
                                          int&nbsp;scale)</pre>
<div class="block">从bytes转换为带单位的字符串, 单位最大只支持到G级别，四舍五入</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>scale</code> - 小数后的精度</dd>
</dl>
</li>
</ul>
<a name="toTimeUnit-long-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toTimeUnit</h4>
<pre>public static&nbsp;java.lang.String&nbsp;toTimeUnit(long&nbsp;millis,
                                          int&nbsp;scale)</pre>
<div class="block">转换毫秒为带时间单位的字符串，单位最大到day级别，四舍五入</div>
<dl>
<dt><span class="paramLabel">参数:</span></dt>
<dd><code>scale</code> - 小数后的精度</dd>
</dl>
</li>
</ul>
<a name="toTimeWithMinorUnit-long-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>toTimeWithMinorUnit</h4>
<pre>public static&nbsp;java.lang.String&nbsp;toTimeWithMinorUnit(long&nbsp;millis)</pre>
<div class="block">转换毫秒为带时间单位的字符串，会同时带下一级的单位，四舍五入</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/number/SizeUnit.html" title="com.huazheng.tunny.tools.number中的枚举"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/number/UnitConverterTest.html" title="com.huazheng.tunny.tools.number中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/number/UnitConverter.html" target="_top">框架</a></li>
<li><a href="UnitConverter.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
