<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="zh">
<head>
<!-- Generated by javadoc (1.8.0_31) on Tue Sep 18 18:06:32 CST 2018 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>RandomUtil</title>
<meta name="date" content="2018-09-18">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RandomUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9,"i14":9,"i15":9,"i16":9,"i17":9,"i18":9,"i19":9,"i20":9,"i21":9,"i22":9,"i23":9,"i24":9,"i25":9,"i26":9,"i27":9,"i28":9,"i29":9,"i30":9,"i31":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtilTest.html" title="com.huazheng.tunny.tools.number中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtilTest.html" title="com.huazheng.tunny.tools.number中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/number/RandomUtil.html" target="_top">框架</a></li>
<li><a href="RandomUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.huazheng.tunny.tools.number</div>
<h2 title="类 RandomUtil" class="title">类 RandomUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.huazheng.tunny.tools.number.RandomUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">RandomUtil</span>
extends java.lang.Object</pre>
<div class="block">随机数工具集.
 
 1. 获取无锁的ThreadLocalRandom, 性能较佳的SecureRandom
 
 2. 保证没有负数陷阱，也能更精确设定范围的nextInt/nextLong/nextDouble
  (copy from Common Lang RandomUtils，但默认使用性能较优的ThreadLocalRandom，并可配置其他的Random)
 
 3. 随机字符串 (via Common Lang RandomStringUtils)</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>构造器概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="构造器概要表, 列表构造器和解释">
<caption><span>构造器</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">构造器和说明</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#RandomUtil--">RandomUtil</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>方法概要</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="方法概要表, 列表方法和解释">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">限定符和类型</th>
<th class="colLast" scope="col">方法和说明</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#nextDouble--">nextDouble</a></span>()</code>
<div class="block">返回0-之间的double, 使用ThreadLocalRandom</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#nextDouble-double-">nextDouble</a></span>(double&nbsp;max)</code>
<div class="block">返回0-max之间的double, 使用ThreadLocalRandom
 
 注意：与JDK默认返回0-1的行为不一致.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#nextDouble-double-double-">nextDouble</a></span>(double&nbsp;min,
          double&nbsp;max)</code>
<div class="block">返回min-max之间的double,ThreadLocalRandom</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#nextDouble-java.util.Random-">nextDouble</a></span>(java.util.Random&nbsp;random)</code>
<div class="block">返回0-Double.MAX之间的double</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#nextDouble-java.util.Random-double-">nextDouble</a></span>(java.util.Random&nbsp;random,
          double&nbsp;max)</code>
<div class="block">返回0-max之间的double</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#nextDouble-java.util.Random-double-double-">nextDouble</a></span>(java.util.Random&nbsp;random,
          double&nbsp;min,
          double&nbsp;max)</code>
<div class="block">返回min-max之间的double</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#nextInt--">nextInt</a></span>()</code>
<div class="block">返回0到Intger.MAX_VALUE的随机Int, 使用ThreadLocalRandom.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#nextInt-int-">nextInt</a></span>(int&nbsp;max)</code>
<div class="block">返回0到max的随机Int, 使用ThreadLocalRandom.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#nextInt-int-int-">nextInt</a></span>(int&nbsp;min,
       int&nbsp;max)</code>
<div class="block">返回min到max的随机Int, 使用ThreadLocalRandom.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#nextInt-java.util.Random-">nextInt</a></span>(java.util.Random&nbsp;random)</code>
<div class="block">返回0到Intger.MAX_VALUE的随机Int, 可传入ThreadLocalRandom或SecureRandom</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#nextInt-java.util.Random-int-">nextInt</a></span>(java.util.Random&nbsp;random,
       int&nbsp;max)</code>
<div class="block">返回0到max的随机Int, 可传入SecureRandom或ThreadLocalRandom</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#nextInt-java.util.Random-int-int-">nextInt</a></span>(java.util.Random&nbsp;random,
       int&nbsp;min,
       int&nbsp;max)</code>
<div class="block">返回min到max的随机Int,可传入SecureRandom或ThreadLocalRandom.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#nextLong--">nextLong</a></span>()</code>
<div class="block">返回0－Long.MAX_VALUE间的随机Long, 使用ThreadLocalRandom.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#nextLong-long-">nextLong</a></span>(long&nbsp;max)</code>
<div class="block">返回0－max间的随机Long, 使用ThreadLocalRandom.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#nextLong-long-long-">nextLong</a></span>(long&nbsp;min,
        long&nbsp;max)</code>
<div class="block">返回min－max间的随机Long, 使用ThreadLocalRandom.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#nextLong-java.util.Random-">nextLong</a></span>(java.util.Random&nbsp;random)</code>
<div class="block">返回0－Long.MAX_VALUE间的随机Long, 可传入SecureRandom或ThreadLocalRandom</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#nextLong-java.util.Random-long-">nextLong</a></span>(java.util.Random&nbsp;random,
        long&nbsp;max)</code>
<div class="block">返回0-max间的随机Long, 可传入SecureRandom或ThreadLocalRandom</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#nextLong-java.util.Random-long-long-">nextLong</a></span>(java.util.Random&nbsp;random,
        long&nbsp;min,
        long&nbsp;max)</code>
<div class="block">返回min-max间的随机Long,可传入SecureRandom或ThreadLocalRandom.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#randomAsciiFixLength-int-">randomAsciiFixLength</a></span>(int&nbsp;length)</code>
<div class="block">随机ASCII字符(含字母，数字及其他符号)，固定长度</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#randomAsciiFixLength-java.util.Random-int-">randomAsciiFixLength</a></span>(java.util.Random&nbsp;random,
                    int&nbsp;length)</code>
<div class="block">随机ASCII字符(含字母，数字及其他符号)，固定长度</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#randomAsciiRandomLength-int-int-">randomAsciiRandomLength</a></span>(int&nbsp;minLength,
                       int&nbsp;maxLength)</code>
<div class="block">随机ASCII字符(含字母，数字及其他符号)，随机长度</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#randomAsciiRandomLength-java.util.Random-int-int-">randomAsciiRandomLength</a></span>(java.util.Random&nbsp;random,
                       int&nbsp;minLength,
                       int&nbsp;maxLength)</code>
<div class="block">随机ASCII字符(含字母，数字及其他符号)，随机长度</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#randomLetterFixLength-int-">randomLetterFixLength</a></span>(int&nbsp;length)</code>
<div class="block">随机字母，固定长度</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#randomLetterFixLength-java.util.Random-int-">randomLetterFixLength</a></span>(java.util.Random&nbsp;random,
                     int&nbsp;length)</code>
<div class="block">随机字母，固定长度</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#randomLetterRandomLength-int-int-">randomLetterRandomLength</a></span>(int&nbsp;minLength,
                        int&nbsp;maxLength)</code>
<div class="block">随机字母，随机长度</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#randomLetterRandomLength-java.util.Random-int-int-">randomLetterRandomLength</a></span>(java.util.Random&nbsp;random,
                        int&nbsp;minLength,
                        int&nbsp;maxLength)</code>
<div class="block">随机字母，随机长度</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#randomStringFixLength-int-">randomStringFixLength</a></span>(int&nbsp;length)</code>
<div class="block">随机字母或数字，固定长度</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#randomStringFixLength-java.util.Random-int-">randomStringFixLength</a></span>(java.util.Random&nbsp;random,
                     int&nbsp;length)</code>
<div class="block">随机字母或数字，固定长度</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#randomStringRandomLength-int-int-">randomStringRandomLength</a></span>(int&nbsp;minLength,
                        int&nbsp;maxLength)</code>
<div class="block">随机字母或数字，随机长度</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#randomStringRandomLength-java.util.Random-int-int-">randomStringRandomLength</a></span>(java.util.Random&nbsp;random,
                        int&nbsp;minLength,
                        int&nbsp;maxLength)</code>
<div class="block">随机字母或数字，随机长度</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>static java.security.SecureRandom</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#secureRandom--">secureRandom</a></span>()</code>
<div class="block">使用性能更好的SHA1PRNG, Tomcat的sessionId生成也用此算法.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>static java.util.Random</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtil.html#threadLocalRandom--">threadLocalRandom</a></span>()</code>
<div class="block">返回无锁的ThreadLocalRandom</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>从类继承的方法&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>构造器详细资料</h3>
<a name="RandomUtil--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>RandomUtil</h4>
<pre>public&nbsp;RandomUtil()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>方法详细资料</h3>
<a name="threadLocalRandom--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>threadLocalRandom</h4>
<pre>public static&nbsp;java.util.Random&nbsp;threadLocalRandom()</pre>
<div class="block">返回无锁的ThreadLocalRandom</div>
</li>
</ul>
<a name="secureRandom--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>secureRandom</h4>
<pre>public static&nbsp;java.security.SecureRandom&nbsp;secureRandom()</pre>
<div class="block">使用性能更好的SHA1PRNG, Tomcat的sessionId生成也用此算法.
 
 但JDK7中，需要在启动参数加入 -Djava.security=file:/dev/./urandom （中间那个点很重要）
 
 详见：《SecureRandom的江湖偏方与真实效果》http://calvin1978.blogcn.com/articles/securerandom.html</div>
</li>
</ul>
<a name="nextInt--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nextInt</h4>
<pre>public static&nbsp;int&nbsp;nextInt()</pre>
<div class="block">返回0到Intger.MAX_VALUE的随机Int, 使用ThreadLocalRandom.</div>
</li>
</ul>
<a name="nextInt-java.util.Random-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nextInt</h4>
<pre>public static&nbsp;int&nbsp;nextInt(java.util.Random&nbsp;random)</pre>
<div class="block">返回0到Intger.MAX_VALUE的随机Int, 可传入ThreadLocalRandom或SecureRandom</div>
</li>
</ul>
<a name="nextInt-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nextInt</h4>
<pre>public static&nbsp;int&nbsp;nextInt(int&nbsp;max)</pre>
<div class="block">返回0到max的随机Int, 使用ThreadLocalRandom.</div>
</li>
</ul>
<a name="nextInt-java.util.Random-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nextInt</h4>
<pre>public static&nbsp;int&nbsp;nextInt(java.util.Random&nbsp;random,
                          int&nbsp;max)</pre>
<div class="block">返回0到max的随机Int, 可传入SecureRandom或ThreadLocalRandom</div>
</li>
</ul>
<a name="nextInt-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nextInt</h4>
<pre>public static&nbsp;int&nbsp;nextInt(int&nbsp;min,
                          int&nbsp;max)</pre>
<div class="block">返回min到max的随机Int, 使用ThreadLocalRandom.
 
 min必须大于0.</div>
</li>
</ul>
<a name="nextInt-java.util.Random-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nextInt</h4>
<pre>public static&nbsp;int&nbsp;nextInt(java.util.Random&nbsp;random,
                          int&nbsp;min,
                          int&nbsp;max)</pre>
<div class="block">返回min到max的随机Int,可传入SecureRandom或ThreadLocalRandom.
 
 min必须大于0.
 
 JDK本身不具有控制两端范围的nextInt，因此参考Commons Lang RandomUtils的实现, 不直接复用是因为要传入Random实例</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>org.apache.commons.lang3.RandomUtils#nextInt(long, long)</code></dd>
</dl>
</li>
</ul>
<a name="nextLong--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nextLong</h4>
<pre>public static&nbsp;long&nbsp;nextLong()</pre>
<div class="block">返回0－Long.MAX_VALUE间的随机Long, 使用ThreadLocalRandom.</div>
</li>
</ul>
<a name="nextLong-java.util.Random-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nextLong</h4>
<pre>public static&nbsp;long&nbsp;nextLong(java.util.Random&nbsp;random)</pre>
<div class="block">返回0－Long.MAX_VALUE间的随机Long, 可传入SecureRandom或ThreadLocalRandom</div>
</li>
</ul>
<a name="nextLong-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nextLong</h4>
<pre>public static&nbsp;long&nbsp;nextLong(long&nbsp;max)</pre>
<div class="block">返回0－max间的随机Long, 使用ThreadLocalRandom.</div>
</li>
</ul>
<a name="nextLong-java.util.Random-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nextLong</h4>
<pre>public static&nbsp;long&nbsp;nextLong(java.util.Random&nbsp;random,
                            long&nbsp;max)</pre>
<div class="block">返回0-max间的随机Long, 可传入SecureRandom或ThreadLocalRandom</div>
</li>
</ul>
<a name="nextLong-long-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nextLong</h4>
<pre>public static&nbsp;long&nbsp;nextLong(long&nbsp;min,
                            long&nbsp;max)</pre>
<div class="block">返回min－max间的随机Long, 使用ThreadLocalRandom.
 
 min必须大于0.</div>
</li>
</ul>
<a name="nextLong-java.util.Random-long-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nextLong</h4>
<pre>public static&nbsp;long&nbsp;nextLong(java.util.Random&nbsp;random,
                            long&nbsp;min,
                            long&nbsp;max)</pre>
<div class="block">返回min-max间的随机Long,可传入SecureRandom或ThreadLocalRandom.
 
 min必须大于0.
 
 JDK本身不具有控制两端范围的nextLong，因此参考Commons Lang RandomUtils的实现, 不直接复用是因为要传入Random实例</div>
<dl>
<dt><span class="seeLabel">另请参阅:</span></dt>
<dd><code>RandomUtils.nextLong(long, long)</code></dd>
</dl>
</li>
</ul>
<a name="nextDouble--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nextDouble</h4>
<pre>public static&nbsp;double&nbsp;nextDouble()</pre>
<div class="block">返回0-之间的double, 使用ThreadLocalRandom</div>
</li>
</ul>
<a name="nextDouble-java.util.Random-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nextDouble</h4>
<pre>public static&nbsp;double&nbsp;nextDouble(java.util.Random&nbsp;random)</pre>
<div class="block">返回0-Double.MAX之间的double</div>
</li>
</ul>
<a name="nextDouble-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nextDouble</h4>
<pre>public static&nbsp;double&nbsp;nextDouble(double&nbsp;max)</pre>
<div class="block">返回0-max之间的double, 使用ThreadLocalRandom
 
 注意：与JDK默认返回0-1的行为不一致.</div>
</li>
</ul>
<a name="nextDouble-java.util.Random-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nextDouble</h4>
<pre>public static&nbsp;double&nbsp;nextDouble(java.util.Random&nbsp;random,
                                double&nbsp;max)</pre>
<div class="block">返回0-max之间的double</div>
</li>
</ul>
<a name="nextDouble-double-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nextDouble</h4>
<pre>public static&nbsp;double&nbsp;nextDouble(double&nbsp;min,
                                double&nbsp;max)</pre>
<div class="block">返回min-max之间的double,ThreadLocalRandom</div>
</li>
</ul>
<a name="nextDouble-java.util.Random-double-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>nextDouble</h4>
<pre>public static&nbsp;double&nbsp;nextDouble(java.util.Random&nbsp;random,
                                double&nbsp;min,
                                double&nbsp;max)</pre>
<div class="block">返回min-max之间的double</div>
</li>
</ul>
<a name="randomStringFixLength-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>randomStringFixLength</h4>
<pre>public static&nbsp;java.lang.String&nbsp;randomStringFixLength(int&nbsp;length)</pre>
<div class="block">随机字母或数字，固定长度</div>
</li>
</ul>
<a name="randomStringFixLength-java.util.Random-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>randomStringFixLength</h4>
<pre>public static&nbsp;java.lang.String&nbsp;randomStringFixLength(java.util.Random&nbsp;random,
                                                     int&nbsp;length)</pre>
<div class="block">随机字母或数字，固定长度</div>
</li>
</ul>
<a name="randomStringRandomLength-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>randomStringRandomLength</h4>
<pre>public static&nbsp;java.lang.String&nbsp;randomStringRandomLength(int&nbsp;minLength,
                                                        int&nbsp;maxLength)</pre>
<div class="block">随机字母或数字，随机长度</div>
</li>
</ul>
<a name="randomStringRandomLength-java.util.Random-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>randomStringRandomLength</h4>
<pre>public static&nbsp;java.lang.String&nbsp;randomStringRandomLength(java.util.Random&nbsp;random,
                                                        int&nbsp;minLength,
                                                        int&nbsp;maxLength)</pre>
<div class="block">随机字母或数字，随机长度</div>
</li>
</ul>
<a name="randomLetterFixLength-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>randomLetterFixLength</h4>
<pre>public static&nbsp;java.lang.String&nbsp;randomLetterFixLength(int&nbsp;length)</pre>
<div class="block">随机字母，固定长度</div>
</li>
</ul>
<a name="randomLetterFixLength-java.util.Random-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>randomLetterFixLength</h4>
<pre>public static&nbsp;java.lang.String&nbsp;randomLetterFixLength(java.util.Random&nbsp;random,
                                                     int&nbsp;length)</pre>
<div class="block">随机字母，固定长度</div>
</li>
</ul>
<a name="randomLetterRandomLength-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>randomLetterRandomLength</h4>
<pre>public static&nbsp;java.lang.String&nbsp;randomLetterRandomLength(int&nbsp;minLength,
                                                        int&nbsp;maxLength)</pre>
<div class="block">随机字母，随机长度</div>
</li>
</ul>
<a name="randomLetterRandomLength-java.util.Random-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>randomLetterRandomLength</h4>
<pre>public static&nbsp;java.lang.String&nbsp;randomLetterRandomLength(java.util.Random&nbsp;random,
                                                        int&nbsp;minLength,
                                                        int&nbsp;maxLength)</pre>
<div class="block">随机字母，随机长度</div>
</li>
</ul>
<a name="randomAsciiFixLength-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>randomAsciiFixLength</h4>
<pre>public static&nbsp;java.lang.String&nbsp;randomAsciiFixLength(int&nbsp;length)</pre>
<div class="block">随机ASCII字符(含字母，数字及其他符号)，固定长度</div>
</li>
</ul>
<a name="randomAsciiFixLength-java.util.Random-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>randomAsciiFixLength</h4>
<pre>public static&nbsp;java.lang.String&nbsp;randomAsciiFixLength(java.util.Random&nbsp;random,
                                                    int&nbsp;length)</pre>
<div class="block">随机ASCII字符(含字母，数字及其他符号)，固定长度</div>
</li>
</ul>
<a name="randomAsciiRandomLength-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>randomAsciiRandomLength</h4>
<pre>public static&nbsp;java.lang.String&nbsp;randomAsciiRandomLength(int&nbsp;minLength,
                                                       int&nbsp;maxLength)</pre>
<div class="block">随机ASCII字符(含字母，数字及其他符号)，随机长度</div>
</li>
</ul>
<a name="randomAsciiRandomLength-java.util.Random-int-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>randomAsciiRandomLength</h4>
<pre>public static&nbsp;java.lang.String&nbsp;randomAsciiRandomLength(java.util.Random&nbsp;random,
                                                       int&nbsp;minLength,
                                                       int&nbsp;maxLength)</pre>
<div class="block">随机ASCII字符(含字母，数字及其他符号)，随机长度</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="跳过导航链接">跳过导航链接</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="导航">
<li><a href="../../../../../overview-summary.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="navBarCell1Rev">类</li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../deprecated-list.html">已过时</a></li>
<li><a href="../../../../../index-files/index-1.html">索引</a></li>
<li><a href="../../../../../help-doc.html">帮助</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/huazheng/tunny/tools/number/NumberUtilTest.html" title="com.huazheng.tunny.tools.number中的类"><span class="typeNameLink">上一个类</span></a></li>
<li><a href="../../../../../com/huazheng/tunny/tools/number/RandomUtilTest.html" title="com.huazheng.tunny.tools.number中的类"><span class="typeNameLink">下一个类</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/huazheng/tunny/tools/number/RandomUtil.html" target="_top">框架</a></li>
<li><a href="RandomUtil.html" target="_top">无框架</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">所有类</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">方法</a></li>
</ul>
<ul class="subNavList">
<li>详细资料:&nbsp;</li>
<li>字段&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">方法</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
