<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.huazheng</groupId>
		<artifactId>qdydyyq-common</artifactId>
		<version>1.3.2</version>
	</parent>


	<groupId>com.huazheng</groupId>
	<artifactId>qdydyyq-common-tools</artifactId>
	<version>1.3.2</version>
	<packaging>jar</packaging>
	<name>qdydyyq-common-tools</name>
	<description>java开发工具包</description>

	<properties>
		<guava.version>20.0</guava.version>
		<commons-lang3.version>3.7</commons-lang3.version>
		<slf4j.version>1.7.25</slf4j.version>
		<logback.version>1.1.8</logback.version>
		<dozer.version>5.5.1</dozer.version>
		<jackson.version>2.8.6</jackson.version>
		<junit.version>4.12</junit.version>
		<assertj.version>2.6.0</assertj.version>
		<mockito.version>2.18.3</mockito.version>

		<!-- Plugin的属性 -->
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<java.version>1.8</java.version>
		<maven.compiler.source>${java.version}</maven.compiler.source>
		<maven.compiler.target>${java.version}</maven.compiler.target>

		<!--自定义默认的编码格式-->
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<!--自定义默认的时间格式 年-月-日-时-分-->
		<maven.build.timestamp.format>
			yyyy-MM-dd_HH_mm
		</maven.build.timestamp.format>
	</properties>

	<!-- 尽量少的引入依赖包，方便单独重用本模块 -->
	<dependencies>

		<!-- Util -->
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>${guava.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>${commons-lang3.version}</version>
		</dependency>

		<!-- Log -->
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>${slf4j.version}</version>
		</dependency>

		<!-- optional for BeanMapper -->
		<dependency>
			<groupId>net.sf.dozer</groupId>
			<artifactId>dozer</artifactId>
			<version>${dozer.version}</version>
			<optional>true</optional>
		</dependency>

		<!-- optional for JsonMapper -->
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>${jackson.version}</version>
			<optional>true</optional>
		</dependency>

		<!-- Test -->
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>${junit.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.assertj</groupId>
			<artifactId>assertj-core</artifactId>
			<version>${assertj.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
			<version>${mockito.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-classic</artifactId>
			<version>${logback.version}</version>
			<scope>test</scope>
		</dependency>

		<!-- only for XMLMapper test -->
		<dependency>
			<groupId>dom4j</groupId>
			<artifactId>dom4j</artifactId>
			<version>1.6.1</version>
			<scope>test</scope>
		</dependency>

		<!-- only for XMLMapper test -->
		<dependency>
			<groupId>jaxen</groupId>
			<artifactId>jaxen</artifactId>
			<version>1.1.6</version>
			<scope>test</scope>
		</dependency>

		<!--FastDFS鏡像包，将源码下载，手工clean install安装后引用-->
		<dependency>
			<groupId>org.csource</groupId>
			<artifactId>fastdfs-client-java</artifactId>
			<version>1.27</version>
		</dependency>
		<!--阿里云OSS文件上传-->
		<dependency>
			<groupId>com.aliyun.oss</groupId>
			<artifactId>aliyun-sdk-oss</artifactId>
			<version>2.3.0</version>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-email</artifactId>
			<version>1.4</version>
		</dependency>

		<!-- 阿里短信服务 -->
<!--		<dependency>-->
<!--			<groupId>com.aliyun</groupId>-->
<!--			<artifactId>aliyun-java-sdk-core</artifactId>-->
<!--			<version>4.3.2</version>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>com.aliyun</groupId>-->
<!--			<artifactId>aliyun-java-sdk-ecs</artifactId>-->
<!--			<version>4.11.0</version>-->
<!--		</dependency>-->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>1.10.0</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>

	<profiles>
		<profile>
			<id>release</id>
			<build>
				<plugins>
					<!-- javadoc attach plugin -->
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-javadoc-plugin</artifactId>
						<configuration>
							<failOnError>false</failOnError>
						</configuration>
						<executions>
							<execution>
								<id>attach-javadocs</id>
								<goals>
									<goal>jar</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
					<!-- source attach plugin -->
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-source-plugin</artifactId>
						<version>3.0.1</version>
						<executions>
							<execution>
								<id>attach-sources</id>
								<goals>
									<goal>jar</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-release-plugin</artifactId>
						<version>2.5.3</version>
						<configuration>
							<tagNameFormat>v.@{project.version}</tagNameFormat>
						</configuration>
					</plugin>

					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-gpg-plugin</artifactId>
						<version>1.6</version>
						<executions>
							<execution>
								<id>sign-artifacts</id>
								<phase>verify</phase>
								<goals>
									<goal>sign</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>

		<profile>
			<id>sonar</id>
			<properties>
				<sonar.java.source>1.7</sonar.java.source>
				<sonar.exclusions>**/primitive/**/*.java,**/jsr166e/**/*.java,**/SortedArrayList.java,**/WildcardMatcher.java,**/CsvUtil.java</sonar.exclusions>
			</properties>
			<build>
				<plugins>
					<plugin>
						<groupId>org.jacoco</groupId>
						<artifactId>jacoco-maven-plugin</artifactId>
						<version>0.7.2.201409121644</version>
						<configuration>
							<append>true</append>
						</configuration>
						<executions>
							<execution>
								<id>agent-for-ut</id>
								<goals>
									<goal>prepare-agent</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
					<plugin>
						<groupId>org.sonarsource.scanner.maven</groupId>
						<artifactId>sonar-maven-plugin</artifactId>
						<version>3.4.0.905</version>
					</plugin>
				</plugins>
			</build>
		</profile>

		<profile>
			<id>jdk9</id>
			<activation>
				<jdk>9</jdk>
			</activation>
			<build>
				<plugins>
					<!-- only for XMLMapper test -->
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-surefire-plugin</artifactId>
						<version>2.22.0</version>
						<configuration>
							<argLine>--add-modules java.xml.bind</argLine>
						</configuration>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>

<!--
	<distributionManagement>
		<snapshotRepository>
			<id>sonatype-nexus-snapshots</id>
			<url>https://oss.sonatype.org/content/repositories/snapshots</url>
		</snapshotRepository>
		<repository>
			<id>sonatype-nexus-releases</id>
			<url>https://oss.sonatype.org/service/local/staging/deploy/maven2</url>
		</repository>
	</distributionManagement>
-->


	<licenses>
		<license>
			<name>Apache License 2.0</name>
			<url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
			<distribution>repo</distribution>
		</license>
	</licenses>



	<developers>
		<!--not noly me, write a name here just for sonatype requirement -->
		<developer>
			<id>calvin</id>
			<name>Calvin Xiao</name>
			<email>calvin.xiao at vipshop.com</email>
			<roles>
				<role>developer</role>
			</roles>
			<timezone>+8</timezone>
		</developer>
	</developers>

	<!-- 使用aliyun镜像 -->
	<repositories>
		<repository>
			<id>spring-milestones</id>
			<name>Spring Milestones</name>
			<url>https://repo.spring.io/libs-milestone</url>
		</repository>
		<repository>
			<id>aliyun</id>
			<name>aliyun</name>
			<url>http://maven.aliyun.com/nexus/content/groups/public/</url>
		</repository>
	</repositories>

	<build>
		<finalName>${project.artifactId}-${maven.build.timestamp}</finalName>
	</build>
</project>
