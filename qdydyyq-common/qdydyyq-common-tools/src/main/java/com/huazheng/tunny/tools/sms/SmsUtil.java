//package com.huazheng.tunny.tools.sms;
//
//import com.aliyuncs.CommonRequest;
//import com.aliyuncs.CommonResponse;
//import com.aliyuncs.DefaultAcsClient;
//import com.aliyuncs.IAcsClient;
//import com.aliyuncs.http.MethodType;
//import com.aliyuncs.profile.DefaultProfile;
//import org.springframework.util.StringUtils;
//
///**
// * @Description: 阿里短信工具类
// * @Author: wx
// * @Date: 2019-12-30 09:41
// */
//
//public class SmsUtil {
//
//    private static final String ACCESS_KEY_ID = "kAqXHsBnkWTcEiYg1";
//    private static final String ACCESS_KEY_SECRET = "WCJVbBB4FPTjt6bQDomFnvIuVTrIkA";
//    private static final String SIGN_NAME = "云平台";
//    private static final String REGION_ID = "cn-hangzhou";
//    private static final String URL = "dysmsapi.aliyuncs.com";
//    private static final String TEMPLATE_CODE = "SMS_238461834";
//    private static final String VERSION = "2017-05-25";
//    private static IAcsClient client;
//
//    static {
//        DefaultProfile profile = DefaultProfile.getProfile(REGION_ID, ACCESS_KEY_ID, ACCESS_KEY_SECRET);
//        client = new DefaultAcsClient(profile);
//    }
//
//    /**
//     * @Description: 发送短信:支持对多个手机号码(上限1000)发送相同短信. 手机号码之间以英文逗号(,)分隔.
//     * @param: phone
//     * @Return: com.aliyuncs.CommonResponse
//     * @Author: wx
//     * @Date: 2019-12-30 10:10
//     */
//    public static final CommonResponse sendSms(String phone) {
//        return sendSms(phone, null);
//    }
//
//    /**
//     * @Description: 发送短信: templateParam 为 json 字符串.
//     * @param: phone, templateParam
//     * @Return: com.aliyuncs.CommonResponse
//     * @Author: wx
//     * @Date: 2019-12-30 10:12
//     */
//    public static final CommonResponse sendSms(String phone, String templateParam) {
//        return sendSms(phone, templateParam, null, null);
//    }
//
//    /**
//     * @Description: 发送短信: signName:签名,templateCode:模板code
//     * @param: phone, templateParam, signName, templateCode
//     * @Return: com.thingshive.edge.domain.vo.SmsResult
//     * @Author: wx
//     * @Date: 2019-12-30 10:03
//     */
//    public static final CommonResponse sendSms(String phone, String templateParam, String signName, String templateCode) {
//        if (StringUtils.isEmpty(signName)) {
//            signName = SIGN_NAME;
//        }
//        if (StringUtils.isEmpty(templateCode)) {
//            templateCode = TEMPLATE_CODE;
//        }
//        CommonResponse response = null;
//        CommonRequest request = new CommonRequest();
//        request.setMethod(MethodType.POST);
//        request.setDomain(URL);
//        request.setVersion(VERSION);
//        request.setAction("SendSms");
//        request.putQueryParameter("RegionId", REGION_ID);
//        request.putQueryParameter("PhoneNumbers", phone);
//        request.putQueryParameter("SignName", signName);
//        request.putQueryParameter("TemplateCode", templateCode);
//        // TemplateParam 为 json 格式;
//        if (!StringUtils.isEmpty(templateParam)) {
//            request.putQueryParameter("TemplateParam", templateParam);
//        }
//        try {
//            response = client.getCommonResponse(request);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return response;
//    }
//
//    /**
//     * @Description: 查询短信签名申请状态
//     * @param: singName
//     * @Return: com.thingshive.edge.domain.vo.SmsResult
//     * @Author: wx
//     * @Date: 2019-12-30 11:47
//     */
//    public static final CommonResponse querySmsSign(String singName) {
//        if (StringUtils.isEmpty(singName)) {
//            singName = SIGN_NAME;
//        }
//        CommonResponse response = null;
//        CommonRequest request = new CommonRequest();
//        request.setMethod(MethodType.POST);
//        request.setDomain(URL);
//        request.setVersion(VERSION);
//        request.setAction("QuerySmsSign");
//        request.putQueryParameter("RegionId", REGION_ID);
//        request.putQueryParameter("SignName", singName);
//        try {
//            response = client.getCommonResponse(request);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return response;
//    }
//
//    /**
//     * @Description: 查询短信模板的审核状态
//     * @param: templateCode
//     * @Return: com.aliyuncs.CommonResponse
//     * @Author: wx
//     * @Date: 2019-12-30 12:06
//     */
//    public static final CommonResponse querySmsTemplate(String templateCode) {
//        CommonResponse response = null;
//        CommonRequest request = new CommonRequest();
//        request.setMethod(MethodType.POST);
//        request.setDomain(URL);
//        request.setVersion(VERSION);
//        request.setAction("QuerySmsTemplate");
//        request.putQueryParameter("RegionId", REGION_ID);
//        request.putQueryParameter("TemplateCode", templateCode);
//        try {
//            response = client.getCommonResponse(request);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return response;
//
//    }
//
//    /**
//     * @Description: 查看短信发送记录和发送状态, BizId: 发送回执ID(没有传 null), sendDate: 最近30天,格式为yyyyMMdd,如20181225
//     * @param: phone, sendDate, currentPage, pageSize, bizId
//     * @Return: com.aliyuncs.CommonResponse
//     * @Author: wx
//     * @Date: 2019-12-30 13:28
//     */
//    public static final CommonResponse querySendDetails(String phone, String sendDate, Long currentPage, Long pageSize, String bizId) {
//        CommonResponse response = null;
//        CommonRequest request = new CommonRequest();
//        request.setMethod(MethodType.POST);
//        request.setDomain(URL);
//        request.setVersion(VERSION);
//        request.setAction("QuerySendDetails");
//        request.putQueryParameter("RegionId", REGION_ID);
//        request.putQueryParameter("PhoneNumber", phone);
//        request.putQueryParameter("SendDate", sendDate);
//        request.putQueryParameter("PageSize", Long.toString(pageSize));
//        request.putQueryParameter("CurrentPage", Long.toString(currentPage));
//        if (!StringUtils.isEmpty(bizId)) {
//            request.putQueryParameter("BizId", bizId);
//        }
//        try {
//            response = client.getCommonResponse(request);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return response;
//    }
//
//    /**
//     * @Description: 参数为 JSON 数组格式,
//     * @param: phoneNumberJson, signNameJson, templateParamJson
//     * @Return: com.aliyuncs.CommonResponse
//     * @Author: wx
//     * @Date: 2019-12-30 14:24
//     */
//    public static final CommonResponse sendBatchSms(String phoneNumberJson, String signNameJson, String templateParamJson) {
//        return sendBatchSms(phoneNumberJson, signNameJson, null, templateParamJson);
//    }
//
//    /**
//     * @Description: 在一次请求中分别向多个不同的手机号码发送不同签名的短信, 短信服务根据字段在JSON中的顺序判断发往指定手机号码的签名
//     * @param: phoneNumberJson, signNameJson, templateCode, TemplateParamJson
//     * @Return: com.aliyuncs.CommonResponse
//     * @Author: wx
//     * @Date: 2019-12-30 14:19
//     */
//    public static final CommonResponse sendBatchSms(String phoneNumberJson, String signNameJson, String templateCode, String templateParamJson) {
//        if (StringUtils.isEmpty(templateCode)) {
//            templateCode = TEMPLATE_CODE;
//        }
//        CommonResponse response = null;
//        CommonRequest request = new CommonRequest();
//        request.setMethod(MethodType.POST);
//        request.setDomain(URL);
//        request.setVersion(VERSION);
//        request.setAction("SendBatchSms");
//        request.putQueryParameter("RegionId", REGION_ID);
//        request.putQueryParameter("PhoneNumberJson", phoneNumberJson);
//        request.putQueryParameter("SignNameJson", signNameJson);
//        request.putQueryParameter("TemplateCode", templateCode);
//        if (!StringUtils.isEmpty(templateParamJson)) {
//            request.putQueryParameter("TemplateParamJson", templateParamJson);
//        }
//        try {
//            response = client.getCommonResponse(request);
//            System.out.println(response.getData());
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return response;
//    }
//
//}
//
//
