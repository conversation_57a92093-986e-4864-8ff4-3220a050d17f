package com.huazheng.tunny.dmhg.service;


import com.huazheng.tunny.dmhg.api.dto.EventData;

/**
 * 事件处理服务接口
 * 
 * <AUTHOR>
 * @date 2024/01/01
 */
public interface EventProcessingService {
    
    /**
     * 处理接收到的事件数据
     * 
     * @param contentType 内容类型
     * @param eventContent 事件内容（JSON/XML字符串）
     * @param binaryData 二进制数据（图片/视频）
     * @param deviceIp 设备IP
     * @param devicePort 设备端口
     * @return 解析后的事件数据
     */
    EventData processEvent(int contentType, String eventContent, byte[] binaryData, String deviceIp, String devicePort);
    
    /**
     * 解析JSON格式的事件数据
     * 
     * @param jsonContent JSON内容
     * @param deviceIp 设备IP
     * @param devicePort 设备端口
     * @return 解析后的事件数据
     */
    EventData parseJsonEvent(String jsonContent, String deviceIp, String devicePort);
    
    /**
     * 解析XML格式的事件数据
     * 
     * @param xmlContent XML内容
     * @param deviceIp 设备IP
     * @param devicePort 设备端口
     * @return 解析后的事件数据
     */
    EventData parseXmlEvent(String xmlContent, String deviceIp, String devicePort);
    
    /**
     * 处理二进制数据（图片/视频）
     * 
     * @param binaryData 二进制数据
     * @param contentType 内容类型
     * @param deviceIp 设备IP
     * @param devicePort 设备端口
     * @return 文件保存路径
     */
    String processBinaryData(byte[] binaryData, int contentType, String deviceIp, String devicePort);
    
    /**
     * 事件处理回调（用户可以重写此方法来处理具体的业务逻辑）
     * 
     * @param eventData 解析后的事件数据
     */
    void onEventReceived(EventData eventData);
}
