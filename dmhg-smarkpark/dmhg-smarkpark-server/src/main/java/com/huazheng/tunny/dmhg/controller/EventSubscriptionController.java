package com.huazheng.tunny.dmhg.controller;


import com.huazheng.tunny.dmhg.api.dto.ApiResponse;
import com.huazheng.tunny.dmhg.api.dto.DeviceInfoDTO;
import com.huazheng.tunny.dmhg.api.dto.SubscribeEventRequest;
import com.huazheng.tunny.dmhg.api.dto.SubscriptionStatusResponse;
import com.huazheng.tunny.dmhg.service.EventSubscriptionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 事件订阅控制器
 * 
 * <AUTHOR>
 * @date 2024/01/01
 */
@Slf4j
@RestController
@RequestMapping("/api/event")
public class EventSubscriptionController {
    
    @Autowired
    private EventSubscriptionService eventSubscriptionService;
    
    /**
     * 订阅设备事件
     * 
     * @param request 订阅请求
     * @return 订阅结果
     */
    @PostMapping("/subscribe")
    public ApiResponse<SubscriptionStatusResponse> subscribeEvent(@RequestBody SubscribeEventRequest request) {
        try {
            log.info("接收到事件订阅请求: {}", request);
            
            // 参数校验
            if (request.getDevIp() == null || request.getDevIp().trim().isEmpty()) {
                return ApiResponse.error(400, "设备IP地址不能为空");
            }
            if (request.getDevPort() == null || request.getDevPort().trim().isEmpty()) {
                return ApiResponse.error(400, "设备端口不能为空");
            }
            if (request.getUsername() == null || request.getUsername().trim().isEmpty()) {
                return ApiResponse.error(400, "设备用户名不能为空");
            }
            if (request.getPassword() == null || request.getPassword().trim().isEmpty()) {
                return ApiResponse.error(400, "设备密码不能为空");
            }
            
            // 构建设备信息
            DeviceInfoDTO deviceInfo = new DeviceInfoDTO();
            deviceInfo.setDevIp(request.getDevIp());
            deviceInfo.setDevPort(request.getDevPort());
            deviceInfo.setUsername(request.getUsername());
            deviceInfo.setPassword(request.getPassword());
            deviceInfo.setHttpType(request.getHttpType() != null ? request.getHttpType() : 0);
            
            boolean success;
            if (request.getSubscribeAll() != null && request.getSubscribeAll()) {
                // 订阅所有事件
                success = eventSubscriptionService.subscribeAllEvents(deviceInfo);
            } else {
                // 订阅指定事件
                String eventType = request.getEventType() != null ? request.getEventType() : "IDCardInfoEvent";
                String minorEvent = request.getMinorEvent() != null ? request.getMinorEvent() : "0x69,0x70,0x71";
                success = eventSubscriptionService.subscribeEvent(deviceInfo, eventType, minorEvent);
            }
            
            if (success) {
                SubscriptionStatusResponse statusResponse = new SubscriptionStatusResponse(
                    deviceInfo.getDevIp(), 
                    deviceInfo.getDevPort(), 
                    true, 
                    "订阅成功"
                );
                return ApiResponse.success("事件订阅成功", statusResponse);
            } else {
                return ApiResponse.error("事件订阅失败");
            }
            
        } catch (Exception e) {
            log.error("订阅事件异常", e);
            return ApiResponse.error("订阅事件异常: " + e.getMessage());
        }
    }
    
    /**
     * 取消订阅设备事件
     * 
     * @param devIp 设备IP
     * @param devPort 设备端口
     * @return 取消订阅结果
     */
    @DeleteMapping("/unsubscribe")
    public ApiResponse<SubscriptionStatusResponse> unsubscribeEvent(
            @RequestParam String devIp, 
            @RequestParam String devPort) {
        try {
            log.info("接收到取消订阅请求: {}:{}", devIp, devPort);
            
            // 参数校验
            if (devIp == null || devIp.trim().isEmpty()) {
                return ApiResponse.error(400, "设备IP地址不能为空");
            }
            if (devPort == null || devPort.trim().isEmpty()) {
                return ApiResponse.error(400, "设备端口不能为空");
            }
            
            // 构建设备信息
            DeviceInfoDTO deviceInfo = new DeviceInfoDTO();
            deviceInfo.setDevIp(devIp);
            deviceInfo.setDevPort(devPort);
            
            boolean success = eventSubscriptionService.unsubscribeEvent(deviceInfo);
            
            if (success) {
                SubscriptionStatusResponse statusResponse = new SubscriptionStatusResponse(
                    devIp, 
                    devPort, 
                    false, 
                    "取消订阅成功"
                );
                return ApiResponse.success("取消订阅成功", statusResponse);
            } else {
                return ApiResponse.error("取消订阅失败");
            }
            
        } catch (Exception e) {
            log.error("取消订阅异常", e);
            return ApiResponse.error("取消订阅异常: " + e.getMessage());
        }
    }
    
    /**
     * 查询设备订阅状态
     * 
     * @param devIp 设备IP
     * @param devPort 设备端口
     * @return 订阅状态
     */
    @GetMapping("/status")
    public ApiResponse<SubscriptionStatusResponse> getSubscriptionStatus(
            @RequestParam String devIp, 
            @RequestParam String devPort) {
        try {
            log.info("查询订阅状态: {}:{}", devIp, devPort);
            
            // 参数校验
            if (devIp == null || devIp.trim().isEmpty()) {
                return ApiResponse.error(400, "设备IP地址不能为空");
            }
            if (devPort == null || devPort.trim().isEmpty()) {
                return ApiResponse.error(400, "设备端口不能为空");
            }
            
            // 构建设备信息
            DeviceInfoDTO deviceInfo = new DeviceInfoDTO();
            deviceInfo.setDevIp(devIp);
            deviceInfo.setDevPort(devPort);
            
            boolean isSubscribed = eventSubscriptionService.isSubscribed(deviceInfo);
            String status = eventSubscriptionService.getSubscriptionStatus(deviceInfo);
            
            SubscriptionStatusResponse statusResponse = new SubscriptionStatusResponse(
                devIp, 
                devPort, 
                isSubscribed, 
                status
            );
            
            return ApiResponse.success("查询成功", statusResponse);
            
        } catch (Exception e) {
            log.error("查询订阅状态异常", e);
            return ApiResponse.error("查询订阅状态异常: " + e.getMessage());
        }
    }
}
