package com.huazheng.tunny.dmhg.service;


import com.huazheng.tunny.dmhg.listen.EnhancedListenThread;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 增强版监听服务
 * 管理事件监听的启动、停止和状态查询
 * 
 * <AUTHOR>
 * @date 2024/01/01
 */
@Slf4j
@Service
public class EnhancedListenService {
    
    @Autowired
    private EnhancedListenThread enhancedListenThread;
    
    /**
     * 启动事件监听
     * 
     * @param port 监听端口
     * @return 启动结果
     */
    public boolean startListen(int port) {
        try {
            if (enhancedListenThread.isRunning()) {
                log.warn("事件监听已在运行中");
                return false;
            }
            
            enhancedListenThread.setListenPort(port);
            enhancedListenThread.startListen();
            
            // 等待一段时间确保启动成功
            Thread.sleep(1000);
            
            if (enhancedListenThread.isRunning()) {
                log.info("事件监听启动成功 - 端口: {}", port);
                return true;
            } else {
                log.error("事件监听启动失败 - 端口: {}", port);
                return false;
            }
            
        } catch (Exception e) {
            log.error("启动事件监听异常 - 端口: {}", port, e);
            return false;
        }
    }
    
    /**
     * 启动事件监听（使用默认端口9999）
     */
    public boolean startListen() {
        return startListen(9999);
    }
    
    /**
     * 停止事件监听
     * 
     * @return 停止结果
     */
    public boolean stopListen() {
        try {
            if (!enhancedListenThread.isRunning()) {
                log.warn("事件监听未在运行");
                return true;
            }
            
            enhancedListenThread.stopListen();
            
            // 等待一段时间确保停止成功
            Thread.sleep(1000);
            
            if (!enhancedListenThread.isRunning()) {
                log.info("事件监听停止成功");
                return true;
            } else {
                log.error("事件监听停止失败");
                return false;
            }
            
        } catch (Exception e) {
            log.error("停止事件监听异常", e);
            return false;
        }
    }
    
    /**
     * 检查监听状态
     * 
     * @return 是否正在监听
     */
    public boolean isListening() {
        return enhancedListenThread.isRunning();
    }
    
    /**
     * 获取监听状态信息
     * 
     * @return 状态描述
     */
    public String getListenStatus() {
        if (enhancedListenThread.isRunning()) {
            return "正在监听事件";
        } else {
            return "监听已停止";
        }
    }
}
