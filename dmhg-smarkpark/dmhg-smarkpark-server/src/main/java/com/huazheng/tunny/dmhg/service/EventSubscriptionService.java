package com.huazheng.tunny.dmhg.service;


import com.huazheng.tunny.dmhg.api.dto.DeviceInfoDTO;

/**
 * 事件订阅服务接口
 * 
 * <AUTHOR>
 * @date 2024/01/01
 */
public interface EventSubscriptionService {
    
    /**
     * 订阅设备事件
     * 
     * @param deviceInfo 设备信息
     * @param eventType 事件类型 (如: IDCardInfoEvent, FaceDetectionEvent等)
     * @param minorEvent 子事件类型 (如: 0x69,0x70,0x71)
     * @return 订阅结果
     */
    boolean subscribeEvent(DeviceInfoDTO deviceInfo, String eventType, String minorEvent);
    
    /**
     * 订阅所有事件
     * 
     * @param deviceInfo 设备信息
     * @return 订阅结果
     */
    boolean subscribeAllEvents(DeviceInfoDTO deviceInfo);
    
    /**
     * 取消订阅设备事件
     * 
     * @param deviceInfo 设备信息
     * @return 取消订阅结果
     */
    boolean unsubscribeEvent(DeviceInfoDTO deviceInfo);
    
    /**
     * 检查设备订阅状态
     * 
     * @param deviceInfo 设备信息
     * @return 是否已订阅
     */
    boolean isSubscribed(DeviceInfoDTO deviceInfo);
    
    /**
     * 获取设备订阅状态信息
     * 
     * @param deviceInfo 设备信息
     * @return 订阅状态信息
     */
    String getSubscriptionStatus(DeviceInfoDTO deviceInfo);
}
