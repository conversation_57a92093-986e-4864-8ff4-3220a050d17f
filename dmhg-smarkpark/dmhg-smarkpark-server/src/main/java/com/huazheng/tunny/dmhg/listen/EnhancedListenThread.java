package com.huazheng.tunny.dmhg.listen;


import com.huazheng.tunny.dmhg.api.dto.EventData;
import com.huazheng.tunny.dmhg.enums.ContentTypeEnum;
import com.huazheng.tunny.dmhg.service.EventProcessingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 增强版监听线程
 * 集成了事件处理服务，提供更完善的事件解析和处理功能
 * 
 * <AUTHOR>
 * @date 2024/01/01
 */
@Slf4j
@Component
public class EnhancedListenThread implements Runnable {
    
    @Autowired
    private EventProcessingService eventProcessingService;
    
    private int listenPort = 9999;
    private volatile boolean running = false;
    private ServerSocket serverSocket;
    
    public EnhancedListenThread() {
    }
    
    public EnhancedListenThread(int listenPort) {
        this.listenPort = listenPort;
    }
    
    @Override
    public void run() {
        try {
            serverSocket = new ServerSocket(listenPort);
            running = true;
            log.info("启动增强版事件监听 - 端口: {}", listenPort);
            
            while (running && !Thread.currentThread().isInterrupted()) {
                try {
                    Socket clientSocket = serverSocket.accept();
                    clientSocket.setKeepAlive(true);
                    
                    String deviceIp = clientSocket.getInetAddress().getHostAddress();
                    log.info("接收到设备连接 - 设备IP: {}", deviceIp);
                    
                    if (clientSocket.isConnected()) {
                        handleDeviceData(clientSocket, deviceIp);
                    }
                    clientSocket.close();
                    
                } catch (Exception e) {
                    if (running) {
                        log.error("处理设备连接异常", e);
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("启动监听异常", e);
        } finally {
            stopListen();
        }
    }
    
    /**
     * 处理设备推送的数据
     */
    private void handleDeviceData(Socket clientSocket, String deviceIp) throws Exception {
        InputStream inputStream = clientSocket.getInputStream();
        OutputStream outputStream = clientSocket.getOutputStream();
        
        // 读取所有数据
        ByteArrayOutputStream dataBuffer = new ByteArrayOutputStream();
        byte[] buffer = new byte[2 * 1024 * 1024]; // 2MB缓冲区
        int length;
        
        String receivedData = "";
        while ((length = inputStream.read(buffer)) > 0) {
            dataBuffer.write(buffer, 0, length);
            
            String currentData = dataBuffer.toString("UTF-8");
            receivedData = receivedData + currentData;
            
            // 检查是否接收完整（通过boundary结束符判断）
            if (isDataComplete(receivedData)) {
                break;
            }
        }
        
        // 发送HTTP响应
        sendHttpResponse(outputStream);
        
        // 解析接收到的数据
        parseReceivedData(dataBuffer.toByteArray(), deviceIp);
        
        outputStream.close();
        inputStream.close();
    }
    
    /**
     * 检查数据是否接收完整
     */
    private boolean isDataComplete(String data) {
        // 查找boundary标记
        String boundaryMark = "boundary=";
        int beginIndex = data.indexOf(boundaryMark);
        if (beginIndex == -1) {
            return false;
        }
        
        beginIndex += boundaryMark.length();
        int endIndex = data.indexOf("\r\n", beginIndex);
        if (endIndex == -1) {
            return false;
        }
        
        String boundary = data.substring(beginIndex, endIndex).trim();
        String endMark = "--" + boundary + "--";
        
        return data.contains(endMark);
    }
    
    /**
     * 发送HTTP响应
     */
    private void sendHttpResponse(OutputStream outputStream) throws Exception {
        String response = "HTTP/1.1 200 OK\r\n" +
                "Connection: close\r\n" +
                "\r\n";
        outputStream.write(response.getBytes());
        outputStream.flush();
    }
    
    /**
     * 解析接收到的数据
     */
    private void parseReceivedData(byte[] rawData, String deviceIp) {
        try {
            log.info("开始解析设备数据 - 设备: {}, 数据大小: {} bytes", deviceIp, rawData.length);

            // 解析multipart数据
            MultipartDataParser parser = new MultipartDataParser(rawData);
            parser.parse();

            List<MultipartDataParser.Part> parts = parser.getParts();
            log.info("解析出 {} 个数据部分", parts.size());

            // 分类处理不同类型的数据部分
            EventData mainEventData = null;
            List<String> imageFiles = new ArrayList<>();
            List<String> videoFiles = new ArrayList<>();

            for (int i = 0; i < parts.size(); i++) {
                MultipartDataParser.Part part = parts.get(i);
                log.info("处理第 {} 个数据部分 - 类型: {}, 大小: {} bytes",
                        i + 1, part.getContentType(), part.getData() != null ? part.getData().length : 0);

                EventData partEventData = processDataPart(part, deviceIp, i + 1);

                // 保存主要事件数据（通常是第一个文本部分）
                if (mainEventData == null && partEventData != null && partEventData.getEventType() != null) {
                    mainEventData = partEventData;
                }

                // 收集文件路径
                if (partEventData != null) {
                    if (partEventData.getImagePath() != null) {
                        imageFiles.add(partEventData.getImagePath());
                    }
                    if (partEventData.getVideoPath() != null) {
                        videoFiles.add(partEventData.getVideoPath());
                    }
                }
            }

            // 合并事件数据
            if (mainEventData != null) {
                // 将所有图片和视频文件路径添加到主事件数据中
                if (!imageFiles.isEmpty()) {
                    mainEventData.getEventDetails().put("imageFiles", imageFiles);
                    log.info("事件包含 {} 个图片文件", imageFiles.size());
                }
                if (!videoFiles.isEmpty()) {
                    mainEventData.getEventDetails().put("videoFiles", videoFiles);
                    log.info("事件包含 {} 个视频文件", videoFiles.size());
                }

                // 调用完整事件处理
                handleCompleteEvent(mainEventData);
            }

        } catch (Exception e) {
            log.error("解析设备数据异常 - 设备: {}", deviceIp, e);
        }
    }
    
    /**
     * 处理单个数据部分
     */
    private EventData processDataPart(MultipartDataParser.Part part, String deviceIp, int partIndex) {
        try {
            String contentType = part.getContentType();
            int typeEnum = ContentTypeEnum.getEventType(contentType);

            log.debug("处理数据部分 {} - 内容类型: {}, 类型枚举: {}", partIndex, contentType, typeEnum);

            EventData eventData;
            if (part.isTextContent()) {
                // 处理文本内容（JSON/XML）
                String textContent = new String(part.getData(), "UTF-8");
                log.info("解析文本内容 - 长度: {} 字符", textContent.length());
                log.debug("文本内容预览: {}", textContent.length() > 200 ? textContent.substring(0, 200) + "..." : textContent);

                eventData = eventProcessingService.processEvent(typeEnum, textContent, null, deviceIp, "80");

                // 详细解析事件内容
                if (eventData != null) {
                    parseDetailedEventContent(eventData, textContent, typeEnum);
                }

            } else {
                // 处理二进制内容（图片/视频）
                log.info("处理二进制数据 - 大小: {} bytes, 类型: {}", part.getData().length, contentType);
                eventData = eventProcessingService.processEvent(typeEnum, null, part.getData(), deviceIp, "80");
            }

            if (eventData != null) {
                log.info("成功处理数据部分 {} - 设备: {}, 事件类型: {}, 数据类型: {}",
                        partIndex, deviceIp, eventData.getEventType(), eventData.getDataType());
            }

            return eventData;

        } catch (Exception e) {
            log.error("处理数据部分 {} 异常 - 设备: {}", partIndex, deviceIp, e);
            return null;
        }
    }
    
    /**
     * 启动监听
     */
    public void startListen() {
        if (!running) {
            Thread listenThread = new Thread(this);
            listenThread.setName("EnhancedListen-" + listenPort);
            listenThread.start();
        }
    }
    
    /**
     * 停止监听
     */
    public void stopListen() {
        running = false;
        try {
            if (serverSocket != null && !serverSocket.isClosed()) {
                serverSocket.close();
            }
            log.info("停止事件监听完成");
        } catch (Exception e) {
            log.error("停止监听异常", e);
        }
    }
    
    /**
     * 设置监听端口
     */
    public void setListenPort(int port) {
        this.listenPort = port;
    }
    
    /**
     * 获取监听状态
     */
    public boolean isRunning() {
        return running;
    }

    /**
     * 详细解析事件内容
     */
    private void parseDetailedEventContent(EventData eventData, String textContent, int contentType) {
        try {
            if (eventData.getEventType() == null) {
                return;
            }

            log.info("详细解析事件内容 - 事件类型: {}", eventData.getEventType());

            switch (eventData.getEventType()) {
                case "IDCardInfoEvent":
                    parseIDCardEvent(eventData, textContent, contentType);
                    break;
                case "FaceDetectionEvent":
                case "FaceRecognitionEvent":
                    parseFaceEvent(eventData, textContent, contentType);
                    break;
                case "VehicleDetectionEvent":
                case "PlateRecognitionEvent":
                    parseVehicleEvent(eventData, textContent, contentType);
                    break;
                case "PersonDetectionEvent":
                    parsePersonEvent(eventData, textContent, contentType);
                    break;
                case "AccessControlEvent":
                    parseAccessControlEvent(eventData, textContent, contentType);
                    break;
                default:
                    parseGenericEvent(eventData, textContent, contentType);
                    break;
            }

        } catch (Exception e) {
            log.error("详细解析事件内容异常 - 事件类型: {}", eventData.getEventType(), e);
        }
    }

    /**
     * 解析身份证事件
     */
    private void parseIDCardEvent(EventData eventData, String textContent, int contentType) {
        log.info("解析身份证事件");
        try {
            if (contentType == ContentTypeEnum.APPLICATION_JSON) {
                org.json.JSONObject jsonObj = new org.json.JSONObject(textContent);
                if (jsonObj.has("IDCardInfo")) {
                    org.json.JSONObject idCardInfo = jsonObj.getJSONObject("IDCardInfo");

                    // 提取身份证信息
                    Map<String, Object> idCardDetails = new HashMap<>();
                    if (idCardInfo.has("name")) {
                        idCardDetails.put("姓名", idCardInfo.getString("name"));
                    }
                    if (idCardInfo.has("idNumber")) {
                        idCardDetails.put("身份证号", idCardInfo.getString("idNumber"));
                    }
                    if (idCardInfo.has("gender")) {
                        idCardDetails.put("性别", idCardInfo.getString("gender"));
                    }
                    if (idCardInfo.has("nation")) {
                        idCardDetails.put("民族", idCardInfo.getString("nation"));
                    }
                    if (idCardInfo.has("birthday")) {
                        idCardDetails.put("出生日期", idCardInfo.getString("birthday"));
                    }
                    if (idCardInfo.has("address")) {
                        idCardDetails.put("地址", idCardInfo.getString("address"));
                    }

                    eventData.getEventDetails().put("身份证信息", idCardDetails);
                    log.info("身份证事件解析完成 - 姓名: {}, 身份证号: {}",
                            idCardDetails.get("姓名"), idCardDetails.get("身份证号"));
                }
            }
        } catch (Exception e) {
            log.error("解析身份证事件异常", e);
        }
    }

    /**
     * 解析人脸事件
     */
    private void parseFaceEvent(EventData eventData, String textContent, int contentType) {
        log.info("解析人脸检测/识别事件");
        try {
            if (contentType == ContentTypeEnum.APPLICATION_JSON) {
                org.json.JSONObject jsonObj = new org.json.JSONObject(textContent);

                Map<String, Object> faceDetails = new HashMap<>();

                // 查找人脸相关信息
                if (jsonObj.has("FaceDetection")) {
                    org.json.JSONObject faceInfo = jsonObj.getJSONObject("FaceDetection");
                    extractFaceInfo(faceInfo, faceDetails);
                } else if (jsonObj.has("FaceRecognition")) {
                    org.json.JSONObject faceInfo = jsonObj.getJSONObject("FaceRecognition");
                    extractFaceInfo(faceInfo, faceDetails);
                }

                if (!faceDetails.isEmpty()) {
                    eventData.getEventDetails().put("人脸信息", faceDetails);
                    log.info("人脸事件解析完成 - 详情: {}", faceDetails);
                }
            }
        } catch (Exception e) {
            log.error("解析人脸事件异常", e);
        }
    }

    /**
     * 提取人脸信息
     */
    private void extractFaceInfo(org.json.JSONObject faceInfo, Map<String, Object> faceDetails) {
        if (faceInfo.has("faceID")) {
            faceDetails.put("人脸ID", faceInfo.getString("faceID"));
        }
        if (faceInfo.has("confidence")) {
            faceDetails.put("置信度", faceInfo.getDouble("confidence"));
        }
        if (faceInfo.has("age")) {
            faceDetails.put("年龄", faceInfo.getInt("age"));
        }
        if (faceInfo.has("gender")) {
            faceDetails.put("性别", faceInfo.getString("gender"));
        }
        if (faceInfo.has("emotion")) {
            faceDetails.put("情绪", faceInfo.getString("emotion"));
        }
        if (faceInfo.has("name")) {
            faceDetails.put("姓名", faceInfo.getString("name"));
        }
    }

    /**
     * 解析车辆事件
     */
    private void parseVehicleEvent(EventData eventData, String textContent, int contentType) {
        log.info("解析车辆检测/车牌识别事件");
        try {
            if (contentType == ContentTypeEnum.APPLICATION_JSON) {
                org.json.JSONObject jsonObj = new org.json.JSONObject(textContent);

                Map<String, Object> vehicleDetails = new HashMap<>();

                // 查找车辆相关信息
                if (jsonObj.has("VehicleInfo")) {
                    org.json.JSONObject vehicleInfo = jsonObj.getJSONObject("VehicleInfo");
                    extractVehicleInfo(vehicleInfo, vehicleDetails);
                } else if (jsonObj.has("PlateRecognition")) {
                    org.json.JSONObject plateInfo = jsonObj.getJSONObject("PlateRecognition");
                    extractVehicleInfo(plateInfo, vehicleDetails);
                }

                if (!vehicleDetails.isEmpty()) {
                    eventData.getEventDetails().put("车辆信息", vehicleDetails);
                    log.info("车辆事件解析完成 - 车牌: {}, 车型: {}",
                            vehicleDetails.get("车牌号码"), vehicleDetails.get("车辆类型"));
                }
            }
        } catch (Exception e) {
            log.error("解析车辆事件异常", e);
        }
    }

    /**
     * 提取车辆信息
     */
    private void extractVehicleInfo(org.json.JSONObject vehicleInfo, Map<String, Object> vehicleDetails) {
        if (vehicleInfo.has("plateNumber")) {
            vehicleDetails.put("车牌号码", vehicleInfo.getString("plateNumber"));
        }
        if (vehicleInfo.has("vehicleType")) {
            vehicleDetails.put("车辆类型", vehicleInfo.getString("vehicleType"));
        }
        if (vehicleInfo.has("color")) {
            vehicleDetails.put("车辆颜色", vehicleInfo.getString("color"));
        }
        if (vehicleInfo.has("speed")) {
            vehicleDetails.put("车辆速度", vehicleInfo.getString("speed"));
        }
        if (vehicleInfo.has("direction")) {
            vehicleDetails.put("行驶方向", vehicleInfo.getString("direction"));
        }
        if (vehicleInfo.has("confidence")) {
            vehicleDetails.put("识别置信度", vehicleInfo.getDouble("confidence"));
        }
    }

    /**
     * 解析人员检测事件
     */
    private void parsePersonEvent(EventData eventData, String textContent, int contentType) {
        log.info("解析人员检测事件");
        try {
            if (contentType == ContentTypeEnum.APPLICATION_JSON) {
                org.json.JSONObject jsonObj = new org.json.JSONObject(textContent);

                Map<String, Object> personDetails = new HashMap<>();

                if (jsonObj.has("PersonInfo")) {
                    org.json.JSONObject personInfo = jsonObj.getJSONObject("PersonInfo");

                    if (personInfo.has("personID")) {
                        personDetails.put("人员ID", personInfo.getString("personID"));
                    }
                    if (personInfo.has("position")) {
                        personDetails.put("位置", personInfo.getString("position"));
                    }
                    if (personInfo.has("behavior")) {
                        personDetails.put("行为", personInfo.getString("behavior"));
                    }
                    if (personInfo.has("count")) {
                        personDetails.put("人数", personInfo.getInt("count"));
                    }
                }

                if (!personDetails.isEmpty()) {
                    eventData.getEventDetails().put("人员信息", personDetails);
                    log.info("人员事件解析完成 - 详情: {}", personDetails);
                }
            }
        } catch (Exception e) {
            log.error("解析人员事件异常", e);
        }
    }

    /**
     * 解析门禁事件
     */
    private void parseAccessControlEvent(EventData eventData, String textContent, int contentType) {
        log.info("解析门禁事件");
        try {
            if (contentType == ContentTypeEnum.APPLICATION_JSON) {
                org.json.JSONObject jsonObj = new org.json.JSONObject(textContent);

                Map<String, Object> accessDetails = new HashMap<>();

                if (jsonObj.has("AccessControlInfo")) {
                    org.json.JSONObject accessInfo = jsonObj.getJSONObject("AccessControlInfo");

                    if (accessInfo.has("cardNo")) {
                        accessDetails.put("卡号", accessInfo.getString("cardNo"));
                    }
                    if (accessInfo.has("employeeNo")) {
                        accessDetails.put("员工号", accessInfo.getString("employeeNo"));
                    }
                    if (accessInfo.has("name")) {
                        accessDetails.put("姓名", accessInfo.getString("name"));
                    }
                    if (accessInfo.has("accessResult")) {
                        accessDetails.put("通行结果", accessInfo.getString("accessResult"));
                    }
                    if (accessInfo.has("doorName")) {
                        accessDetails.put("门禁点", accessInfo.getString("doorName"));
                    }
                }

                if (!accessDetails.isEmpty()) {
                    eventData.getEventDetails().put("门禁信息", accessDetails);
                    log.info("门禁事件解析完成 - 姓名: {}, 通行结果: {}",
                            accessDetails.get("姓名"), accessDetails.get("通行结果"));
                }
            }
        } catch (Exception e) {
            log.error("解析门禁事件异常", e);
        }
    }

    /**
     * 解析通用事件
     */
    private void parseGenericEvent(EventData eventData, String textContent, int contentType) {
        log.info("解析通用事件 - 事件类型: {}", eventData.getEventType());
        try {
            // 对于未知类型的事件，尝试提取一些通用信息
            if (contentType == ContentTypeEnum.APPLICATION_JSON) {
                org.json.JSONObject jsonObj = new org.json.JSONObject(textContent);

                Map<String, Object> genericDetails = new HashMap<>();

                // 提取一些常见字段
                String[] commonFields = {"description", "status", "level", "priority", "location", "zone"};
                for (String field : commonFields) {
                    if (jsonObj.has(field)) {
                        genericDetails.put(field, jsonObj.get(field));
                    }
                }

                if (!genericDetails.isEmpty()) {
                    eventData.getEventDetails().put("通用信息", genericDetails);
                    log.info("通用事件解析完成 - 详情: {}", genericDetails);
                }
            }
        } catch (Exception e) {
            log.error("解析通用事件异常", e);
        }
    }

    /**
     * 处理完整事件
     */
    private void handleCompleteEvent(EventData eventData) {
        try {
            log.info("=== 完整事件处理开始 ===");
            log.info("设备信息: {}:{}", eventData.getDeviceIp(), eventData.getDevicePort());
            log.info("事件类型: {}", eventData.getEventType());
            log.info("事件时间: {}", eventData.getDateTime());
            log.info("通道ID: {}", eventData.getChannelID());
            log.info("数据类型: {}", eventData.getDataType());

            // 打印详细事件信息
            if (eventData.getEventDetails() != null && !eventData.getEventDetails().isEmpty()) {
                log.info("事件详情:");
                for (Map.Entry<String, Object> entry : eventData.getEventDetails().entrySet()) {
                    log.info("  {}: {}", entry.getKey(), entry.getValue());
                }
            }

            // 调用原有的事件处理回调
            if (eventProcessingService != null) {
                eventProcessingService.onEventReceived(eventData);
            }

            // TODO: 在这里添加您的自定义业务逻辑
            // 例如：
            // 1. 保存到数据库
            // 2. 发送通知
            // 3. 触发其他业务流程
            // 4. 调用第三方接口

            log.info("=== 完整事件处理结束 ===");

        } catch (Exception e) {
            log.error("处理完整事件异常", e);
        }
    }
}
