package com.huazheng.tunny.dmhg.controller;


import com.huazheng.tunny.dmhg.api.dto.ApiResponse;
import com.huazheng.tunny.dmhg.api.dto.EventData;
import com.huazheng.tunny.dmhg.enums.ContentTypeEnum;
import com.huazheng.tunny.dmhg.service.EventProcessingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 事件接收控制器
 * 提供HTTP接口用于接收和解析设备推送的事件数据
 * 
 * <AUTHOR>
 * @date 2024/01/01
 */
@Slf4j
@RestController
@RequestMapping("/api/event")
public class EventReceiveController {
    
    @Autowired
    private EventProcessingService eventProcessingService;
    
    /**
     * 接收设备推送的事件数据（JSON格式）
     * 
     * @param request HTTP请求
     * @return 处理结果
     */
    @PostMapping("/receive/json")
    public ApiResponse<EventData> receiveJsonEvent(HttpServletRequest request,
                                                   @RequestParam(required = false) String deviceIp,
                                                   @RequestParam(required = false) String devicePort) {
        try {
            // 获取设备信息
            String devIp = deviceIp != null ? deviceIp : request.getRemoteAddr();
            String devPort = devicePort != null ? devicePort : "80";
            
            // 读取请求体中的JSON数据
            StringBuilder jsonBuilder = new StringBuilder();
            String line;
            try (BufferedReader reader = request.getReader()) {
                while ((line = reader.readLine()) != null) {
                    jsonBuilder.append(line);
                }
            }
            
            String jsonContent = jsonBuilder.toString();
            log.info("接收到JSON事件数据 - 设备: {}:{}, 数据长度: {}", devIp, devPort, jsonContent.length());
            
            if (jsonContent.trim().isEmpty()) {
                return ApiResponse.error("事件数据为空");
            }
            
            // 解析事件数据
            EventData eventData = eventProcessingService.parseJsonEvent(jsonContent, devIp, devPort);
            
            return ApiResponse.success("事件接收成功", eventData);
            
        } catch (Exception e) {
            log.error("接收JSON事件数据异常", e);
            return ApiResponse.error("接收事件数据异常: " + e.getMessage());
        }
    }
    
    /**
     * 接收设备推送的事件数据（XML格式）
     * 
     * @param request HTTP请求
     * @return 处理结果
     */
    @PostMapping("/receive/xml")
    public ApiResponse<EventData> receiveXmlEvent(HttpServletRequest request,
                                                 @RequestParam(required = false) String deviceIp,
                                                 @RequestParam(required = false) String devicePort) {
        try {
            // 获取设备信息
            String devIp = deviceIp != null ? deviceIp : request.getRemoteAddr();
            String devPort = devicePort != null ? devicePort : "80";
            
            // 读取请求体中的XML数据
            StringBuilder xmlBuilder = new StringBuilder();
            String line;
            try (BufferedReader reader = request.getReader()) {
                while ((line = reader.readLine()) != null) {
                    xmlBuilder.append(line);
                }
            }
            
            String xmlContent = xmlBuilder.toString();
            log.info("接收到XML事件数据 - 设备: {}:{}, 数据长度: {}", devIp, devPort, xmlContent.length());
            
            if (xmlContent.trim().isEmpty()) {
                return ApiResponse.error("事件数据为空");
            }
            
            // 解析事件数据
            EventData eventData = eventProcessingService.parseXmlEvent(xmlContent, devIp, devPort);
            
            return ApiResponse.success("事件接收成功", eventData);
            
        } catch (Exception e) {
            log.error("接收XML事件数据异常", e);
            return ApiResponse.error("接收事件数据异常: " + e.getMessage());
        }
    }
    
    /**
     * 接收设备推送的multipart格式事件数据
     * 
     * @param request HTTP请求
     * @return 处理结果
     */
    @PostMapping("/receive/multipart")
    public ApiResponse<List<EventData>> receiveMultipartEvent(MultipartHttpServletRequest request,
                                                             @RequestParam(required = false) String deviceIp,
                                                             @RequestParam(required = false) String devicePort) {
        try {
            // 获取设备信息
            String devIp = deviceIp != null ? deviceIp : request.getRemoteAddr();
            String devPort = devicePort != null ? devicePort : "80";
            
            log.info("接收到Multipart事件数据 - 设备: {}:{}", devIp, devPort);
            
            List<EventData> eventDataList = new ArrayList<>();
            
            // 处理所有的multipart数据
            Map<String, MultipartFile> fileMap = request.getFileMap();
            for (Map.Entry<String, MultipartFile> entry : fileMap.entrySet()) {
                MultipartFile file = entry.getValue();
                String contentType = file.getContentType();
                
                log.info("处理multipart数据 - 名称: {}, 类型: {}, 大小: {}", 
                        file.getName(), contentType, file.getSize());
                
                try {
                    byte[] fileBytes = file.getBytes();
                    int typeEnum = ContentTypeEnum.getEventType(contentType);
                    
                    EventData eventData;
                    if (isTextContent(contentType)) {
                        // 处理文本内容
                        String textContent = new String(fileBytes, "UTF-8");
                        eventData = eventProcessingService.processEvent(typeEnum, textContent, null, devIp, devPort);
                    } else {
                        // 处理二进制内容
                        eventData = eventProcessingService.processEvent(typeEnum, null, fileBytes, devIp, devPort);
                    }
                    
                    if (eventData != null) {
                        eventDataList.add(eventData);
                    }
                    
                } catch (IOException e) {
                    log.error("读取multipart文件数据异常", e);
                }
            }
            
            return ApiResponse.success("事件接收成功", eventDataList);
            
        } catch (Exception e) {
            log.error("接收Multipart事件数据异常", e);
            return ApiResponse.error("接收事件数据异常: " + e.getMessage());
        }
    }
    
    /**
     * 手动解析事件数据（用于测试）
     * 
     * @param eventContent 事件内容
     * @param contentType 内容类型
     * @param deviceIp 设备IP
     * @param devicePort 设备端口
     * @return 解析结果
     */
    @PostMapping("/parse")
    public ApiResponse<EventData> parseEvent(@RequestParam String eventContent,
                                           @RequestParam(defaultValue = "json") String contentType,
                                           @RequestParam(defaultValue = "127.0.0.1") String deviceIp,
                                           @RequestParam(defaultValue = "80") String devicePort) {
        try {
            log.info("手动解析事件数据 - 设备: {}:{}, 类型: {}", deviceIp, devicePort, contentType);
            
            EventData eventData;
            if ("json".equalsIgnoreCase(contentType)) {
                eventData = eventProcessingService.parseJsonEvent(eventContent, deviceIp, devicePort);
            } else if ("xml".equalsIgnoreCase(contentType)) {
                eventData = eventProcessingService.parseXmlEvent(eventContent, deviceIp, devicePort);
            } else {
                return ApiResponse.error("不支持的内容类型: " + contentType);
            }
            
            return ApiResponse.success("解析成功", eventData);
            
        } catch (Exception e) {
            log.error("手动解析事件数据异常", e);
            return ApiResponse.error("解析事件数据异常: " + e.getMessage());
        }
    }
    
    /**
     * 判断是否为文本内容
     */
    private boolean isTextContent(String contentType) {
        if (contentType == null) {
            return false;
        }
        return contentType.contains("json") || 
               contentType.contains("xml") || 
               contentType.contains("text");
    }
}
