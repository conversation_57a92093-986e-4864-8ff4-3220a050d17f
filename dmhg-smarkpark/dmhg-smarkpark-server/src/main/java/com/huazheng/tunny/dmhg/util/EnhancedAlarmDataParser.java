package com.huazheng.tunny.dmhg.util;


import com.huazheng.tunny.dmhg.api.dto.EventData;
import com.huazheng.tunny.dmhg.enums.ContentTypeEnum;
import com.huazheng.tunny.dmhg.service.EventProcessingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 增强版报警数据解析器
 * 集成了事件处理服务，提供更完善的事件解析和处理功能
 * 
 * <AUTHOR>
 * @date 2024/01/01
 */
@Slf4j
@Component
public class EnhancedAlarmDataParser {
    
    @Autowired
    private EventProcessingService eventProcessingService;
    
    /**
     * 解析报警事件信息（增强版）
     * 
     * @param contentType 内容类型
     * @param storeFolderPath 存储文件夹路径
     * @param alarmInfo 报警信息（JSON/XML字符串）
     * @param byteArray 二进制数据（图片/视频）
     * @param deviceIp 设备IP
     * @param devicePort 设备端口
     * @return 解析后的事件数据
     */
    public EventData parseAlarmInfoEnhanced(int contentType, String storeFolderPath,
                                            String alarmInfo, byte[] byteArray,
                                            String deviceIp, String devicePort) {
        
        log.info("开始解析事件数据 - 设备: {}:{}, 内容类型: {}", deviceIp, devicePort, contentType);
        
        EventData eventData = null;
        
        try {
            // 使用事件处理服务解析数据
            eventData = eventProcessingService.processEvent(contentType, alarmInfo, byteArray, deviceIp, devicePort);
            
            // 保存原始数据到文件（保持原有功能）
            saveRawDataToFile(contentType, storeFolderPath, alarmInfo, byteArray, eventData);
            
            log.info("事件数据解析完成 - 事件类型: {}, 设备: {}:{}", 
                    eventData.getEventType(), deviceIp, devicePort);
            
        } catch (Exception e) {
            log.error("解析事件数据时发生异常 - 设备: {}:{}", deviceIp, devicePort, e);
        }
        
        return eventData;
    }
    
    /**
     * 保存原始数据到文件
     */
    private void saveRawDataToFile(int contentType, String storeFolderPath, 
                                  String alarmInfo, byte[] byteArray, EventData eventData) {
        String curTimeStampStr = TimeFormatUtil.curTimeFormat();
        
        try {
            // 保存文本类型数据
            if (alarmInfo != null && !alarmInfo.trim().isEmpty()) {
                String eventType = eventData != null ? eventData.getEventType() : "unknown";
                String filename = curTimeStampStr + "_eventType_" + eventType;
                String fileExtension = ContentTypeEnum.getFilePostfix(contentType);
                
                // 这里可以调用原有的FileUtil保存文件
                // FileUtil.output2File(storeFolderPath, filename, fileExtension, alarmInfo);
                log.debug("保存文本数据到文件: {}{}{}", storeFolderPath, filename, fileExtension);
            }
            
            // 保存二进制数据
            if (byteArray != null && byteArray.length > 0) {
                String fileExtension = ContentTypeEnum.getFilePostfix(contentType);
                // FileUtil.byteAry2File(storeFolderPath, curTimeStampStr, fileExtension, byteArray);
                log.debug("保存二进制数据到文件: {}{}{}", storeFolderPath, curTimeStampStr, fileExtension);
            }
            
        } catch (Exception e) {
            log.error("保存原始数据到文件时发生异常", e);
        }
    }
    
    /**
     * 快速解析JSON事件
     */
    public EventData parseJsonEvent(String jsonContent, String deviceIp, String devicePort) {
        return eventProcessingService.parseJsonEvent(jsonContent, deviceIp, devicePort);
    }
    
    /**
     * 快速解析XML事件
     */
    public EventData parseXmlEvent(String xmlContent, String deviceIp, String devicePort) {
        return eventProcessingService.parseXmlEvent(xmlContent, deviceIp, devicePort);
    }
    
    /**
     * 处理二进制数据
     */
    public String processBinaryData(byte[] binaryData, int contentType, String deviceIp, String devicePort) {
        return eventProcessingService.processBinaryData(binaryData, contentType, deviceIp, devicePort);
    }
}
