package com.huazheng.tunny.dmhg.service.impl;


import com.huazheng.tunny.dmhg.api.dto.DeviceInfoDTO;
import com.huazheng.tunny.dmhg.api.dto.DeviceLinkStatusDTO;
import com.huazheng.tunny.dmhg.listen.AlarmGuardDemo;
import com.huazheng.tunny.dmhg.service.EventSubscriptionService;
import com.huazheng.tunny.dmhg.util.LongLinkThread;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 事件订阅服务实现类
 * 
 * <AUTHOR>
 * @date 2024/01/01
 */
@Slf4j
@Service
public class EventSubscriptionServiceImpl implements EventSubscriptionService {
    
    @Override
    public boolean subscribeEvent(DeviceInfoDTO deviceInfo, String eventType, String minorEvent) {
        try {
            log.info("开始订阅设备事件 - 设备: {}:{}, 事件类型: {}, 子事件: {}", 
                    deviceInfo.getDevIp(), deviceInfo.getDevPort(), eventType, minorEvent);
            
            // 先停止现有订阅
            unsubscribeEvent(deviceInfo);
            
            // 创建设备连接状态
            DeviceLinkStatusDTO status = new DeviceLinkStatusDTO();
            status.stopLink = new AtomicBoolean(false);
            status.dataRecv = new AtomicBoolean(false);
            AlarmGuardDemo.devLinkStatusMap.put(deviceInfo, status);
            
            // 启动订阅线程
            Thread subscribeThread = new Thread(new LongLinkThread(deviceInfo, true, eventType));
            subscribeThread.setName("Subscribe-" + deviceInfo.getDevIp() + "-" + deviceInfo.getDevPort());
            subscribeThread.start();
            
            log.info("设备事件订阅启动成功 - 设备: {}:{}", deviceInfo.getDevIp(), deviceInfo.getDevPort());
            return true;
            
        } catch (Exception e) {
            log.error("订阅设备事件失败 - 设备: {}:{}, 错误: {}", 
                    deviceInfo.getDevIp(), deviceInfo.getDevPort(), e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean subscribeAllEvents(DeviceInfoDTO deviceInfo) {
        try {
            log.info("开始订阅设备所有事件 - 设备: {}:{}", deviceInfo.getDevIp(), deviceInfo.getDevPort());
            
            // 先停止现有订阅
            unsubscribeEvent(deviceInfo);
            
            // 创建设备连接状态
            DeviceLinkStatusDTO status = new DeviceLinkStatusDTO();
            status.stopLink = new AtomicBoolean(false);
            status.dataRecv = new AtomicBoolean(false);
            AlarmGuardDemo.devLinkStatusMap.put(deviceInfo, status);
            
            // 启动订阅所有事件的线程
            Thread subscribeThread = new Thread(new LongLinkThread(deviceInfo, true, "all"));
            subscribeThread.setName("SubscribeAll-" + deviceInfo.getDevIp() + "-" + deviceInfo.getDevPort());
            subscribeThread.start();
            
            log.info("设备所有事件订阅启动成功 - 设备: {}:{}", deviceInfo.getDevIp(), deviceInfo.getDevPort());
            return true;
            
        } catch (Exception e) {
            log.error("订阅设备所有事件失败 - 设备: {}:{}, 错误: {}", 
                    deviceInfo.getDevIp(), deviceInfo.getDevPort(), e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean unsubscribeEvent(DeviceInfoDTO deviceInfo) {
        try {
            log.info("开始取消订阅设备事件 - 设备: {}:{}", deviceInfo.getDevIp(), deviceInfo.getDevPort());
            
            DeviceLinkStatusDTO devLongLinkStatus = AlarmGuardDemo.devLinkStatusMap.get(deviceInfo);
            if (devLongLinkStatus == null) {
                log.info("设备未订阅任何事件 - 设备: {}:{}", deviceInfo.getDevIp(), deviceInfo.getDevPort());
                return true;
            }
            
            // 设置停止标志
            devLongLinkStatus.stopLink.set(true);
            devLongLinkStatus.dataRecv.set(false);
            AlarmGuardDemo.devLinkStatusMap.put(deviceInfo, devLongLinkStatus);
            
            // 等待一段时间确保线程停止
            Thread.sleep(1000);
            
            // 移除设备状态
            AlarmGuardDemo.devLinkStatusMap.remove(deviceInfo);
            
            log.info("设备事件订阅取消成功 - 设备: {}:{}", deviceInfo.getDevIp(), deviceInfo.getDevPort());
            return true;
            
        } catch (Exception e) {
            log.error("取消订阅设备事件失败 - 设备: {}:{}, 错误: {}", 
                    deviceInfo.getDevIp(), deviceInfo.getDevPort(), e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean isSubscribed(DeviceInfoDTO deviceInfo) {
        DeviceLinkStatusDTO devLongLinkStatus = AlarmGuardDemo.devLinkStatusMap.get(deviceInfo);
        if (devLongLinkStatus == null) {
            return false;
        }
        return !devLongLinkStatus.stopLink.get();
    }
    
    @Override
    public String getSubscriptionStatus(DeviceInfoDTO deviceInfo) {
        DeviceLinkStatusDTO devLongLinkStatus = AlarmGuardDemo.devLinkStatusMap.get(deviceInfo);
        if (devLongLinkStatus == null) {
            return "未订阅";
        }
        
        boolean isActive = !devLongLinkStatus.stopLink.get();
        boolean isReceiving = devLongLinkStatus.dataRecv.get();
        
        if (isActive && isReceiving) {
            return "订阅中且正在接收数据";
        } else if (isActive) {
            return "订阅中但暂无数据";
        } else {
            return "订阅已停止";
        }
    }
}
