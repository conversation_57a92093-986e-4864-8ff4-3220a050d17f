package com.huazheng.tunny.dmhg.listen;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;

/**
 * Multipart数据解析器
 * 用于解析海康设备推送的multipart格式数据
 * 
 * <AUTHOR>
 * @date 2024/01/01
 */
@Slf4j
public class MultipartDataParser {
    
    private static final String BOUNDARY_PREFIX = "boundary=";
    private static final String CONTENT_TYPE = "Content-Type: ";
    private static final String CONTENT_LENGTH = "Content-Length: ";
    private static final String CONTENT_ID = "Content-ID: ";
    private static final String LINE_SEPARATOR = "\r\n";
    private static final String HEADER_SEPARATOR = "\r\n\r\n";
    
    private final byte[] rawData;
    private final List<Part> parts = new ArrayList<>();
    private String boundary;
    
    public MultipartDataParser(byte[] rawData) {
        this.rawData = rawData;
    }
    
    /**
     * 解析multipart数据
     */
    public void parse() throws Exception {
        // 1. 提取boundary
        extractBoundary();
        
        if (boundary == null) {
            throw new Exception("无法找到boundary标记");
        }
        
        // 2. 分割数据部分
        splitParts();
        
        log.info("Multipart数据解析完成 - boundary: {}, 部分数量: {}", boundary, parts.size());
    }
    
    /**
     * 提取boundary标记
     */
    private void extractBoundary() throws UnsupportedEncodingException {
        String headerData = new String(rawData, 0, Math.min(1024, rawData.length), "UTF-8");
        
        int boundaryIndex = headerData.indexOf(BOUNDARY_PREFIX);
        if (boundaryIndex != -1) {
            int startIndex = boundaryIndex + BOUNDARY_PREFIX.length();
            int endIndex = headerData.indexOf(LINE_SEPARATOR, startIndex);
            if (endIndex != -1) {
                boundary = headerData.substring(startIndex, endIndex).trim();
            }
        }
    }
    
    /**
     * 分割数据部分
     */
    private void splitParts() throws Exception {
        String boundaryMark = "--" + boundary;
        String endMark = "--" + boundary + "--";
        
        // 查找所有boundary位置
        List<Integer> boundaryPositions = findBoundaryPositions(boundaryMark);
        
        // 解析每个部分
        for (int i = 0; i < boundaryPositions.size() - 1; i++) {
            int startPos = boundaryPositions.get(i);
            int endPos = boundaryPositions.get(i + 1);
            
            Part part = parsePart(startPos, endPos);
            if (part != null) {
                parts.add(part);
            }
        }
    }
    
    /**
     * 查找boundary位置
     */
    private List<Integer> findBoundaryPositions(String boundaryMark) throws UnsupportedEncodingException {
        List<Integer> positions = new ArrayList<>();
        byte[] boundaryBytes = boundaryMark.getBytes("UTF-8");
        
        for (int i = 0; i <= rawData.length - boundaryBytes.length; i++) {
            if (matchesAt(rawData, i, boundaryBytes)) {
                positions.add(i);
            }
        }
        
        return positions;
    }
    
    /**
     * 检查指定位置是否匹配
     */
    private boolean matchesAt(byte[] data, int offset, byte[] pattern) {
        for (int i = 0; i < pattern.length; i++) {
            if (data[offset + i] != pattern[i]) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 解析单个部分
     */
    private Part parsePart(int startPos, int endPos) throws Exception {
        // 跳过boundary行
        int dataStart = findLineEnd(startPos) + LINE_SEPARATOR.length();
        
        // 查找header和body的分隔符
        int headerEnd = findHeaderEnd(dataStart, endPos);
        if (headerEnd == -1) {
            return null;
        }
        
        // 解析header
        String headerData = new String(rawData, dataStart, headerEnd - dataStart, "UTF-8");
        Part part = parseHeader(headerData);
        
        // 提取body数据
        int bodyStart = headerEnd + HEADER_SEPARATOR.length();
        int bodyLength = endPos - bodyStart - LINE_SEPARATOR.length();
        
        if (bodyLength > 0) {
            byte[] bodyData = new byte[bodyLength];
            System.arraycopy(rawData, bodyStart, bodyData, 0, bodyLength);
            part.setData(bodyData);
        }
        
        return part;
    }
    
    /**
     * 查找行结束位置
     */
    private int findLineEnd(int startPos) throws UnsupportedEncodingException {
        byte[] separator = LINE_SEPARATOR.getBytes("UTF-8");
        
        for (int i = startPos; i <= rawData.length - separator.length; i++) {
            if (matchesAt(rawData, i, separator)) {
                return i;
            }
        }
        return rawData.length;
    }
    
    /**
     * 查找header结束位置
     */
    private int findHeaderEnd(int startPos, int endPos) throws UnsupportedEncodingException {
        byte[] separator = HEADER_SEPARATOR.getBytes("UTF-8");
        
        for (int i = startPos; i <= endPos - separator.length; i++) {
            if (matchesAt(rawData, i, separator)) {
                return i;
            }
        }
        return -1;
    }
    
    /**
     * 解析header信息
     */
    private Part parseHeader(String headerData) {
        Part part = new Part();
        
        String[] lines = headerData.split(LINE_SEPARATOR);
        for (String line : lines) {
            if (line.startsWith(CONTENT_TYPE)) {
                part.setContentType(line.substring(CONTENT_TYPE.length()).trim());
            } else if (line.startsWith(CONTENT_LENGTH)) {
                try {
                    part.setContentLength(Integer.parseInt(line.substring(CONTENT_LENGTH.length()).trim()));
                } catch (NumberFormatException e) {
                    log.warn("解析Content-Length失败: {}", line);
                }
            } else if (line.startsWith(CONTENT_ID)) {
                part.setContentId(line.substring(CONTENT_ID.length()).trim());
            }
        }
        
        return part;
    }
    
    /**
     * 获取解析结果
     */
    public List<Part> getParts() {
        return parts;
    }
    
    /**
     * 数据部分类
     */
    @Data
    public static class Part {
        private String contentType;
        private int contentLength;
        private String contentId;
        private byte[] data;
        
        /**
         * 判断是否为文本内容
         */
        public boolean isTextContent() {
            if (contentType == null) {
                return false;
            }
            return contentType.contains("json") || 
                   contentType.contains("xml") || 
                   contentType.contains("text");
        }
        
        /**
         * 获取文本内容
         */
        public String getTextContent() throws UnsupportedEncodingException {
            if (data == null) {
                return null;
            }
            return new String(data, "UTF-8");
        }
    }
}
