package com.huazheng.tunny.dmhg;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

@EnableDiscoveryClient(autoRegister = false)
//@EnableDiscoveryClient
@SpringBootApplication
@EnableFeignClients({"com.huazheng.tunny.dmhg.api.feign"})
@MapperScan("com.huazheng.tunny.dmhg.mapper")
public class DmhgSmarkParkApplication {

	public static void main(String[] args) {
		SpringApplication.run(DmhgSmarkParkApplication.class, args);
	}
}
