package com.huazheng.tunny.dmhg.controller;

import com.huazheng.tunny.dmhg.api.dto.ApiResponse;
import com.huazheng.tunny.dmhg.service.EnhancedListenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 增强版监听控制器
 * 提供事件监听的管理接口
 * 
 * <AUTHOR>
 * @date 2024/01/01
 */
@Slf4j
@RestController
@RequestMapping("/api/listen")
public class EnhancedListenController {
    
    @Autowired
    private EnhancedListenService enhancedListenService;
    
    /**
     * 启动事件监听
     * 
     * @param port 监听端口（可选，默认9999）
     * @return 启动结果
     */
    @PostMapping("/start")
    public ApiResponse<Map<String, Object>> startListen(@RequestParam(defaultValue = "10000") int port) {
        try {
            log.info("接收到启动监听请求 - 端口: {}", port);
            
            boolean success = enhancedListenService.startListen(port);
            
            Map<String, Object> result = new HashMap<>();
            result.put("listening", success);
            result.put("port", port);
            result.put("status", enhancedListenService.getListenStatus());
            
            if (success) {
                return ApiResponse.success("事件监听启动成功", result);
            } else {
                return ApiResponse.error("事件监听启动失败");
            }
            
        } catch (Exception e) {
            log.error("启动事件监听异常", e);
            return ApiResponse.error("启动事件监听异常: " + e.getMessage());
        }
    }
    
    /**
     * 停止事件监听
     * 
     * @return 停止结果
     */
    @PostMapping("/stop")
    public ApiResponse<Map<String, Object>> stopListen() {
        try {
            log.info("接收到停止监听请求");
            
            boolean success = enhancedListenService.stopListen();
            
            Map<String, Object> result = new HashMap<>();
            result.put("listening", false);
            result.put("status", enhancedListenService.getListenStatus());
            
            if (success) {
                return ApiResponse.success("事件监听停止成功", result);
            } else {
                return ApiResponse.error("事件监听停止失败");
            }
            
        } catch (Exception e) {
            log.error("停止事件监听异常", e);
            return ApiResponse.error("停止事件监听异常: " + e.getMessage());
        }
    }
    
    /**
     * 查询监听状态
     * 
     * @return 监听状态
     */
    @GetMapping("/status")
    public ApiResponse<Map<String, Object>> getListenStatus() {
        try {
            boolean isListening = enhancedListenService.isListening();
            String status = enhancedListenService.getListenStatus();
            
            Map<String, Object> result = new HashMap<>();
            result.put("listening", isListening);
            result.put("status", status);
            result.put("port", 9999); // 默认端口
            
            return ApiResponse.success("查询成功", result);
            
        } catch (Exception e) {
            log.error("查询监听状态异常", e);
            return ApiResponse.error("查询监听状态异常: " + e.getMessage());
        }
    }
}
