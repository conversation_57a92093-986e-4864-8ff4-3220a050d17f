package com.huazheng.tunny.dmhg.api.dto;

import lombok.Data;

import java.util.Map;

/**
 * 事件数据DTO
 * 
 * <AUTHOR>
 * @date 2024/01/01
 */
@Data
public class EventData {
    
    /**
     * 事件类型
     */
    private String eventType;
    
    /**
     * 事件时间
     */
    private String dateTime;
    
    /**
     * 设备IP
     */
    private String deviceIp;
    
    /**
     * 设备端口
     */
    private String devicePort;
    
    /**
     * 通道ID
     */
    private String channelID;
    
    /**
     * 事件状态 (active/inactive)
     */
    private String activePostCount;
    
    /**
     * 事件详细信息
     */
    private Map<String, Object> eventDetails;
    
    /**
     * 原始JSON数据
     */
    private String rawJsonData;
    
    /**
     * 原始XML数据
     */
    private String rawXmlData;
    
    /**
     * 图片数据（Base64编码）
     */
    private String imageData;
    
    /**
     * 图片文件路径
     */
    private String imagePath;
    
    /**
     * 视频数据路径
     */
    private String videoPath;
    
    /**
     * 接收时间戳
     */
    private Long receiveTimestamp;
    
    /**
     * 数据类型 (JSON/XML/IMAGE/VIDEO)
     */
    private String dataType;
    
    public EventData() {
        this.receiveTimestamp = System.currentTimeMillis();
    }
}
