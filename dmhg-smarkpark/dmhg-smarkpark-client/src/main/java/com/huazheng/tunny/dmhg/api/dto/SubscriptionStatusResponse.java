package com.huazheng.tunny.dmhg.api.dto;

import lombok.Data;

/**
 * 订阅状态响应DTO
 * 
 * <AUTHOR>
 * @date 2024/01/01
 */
@Data
public class SubscriptionStatusResponse {
    
    /**
     * 设备IP地址
     */
    private String devIp;
    
    /**
     * 设备端口
     */
    private String devPort;
    
    /**
     * 是否已订阅
     */
    private Boolean subscribed;
    
    /**
     * 订阅状态描述
     */
    private String status;
    
    /**
     * 订阅时间
     */
    private Long subscribeTime;
    
    public SubscriptionStatusResponse() {
    }
    
    public SubscriptionStatusResponse(String devIp, String devPort, Boolean subscribed, String status) {
        this.devIp = devIp;
        this.devPort = devPort;
        this.subscribed = subscribed;
        this.status = status;
        this.subscribeTime = System.currentTimeMillis();
    }
}
