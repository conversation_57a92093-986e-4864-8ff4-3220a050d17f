package com.huazheng.tunny.dmhg.api.dto;

import lombok.Data;


/**
 * 订阅事件请求DTO
 * 
 * <AUTHOR>
 * @date 2024/01/01
 */
@Data
public class SubscribeEventRequest {
    
    /**
     * 设备IP地址
     */

    private String devIp;
    
    /**
     * 设备端口
     */
    private String devPort;
    
    /**
     * 设备用户名
     */
    private String username;
    
    /**
     * 设备密码
     */
    private String password;
    
    /**
     * HTTP类型 (0: HTTP, 1: HTTPS)
     */
    private Integer httpType = 0;
    
    /**
     * 事件类型  fielddetection:区域入侵;
     * 常见类型：
     * - IDCardInfoEvent: 身份证事件
     * - FaceDetectionEvent: 人脸检测事件
     * - VehicleDetectionEvent: 车辆检测事件
     * - all: 所有事件
     */
    private String eventType = "IDCardInfoEvent";
    
    /**
     * 子事件类型
     * 例如: 0x69,0x70,0x71
     */
    private String minorEvent = "0x69,0x70,0x71";
    
    /**
     * 是否订阅所有事件
     */
    private Boolean subscribeAll = false;
}
